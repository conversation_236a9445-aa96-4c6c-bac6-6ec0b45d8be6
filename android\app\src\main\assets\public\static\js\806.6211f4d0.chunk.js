/*! For license information please see 806.6211f4d0.chunk.js.LICENSE.txt */
"use strict";(globalThis.webpackChunkalphabet_game=globalThis.webpackChunkalphabet_game||[]).push([[806],{806:(t,e,r)=>{r.r(e),r.d(e,{createSwipeBackGesture:()=>S});var s=r(384),i=r(406);class l{constructor(t,e,r,s,i){this.id=e,this.name=r,this.disableScroll=i,this.priority=1e6*s+e,this.ctrl=t}canStart(){return!!this.ctrl&&this.ctrl.canStart(this.name)}start(){return!!this.ctrl&&this.ctrl.start(this.name,this.id,this.priority)}capture(){if(!this.ctrl)return!1;const t=this.ctrl.capture(this.name,this.id,this.priority);return t&&this.disableScroll&&this.ctrl.disableScroll(this.id),t}release(){this.ctrl&&(this.ctrl.release(this.id),this.disableScroll&&this.ctrl.enableScroll(this.id))}destroy(){this.release(),this.ctrl=void 0}}class a{constructor(t,e,r,s){this.id=e,this.disable=r,this.disableScroll=s,this.ctrl=t}block(){if(this.ctrl){if(this.disable)for(const t of this.disable)this.ctrl.disableGesture(t,this.id);this.disableScroll&&this.ctrl.disableScroll(this.id)}}unblock(){if(this.ctrl){if(this.disable)for(const t of this.disable)this.ctrl.enableGesture(t,this.id);this.disableScroll&&this.ctrl.enableScroll(this.id)}}destroy(){this.unblock(),this.ctrl=void 0}}const n="backdrop-no-scroll",o=new class{constructor(){this.gestureId=0,this.requestedStart=new Map,this.disabledGestures=new Map,this.disabledScroll=new Set}createGesture(t){var e;return new l(this,this.newID(),t.name,null!==(e=t.priority)&&void 0!==e?e:0,!!t.disableScroll)}createBlocker(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new a(this,this.newID(),t.disable,!!t.disableScroll)}start(t,e,r){return this.canStart(t)?(this.requestedStart.set(e,r),!0):(this.requestedStart.delete(e),!1)}capture(t,e,r){if(!this.start(t,e,r))return!1;const s=this.requestedStart;let i=-1e4;if(s.forEach(t=>{i=Math.max(i,t)}),i===r){this.capturedId=e,s.clear();const r=new CustomEvent("ionGestureCaptured",{detail:{gestureName:t}});return document.dispatchEvent(r),!0}return s.delete(e),!1}release(t){this.requestedStart.delete(t),this.capturedId===t&&(this.capturedId=void 0)}disableGesture(t,e){let r=this.disabledGestures.get(t);void 0===r&&(r=new Set,this.disabledGestures.set(t,r)),r.add(e)}enableGesture(t,e){const r=this.disabledGestures.get(t);void 0!==r&&r.delete(e)}disableScroll(t){this.disabledScroll.add(t),1===this.disabledScroll.size&&document.body.classList.add(n)}enableScroll(t){this.disabledScroll.delete(t),0===this.disabledScroll.size&&document.body.classList.remove(n)}canStart(t){return void 0===this.capturedId&&!this.isDisabled(t)}isCaptured(){return void 0!==this.capturedId}isScrollDisabled(){return this.disabledScroll.size>0}isDisabled(t){const e=this.disabledGestures.get(t);return!!(e&&e.size>0)}newID(){return this.gestureId++,this.gestureId}},c=(t,e,r,s)=>{const i=d(t)?{capture:!!s.capture,passive:!!s.passive}:!!s.capture;let l,a;return t.__zone_symbol__addEventListener?(l="__zone_symbol__addEventListener",a="__zone_symbol__removeEventListener"):(l="addEventListener",a="removeEventListener"),t[l](e,r,i),()=>{t[a](e,r,i)}},d=t=>{if(void 0===u)try{const e=Object.defineProperty({},"passive",{get:()=>{u=!0}});t.addEventListener("optsTest",()=>{},e)}catch(e){u=!1}return!!u};let u;const h=t=>t instanceof Document?t:t.ownerDocument,b=t=>{let e=!1,r=!1,s=!0,i=!1;const l=Object.assign({disableScroll:!1,direction:"x",gesturePriority:0,passive:!0,maxAngle:40,threshold:10},t),a=l.canStart,n=l.onWillStart,d=l.onStart,u=l.onEnd,b=l.notCaptured,S=l.onMove,y=l.threshold,g=l.passive,f=l.blurOnStart,X={type:"pan",startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,event:void 0,data:void 0},w=((t,e,r)=>{const s=r*(Math.PI/180),i="x"===t,l=Math.cos(s),a=e*e;let n=0,o=0,c=!1,d=0;return{start(t,e){n=t,o=e,d=0,c=!0},detect(t,e){if(!c)return!1;const r=t-n,s=e-o,u=r*r+s*s;if(u<a)return!1;const h=Math.sqrt(u),b=(i?r:s)/h;return d=b>l?1:b<-l?-1:0,c=!1,!0},isGesture:()=>0!==d,getDirection:()=>d}})(l.direction,l.threshold,l.maxAngle),Y=o.createGesture({name:t.gestureName,priority:t.gesturePriority,disableScroll:t.disableScroll}),_=()=>{e&&(i=!1,S&&S(X))},G=()=>!!Y.capture()&&(e=!0,s=!1,X.startX=X.currentX,X.startY=X.currentY,X.startTime=X.currentTime,n?n(X).then(D):D(),!0),D=()=>{f&&(()=>{if("undefined"!==typeof document){const t=document.activeElement;(null===t||void 0===t?void 0:t.blur)&&t.blur()}})(),d&&d(X),s=!0},E=()=>{e=!1,r=!1,i=!1,s=!0,Y.release()},I=t=>{const r=e,i=s;E(),i&&(v(X,t),r?u&&u(X):b&&b(X))},T=((t,e,r,s,i)=>{let l,a,n,o,d,u,b,v=0;const p=s=>{v=Date.now()+2e3,e(s)&&(!a&&r&&(a=c(t,"touchmove",r,i)),n||(n=c(s.target,"touchend",S,i)),o||(o=c(s.target,"touchcancel",S,i)))},m=s=>{v>Date.now()||e(s)&&(!u&&r&&(u=c(h(t),"mousemove",r,i)),b||(b=c(h(t),"mouseup",y,i)))},S=t=>{g(),s&&s(t)},y=t=>{f(),s&&s(t)},g=()=>{a&&a(),n&&n(),o&&o(),a=n=o=void 0},f=()=>{u&&u(),b&&b(),u=b=void 0},X=()=>{g(),f()},w=function(){arguments.length>0&&void 0!==arguments[0]&&!arguments[0]?(l&&l(),d&&d(),l=d=void 0,X()):(l||(l=c(t,"touchstart",p,i)),d||(d=c(t,"mousedown",m,i)))};return{enable:w,stop:X,destroy:()=>{w(!1),s=r=e=void 0}}})(l.el,t=>{const e=m(t);return!(r||!s)&&(p(t,X),X.startX=X.currentX,X.startY=X.currentY,X.startTime=X.currentTime=e,X.velocityX=X.velocityY=X.deltaX=X.deltaY=0,X.event=t,(!a||!1!==a(X))&&(Y.release(),!!Y.start()&&(r=!0,0===y?G():(w.start(X.startX,X.startY),!0))))},t=>{e?!i&&s&&(i=!0,v(X,t),requestAnimationFrame(_)):(v(X,t),w.detect(X.currentX,X.currentY)&&(w.isGesture()&&G()||k()))},I,{capture:!1,passive:g}),k=()=>{E(),T.stop(),b&&b(X)};return{enable(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];t||(e&&I(void 0),E()),T.enable(t)},destroy(){Y.destroy(),T.destroy()}}},v=(t,e)=>{if(!e)return;const r=t.currentX,s=t.currentY,i=t.currentTime;p(e,t);const l=t.currentX,a=t.currentY,n=(t.currentTime=m(e))-i;if(n>0&&n<100){const e=(l-r)/n,i=(a-s)/n;t.velocityX=.7*e+.3*t.velocityX,t.velocityY=.7*i+.3*t.velocityY}t.deltaX=l-t.startX,t.deltaY=a-t.startY,t.event=e},p=(t,e)=>{let r=0,s=0;if(t){const e=t.changedTouches;if(e&&e.length>0){const t=e[0];r=t.clientX,s=t.clientY}else void 0!==t.pageX&&(r=t.pageX,s=t.pageY)}e.currentX=r,e.currentY=s},m=t=>t.timeStamp||Date.now(),S=(t,e,r,l,a)=>{const n=t.ownerDocument.defaultView;let o=(0,i.i)(t);const c=t=>o?-t.deltaX:t.deltaX;return b({el:t,gestureName:"goback-swipe",gesturePriority:101,threshold:10,canStart:r=>(o=(0,i.i)(t),(t=>{const{startX:e}=t;return o?e>=n.innerWidth-50:e<=50})(r)&&e()),onStart:r,onMove:t=>{const e=c(t)/n.innerWidth;l(e)},onEnd:t=>{const e=c(t),r=n.innerWidth,i=e/r,l=(t=>o?-t.velocityX:t.velocityX)(t),d=l>=0&&(l>.2||e>r/2),u=(d?1-i:i)*r;let h=0;if(u>5){const t=u/Math.abs(l);h=Math.min(t,540)}a(d,i<=0?.01:(0,s.m)(0,i,.9999),h)}})}}}]);
//# sourceMappingURL=806.6211f4d0.chunk.js.map