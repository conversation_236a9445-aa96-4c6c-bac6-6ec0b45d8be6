/*! For license information please see 923.eef5654c.chunk.js.LICENSE.txt */
"use strict";(globalThis.webpackChunkalphabet_game=globalThis.webpackChunkalphabet_game||[]).push([[923],{923:(t,e,o)=>{o.r(e),o.d(e,{iosTransitionAnimation:()=>f,shadow:()=>l});var n=o(507),r=o(903);const a=t=>document.querySelector(`${t}.ion-cloned-element`),l=t=>t.shadowRoot||t,s=t=>{const e="ION-TABS"===t.tagName?t:t.querySelector("ion-tabs"),o="ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large";if(null!=e){const t=e.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");return null!=t?t.querySelector(o):null}return t.querySelector(o)},i=(t,e)=>{const o="ION-TABS"===t.tagName?t:t.querySelector("ion-tabs");let n=[];if(null!=o){const t=o.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");null!=t&&(n=t.querySelectorAll("ion-buttons"))}else n=t.querySelectorAll("ion-buttons");for(const r of n){const t=r.closest("ion-header"),o=t&&!t.classList.contains("header-collapse-condense-inactive"),n=r.querySelector("ion-back-button"),a=r.classList.contains("buttons-collapse"),l="start"===r.slot||""===r.slot;if(null!==n&&l&&(a&&o&&e||!a))return n}return null},c=(t,e,o,r,s,i,c,d,f)=>{var y,u;const p=e?`calc(100% - ${s.right+4}px)`:s.left-4+"px",b=e?"right":"left",g=e?"left":"right",h=e?"right":"left",S=(null===(y=i.textContent)||void 0===y?void 0:y.trim())===(null===(u=d.textContent)||void 0===u?void 0:u.trim()),$=f.width/c.width,T=(f.height-m)/c.height,q=S?`scale(${$}, ${T})`:`scale(${T})`,x="scale(1)",X=l(r).querySelector("ion-icon").getBoundingClientRect(),v=e?X.width/2-(X.right-s.right)+"px":s.left-X.width/2+"px",E=e?`-${window.innerWidth-s.right}px`:`${s.left}px`,A=`${f.top}px`,C=`${s.top}px`,w=o?[{offset:0,transform:`translate3d(${E}, ${C}, 0)`},{offset:1,transform:`translate3d(${v}, ${A}, 0)`}]:[{offset:0,transform:`translate3d(${v}, ${A}, 0)`},{offset:1,transform:`translate3d(${E}, ${C}, 0)`}],k=o?[{offset:0,opacity:1,transform:x},{offset:1,opacity:0,transform:q}]:[{offset:0,opacity:0,transform:q},{offset:1,opacity:1,transform:x}],B=o?[{offset:0,opacity:1,transform:"scale(1)"},{offset:.2,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:0,transform:"scale(0.6)"}]:[{offset:0,opacity:0,transform:"scale(0.6)"},{offset:.6,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:1,transform:"scale(1)"}],P=(0,n.c)(),R=(0,n.c)(),L=(0,n.c)(),W=a("ion-back-button"),N=l(W).querySelector(".button-text"),z=l(W).querySelector("ion-icon");W.text=r.text,W.mode=r.mode,W.icon=r.icon,W.color=r.color,W.disabled=r.disabled,W.style.setProperty("display","block"),W.style.setProperty("position","fixed"),R.addElement(z),P.addElement(N),L.addElement(W),L.beforeStyles({position:"absolute",top:"0px",[h]:"0px"}).keyframes(w),P.beforeStyles({"transform-origin":`${b} top`}).beforeAddWrite(()=>{r.style.setProperty("display","none"),W.style.setProperty(b,p)}).afterAddWrite(()=>{r.style.setProperty("display",""),W.style.setProperty("display","none"),W.style.removeProperty(b)}).keyframes(k),R.beforeStyles({"transform-origin":`${g} center`}).keyframes(B),t.addAnimation([P,R,L])},d=(t,e,o,r,l,s,i,c)=>{var d,f;const y=e?"right":"left",u=e?`calc(100% - ${l.right}px)`:`${l.left}px`,p=`${l.top}px`,b=e?`-${window.innerWidth-c.right-8}px`:c.x-8+"px",g=c.y-2+"px",h=(null===(d=i.textContent)||void 0===d?void 0:d.trim())===(null===(f=r.textContent)||void 0===f?void 0:f.trim()),S=c.width/s.width,$=c.height/(s.height-m),T="scale(1)",q=h?`scale(${S}, ${$})`:`scale(${$})`,x=o?[{offset:0,opacity:0,transform:`translate3d(${b}, ${g}, 0) ${q}`},{offset:.1,opacity:0},{offset:1,opacity:1,transform:`translate3d(0px, ${p}, 0) ${T}`}]:[{offset:0,opacity:.99,transform:`translate3d(0px, ${p}, 0) ${T}`},{offset:.6,opacity:0},{offset:1,opacity:0,transform:`translate3d(${b}, ${g}, 0) ${q}`}],X=a("ion-title"),v=(0,n.c)();X.innerText=r.innerText,X.size=r.size,X.color=r.color,v.addElement(X),v.beforeStyles({"transform-origin":`${y} top`,height:`${l.height}px`,display:"",position:"relative",[y]:u}).beforeAddWrite(()=>{r.style.setProperty("opacity","0")}).afterAddWrite(()=>{r.style.setProperty("opacity",""),X.style.setProperty("display","none")}).keyframes(x),t.addAnimation(v)},f=(t,e)=>{var o;try{const a="cubic-bezier(0.32,0.72,0,1)",f="opacity",m="transform",y="0%",u=.8,p="rtl"===t.ownerDocument.dir,b=p?"-99.5%":"99.5%",g=p?"33%":"-33%",h=e.enteringEl,S=e.leavingEl,$="back"===e.direction,T=h.querySelector(":scope > ion-content"),q=h.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *"),x=h.querySelectorAll(":scope > ion-header > ion-toolbar"),X=(0,n.c)(),v=(0,n.c)();if(X.addElement(h).duration((null!==(o=e.duration)&&void 0!==o?o:0)||540).easing(e.easing||a).fill("both").beforeRemoveClass("ion-page-invisible"),S&&null!==t&&void 0!==t){const e=(0,n.c)();e.addElement(t),X.addAnimation(e)}if(T||0!==x.length||0!==q.length?(v.addElement(T),v.addElement(q)):v.addElement(h.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),X.addAnimation(v),$?v.beforeClearStyles([f]).fromTo("transform",`translateX(${g})`,`translateX(${y})`).fromTo(f,u,1):v.beforeClearStyles([f]).fromTo("transform",`translateX(${b})`,`translateX(${y})`),T){const t=l(T).querySelector(".transition-effect");if(t){const e=t.querySelector(".transition-cover"),o=t.querySelector(".transition-shadow"),r=(0,n.c)(),a=(0,n.c)(),l=(0,n.c)();r.addElement(t).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),a.addElement(e).beforeClearStyles([f]).fromTo(f,0,.1),l.addElement(o).beforeClearStyles([f]).fromTo(f,.03,.7),r.addAnimation([a,l]),v.addAnimation([r])}}const E=h.querySelector("ion-header.header-collapse-condense"),{forward:A,backward:C}=((t,e,o,n,r)=>{const a=i(n,o),f=s(r),m=s(n),y=i(r,o),u=null!==a&&null!==f&&!o,p=null!==m&&null!==y&&o;if(u){const n=f.getBoundingClientRect(),r=a.getBoundingClientRect(),s=l(a).querySelector(".button-text"),i=s.getBoundingClientRect(),m=l(f).querySelector(".toolbar-title").getBoundingClientRect();d(t,e,o,f,n,m,s,i),c(t,e,o,a,r,s,i,f,m)}else if(p){const n=m.getBoundingClientRect(),r=y.getBoundingClientRect(),a=l(y).querySelector(".button-text"),s=a.getBoundingClientRect(),i=l(m).querySelector(".toolbar-title").getBoundingClientRect();d(t,e,o,m,n,i,a,s),c(t,e,o,y,r,a,s,m,i)}return{forward:u,backward:p}})(X,p,$,h,S);if(x.forEach(t=>{const e=(0,n.c)();e.addElement(t),X.addAnimation(e);const o=(0,n.c)();o.addElement(t.querySelector("ion-title"));const r=(0,n.c)(),a=Array.from(t.querySelectorAll("ion-buttons,[menuToggle]")),s=t.closest("ion-header"),i=null===s||void 0===s?void 0:s.classList.contains("header-collapse-condense-inactive");let c;c=$?a.filter(t=>{const e=t.classList.contains("buttons-collapse");return e&&!i||!e}):a.filter(t=>!t.classList.contains("buttons-collapse")),r.addElement(c);const d=(0,n.c)();d.addElement(t.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])"));const m=(0,n.c)();m.addElement(l(t).querySelector(".toolbar-background"));const u=(0,n.c)(),h=t.querySelector("ion-back-button");if(h&&u.addElement(h),e.addAnimation([o,r,d,m,u]),r.fromTo(f,.01,1),d.fromTo(f,.01,1),$)i||o.fromTo("transform",`translateX(${g})`,`translateX(${y})`).fromTo(f,.01,1),d.fromTo("transform",`translateX(${g})`,`translateX(${y})`),u.fromTo(f,.01,1);else{E||o.fromTo("transform",`translateX(${b})`,`translateX(${y})`).fromTo(f,.01,1),d.fromTo("transform",`translateX(${b})`,`translateX(${y})`),m.beforeClearStyles([f,"transform"]);if((null===s||void 0===s?void 0:s.translucent)?m.fromTo("transform",p?"translateX(-100%)":"translateX(100%)","translateX(0px)"):m.fromTo(f,.01,"var(--opacity)"),A||u.fromTo(f,.01,1),h&&!A){const t=(0,n.c)();t.addElement(l(h).querySelector(".button-text")).fromTo("transform",p?"translateX(-100px)":"translateX(100px)","translateX(0px)"),e.addAnimation(t)}}}),S){const t=(0,n.c)(),e=S.querySelector(":scope > ion-content"),o=S.querySelectorAll(":scope > ion-header > ion-toolbar"),a=S.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *");if(e||0!==o.length||0!==a.length?(t.addElement(e),t.addElement(a)):t.addElement(S.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),X.addAnimation(t),$){t.beforeClearStyles([f]).fromTo("transform",`translateX(${y})`,p?"translateX(-100%)":"translateX(100%)");const e=(0,r.g)(S);X.afterAddWrite(()=>{"normal"===X.getDirection()&&e.style.setProperty("display","none")})}else t.fromTo("transform",`translateX(${y})`,`translateX(${g})`).fromTo(f,1,u);if(e){const o=l(e).querySelector(".transition-effect");if(o){const e=o.querySelector(".transition-cover"),r=o.querySelector(".transition-shadow"),a=(0,n.c)(),l=(0,n.c)(),s=(0,n.c)();a.addElement(o).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),l.addElement(e).beforeClearStyles([f]).fromTo(f,.1,0),s.addElement(r).beforeClearStyles([f]).fromTo(f,.7,.03),a.addAnimation([l,s]),t.addAnimation([a])}}o.forEach(t=>{const e=(0,n.c)();e.addElement(t);const o=(0,n.c)();o.addElement(t.querySelector("ion-title"));const r=(0,n.c)(),a=t.querySelectorAll("ion-buttons,[menuToggle]"),s=t.closest("ion-header"),i=null===s||void 0===s?void 0:s.classList.contains("header-collapse-condense-inactive"),c=Array.from(a).filter(t=>{const e=t.classList.contains("buttons-collapse");return e&&!i||!e});r.addElement(c);const d=(0,n.c)(),u=t.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])");u.length>0&&d.addElement(u);const b=(0,n.c)();b.addElement(l(t).querySelector(".toolbar-background"));const h=(0,n.c)(),S=t.querySelector("ion-back-button");if(S&&h.addElement(S),e.addAnimation([o,r,d,h,b]),X.addAnimation(e),h.fromTo(f,.99,0),r.fromTo(f,.99,0),d.fromTo(f,.99,0),$){i||o.fromTo("transform",`translateX(${y})`,p?"translateX(-100%)":"translateX(100%)").fromTo(f,.99,0),d.fromTo("transform",`translateX(${y})`,p?"translateX(-100%)":"translateX(100%)"),b.beforeClearStyles([f,"transform"]);if((null===s||void 0===s?void 0:s.translucent)?b.fromTo("transform","translateX(0px)",p?"translateX(-100%)":"translateX(100%)"):b.fromTo(f,"var(--opacity)",0),S&&!C){const t=(0,n.c)();t.addElement(l(S).querySelector(".button-text")).fromTo("transform",`translateX(${y})`,`translateX(${(p?-124:124)+"px"})`),e.addAnimation(t)}}else i||o.fromTo("transform",`translateX(${y})`,`translateX(${g})`).fromTo(f,.99,0).afterClearStyles([m,f]),d.fromTo("transform",`translateX(${y})`,`translateX(${g})`).afterClearStyles([m,f]),h.afterClearStyles([f]),o.afterClearStyles([f]),r.afterClearStyles([f])})}return X}catch(a){throw a}},m=10}}]);
//# sourceMappingURL=923.eef5654c.chunk.js.map