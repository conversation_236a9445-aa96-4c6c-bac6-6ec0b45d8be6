{"version": 3, "file": "static/js/627.3dfd551a.chunk.js", "mappings": ";0LAMA,MAAMA,EAAwBA,CAACC,EAAGC,KAC9B,IAAIC,EAAIC,EAAIC,EACZ,MAAMC,EAAa,OAEbC,EAAmC,SAAnBL,EAAKM,UACrBC,EAAaP,EAAKO,WAClBC,EAAYR,EAAKQ,UACjBC,GAAiBC,EAAAA,EAAAA,GAAkBH,GACnCI,EAAqBF,EAAeG,cAAc,eAClDC,GAAiBC,EAAAA,EAAAA,KAcvB,GAbAD,EAAeE,WAAWN,GAAgBO,KAAK,QAAQC,kBAAkB,sBAErEZ,EACAQ,EAAeK,UAAmC,QAAxBjB,EAAKD,EAAKkB,gBAA6B,IAAPjB,EAAgBA,EAAK,IAAM,KAAKkB,OAAO,oCAGjGN,EACKK,UAAmC,QAAxBhB,EAAKF,EAAKkB,gBAA6B,IAAPhB,EAAgBA,EAAK,IAAM,KACtEiB,OAAO,kCACPC,OAAO,YAAa,cAAchB,KAAe,mBACjDgB,OAAO,UAAW,IAAM,GAG7BT,EAAoB,CACpB,MAAMU,GAAkBP,EAAAA,EAAAA,KACxBO,EAAgBN,WAAWJ,GAC3BE,EAAeS,aAAaD,EAChC,CAEA,GAAIb,GAAaH,EAAe,CAE5BQ,EAAeK,UAAmC,QAAxBf,EAAKH,EAAKkB,gBAA6B,IAAPf,EAAgBA,EAAK,IAAM,KAAKgB,OAAO,oCACjG,MAAMI,GAAcT,EAAAA,EAAAA,KACpBS,EACKR,YAAWL,EAAAA,EAAAA,GAAkBF,IAC7BgB,SAAUC,IACS,IAAhBA,GAAqBF,EAAYG,SAASC,OAAS,GACnDJ,EAAYG,SAAS,GAAGE,MAAMC,YAAY,UAAW,UAGxDT,OAAO,YAAa,kBAAyB,cAAchB,MAC3DgB,OAAO,UAAW,EAAG,GAC1BP,EAAeS,aAAaC,EAChC,CACA,OAAOV,E", "sources": ["../node_modules/@ionic/core/components/md.transition.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation.js';\nimport { g as getIonPageElement } from './index2.js';\n\nconst mdTransitionAnimation = (_, opts) => {\n    var _a, _b, _c;\n    const OFF_BOTTOM = '40px';\n    const CENTER = '0px';\n    const backDirection = opts.direction === 'back';\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    const ionPageElement = getIonPageElement(enteringEl);\n    const enteringToolbarEle = ionPageElement.querySelector('ion-toolbar');\n    const rootTransition = createAnimation();\n    rootTransition.addElement(ionPageElement).fill('both').beforeRemoveClass('ion-page-invisible');\n    // animate the component itself\n    if (backDirection) {\n        rootTransition.duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n    }\n    else {\n        rootTransition\n            .duration(((_b = opts.duration) !== null && _b !== void 0 ? _b : 0) || 280)\n            .easing('cubic-bezier(0.36,0.66,0.04,1)')\n            .fromTo('transform', `translateY(${OFF_BOTTOM})`, `translateY(${CENTER})`)\n            .fromTo('opacity', 0.01, 1);\n    }\n    // Animate toolbar if it's there\n    if (enteringToolbarEle) {\n        const enteringToolBar = createAnimation();\n        enteringToolBar.addElement(enteringToolbarEle);\n        rootTransition.addAnimation(enteringToolBar);\n    }\n    // setup leaving view\n    if (leavingEl && backDirection) {\n        // leaving content\n        rootTransition.duration(((_c = opts.duration) !== null && _c !== void 0 ? _c : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n        const leavingPage = createAnimation();\n        leavingPage\n            .addElement(getIonPageElement(leavingEl))\n            .onFinish((currentStep) => {\n            if (currentStep === 1 && leavingPage.elements.length > 0) {\n                leavingPage.elements[0].style.setProperty('display', 'none');\n            }\n        })\n            .fromTo('transform', `translateY(${CENTER})`, `translateY(${OFF_BOTTOM})`)\n            .fromTo('opacity', 1, 0);\n        rootTransition.addAnimation(leavingPage);\n    }\n    return rootTransition;\n};\n\nexport { mdTransitionAnimation };\n"], "names": ["mdTransitionAnimation", "_", "opts", "_a", "_b", "_c", "OFF_BOTTOM", "backDirection", "direction", "enteringEl", "leavingEl", "ionPageElement", "getIonPageElement", "enteringToolbarEle", "querySelector", "rootTransition", "createAnimation", "addElement", "fill", "beforeRemoveClass", "duration", "easing", "fromTo", "enteringToolBar", "addAnimation", "leavingPage", "onFinish", "currentStep", "elements", "length", "style", "setProperty"], "sourceRoot": ""}