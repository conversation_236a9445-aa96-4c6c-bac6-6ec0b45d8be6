{"version": 3, "file": "static/js/834.9037cda7.chunk.js", "mappings": ";gKAGA,MAAMA,EAAc,cAEdC,EAAa,CACf,MACA,YACA,QACA,SACA,IACA,QACA,QACA,YACA,aACA,UACA,OACA,OAEEC,EAAqBC,IACvB,IAAIC,EAAe,GACfC,GAAe,EACnB,MAAMC,EAAMH,EAASA,EAAOI,WAAaC,SACnCC,EAAON,GAAkBK,SAASE,KAClCC,EAAYC,IACdR,EAAaS,QAASC,GAAOA,EAAGC,UAAUC,OAAOhB,IACjDY,EAASC,QAASC,GAAOA,EAAGC,UAAUE,IAAIjB,IAC1CI,EAAeQ,GAEbM,EAAcA,KAChBb,GAAe,EACfM,EAAS,KAEPQ,EAAaC,IACff,EAAeJ,EAAWoB,SAASD,EAAGE,KACjCjB,GACDM,EAAS,KAGXY,EAAaH,IACf,GAAIf,QAAoCmB,IAApBJ,EAAGK,aAA4B,CAC/C,MAAMC,EAAUN,EAAGK,eAAeE,OAAQb,KAElCA,EAAGC,WACID,EAAGC,UAAUa,SAxClB,kBA4CVjB,EAASe,EACb,GAEEG,EAAaA,KACXvB,EAAIwB,gBAAkBrB,GACtBE,EAAS,KAGjBL,EAAIyB,iBAAiB,UAAWZ,GAChCb,EAAIyB,iBAAiB,UAAWR,GAChCjB,EAAIyB,iBAAiB,WAAYF,GACjCvB,EAAIyB,iBAAiB,aAAcb,EAAa,CAAEc,SAAS,IAC3D1B,EAAIyB,iBAAiB,YAAab,GAQlC,MAAO,CACHe,QARYA,KACZ3B,EAAI4B,oBAAoB,UAAWf,GACnCb,EAAI4B,oBAAoB,UAAWX,GACnCjB,EAAI4B,oBAAoB,WAAYL,GACpCvB,EAAI4B,oBAAoB,aAAchB,GACtCZ,EAAI4B,oBAAoB,YAAahB,IAIrCP,Y", "sources": ["../node_modules/@ionic/core/components/focus-visible.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst ION_FOCUSED = 'ion-focused';\nconst ION_FOCUSABLE = 'ion-focusable';\nconst FOCUS_KEYS = [\n    'Tab',\n    'ArrowDown',\n    'Space',\n    'Escape',\n    ' ',\n    'Shift',\n    'Enter',\n    'ArrowLeft',\n    'ArrowRight',\n    'ArrowUp',\n    'Home',\n    'End',\n];\nconst startFocusVisible = (rootEl) => {\n    let currentFocus = [];\n    let keyboardMode = true;\n    const ref = rootEl ? rootEl.shadowRoot : document;\n    const root = rootEl ? rootEl : document.body;\n    const setFocus = (elements) => {\n        currentFocus.forEach((el) => el.classList.remove(ION_FOCUSED));\n        elements.forEach((el) => el.classList.add(ION_FOCUSED));\n        currentFocus = elements;\n    };\n    const pointerDown = () => {\n        keyboardMode = false;\n        setFocus([]);\n    };\n    const onKeydown = (ev) => {\n        keyboardMode = FOCUS_KEYS.includes(ev.key);\n        if (!keyboardMode) {\n            setFocus([]);\n        }\n    };\n    const onFocusin = (ev) => {\n        if (keyboardMode && ev.composedPath !== undefined) {\n            const toFocus = ev.composedPath().filter((el) => {\n                // TODO(FW-2832): type\n                if (el.classList) {\n                    return el.classList.contains(ION_FOCUSABLE);\n                }\n                return false;\n            });\n            setFocus(toFocus);\n        }\n    };\n    const onFocusout = () => {\n        if (ref.activeElement === root) {\n            setFocus([]);\n        }\n    };\n    ref.addEventListener('keydown', onKeydown);\n    ref.addEventListener('focusin', onFocusin);\n    ref.addEventListener('focusout', onFocusout);\n    ref.addEventListener('touchstart', pointerDown, { passive: true });\n    ref.addEventListener('mousedown', pointerDown);\n    const destroy = () => {\n        ref.removeEventListener('keydown', onKeydown);\n        ref.removeEventListener('focusin', onFocusin);\n        ref.removeEventListener('focusout', onFocusout);\n        ref.removeEventListener('touchstart', pointerDown);\n        ref.removeEventListener('mousedown', pointerDown);\n    };\n    return {\n        destroy,\n        setFocus,\n    };\n};\n\nexport { startFocusVisible };\n"], "names": ["ION_FOCUSED", "FOCUS_KEYS", "startFocusVisible", "rootEl", "currentFocus", "keyboardMode", "ref", "shadowRoot", "document", "root", "body", "setFocus", "elements", "for<PERSON>ach", "el", "classList", "remove", "add", "pointerDown", "onKeydown", "ev", "includes", "key", "onFocusin", "undefined", "<PERSON><PERSON><PERSON>", "toFocus", "filter", "contains", "onFocusout", "activeElement", "addEventListener", "passive", "destroy", "removeEventListener"], "sourceRoot": ""}