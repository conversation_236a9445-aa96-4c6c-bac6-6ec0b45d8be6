{"name": "alphabet-game", "version": "0.0.1", "private": true, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/app": "7.0.2", "@capacitor/core": "7.4.2", "@capacitor/haptics": "7.0.2", "@capacitor/keyboard": "7.0.2", "@capacitor/status-bar": "7.0.2", "@ionic/react": "^7.0.0", "@ionic/react-router": "^7.0.0", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^12.6.3", "@types/jest": "^26.0.20", "@types/node": "^12.19.15", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/react-router": "^5.1.11", "@types/react-router-dom": "^5.1.7", "history": "^4.9.0", "ionicons": "^7.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.0", "typescript": "^4.1.3", "web-vitals": "^0.2.4", "workbox-background-sync": "^5.1.4", "workbox-broadcast-update": "^5.1.4", "workbox-cacheable-response": "^5.1.4", "workbox-core": "^5.1.4", "workbox-expiration": "^5.1.4", "workbox-google-analytics": "^5.1.4", "workbox-navigation-preload": "^5.1.4", "workbox-precaching": "^5.1.4", "workbox-range-requests": "^5.1.4", "workbox-routing": "^5.1.4", "workbox-strategies": "^5.1.4", "workbox-streams": "^5.1.4"}, "devDependencies": {"@capacitor/cli": "7.4.2", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "eslint": "^8.35.0", "eslint-plugin-react": "^7.32.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "live": "ionic build && ionic cap run android --livereload --external --public-host=*************", "test": "react-scripts test --transformIgnorePatterns 'node_modules/(?!(@ionic/react|@ionic/react-router|@ionic/core|@stencil/core|ionicons)/)'", "eject": "react-scripts eject", "lint": "eslint --ext .ts,.tsx src"}, "browserslist": ["Chrome >=79", "ChromeAndroid >=79", "Firefox >=70", "Edge >=79", "Safari >=14", "iOS >=14"], "description": "An Ionic project"}