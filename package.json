{"name": "alphabet-game", "version": "0.0.1", "private": true, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/app": "7.0.2", "@capacitor/core": "7.4.2", "@capacitor/haptics": "7.0.2", "@capacitor/keyboard": "7.0.2", "@capacitor/status-bar": "7.0.2", "@hookform/resolvers": "^3.10.0", "@ionic/react": "^7.0.0", "@ionic/react-router": "^7.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^12.6.3", "@types/jest": "^26.0.20", "@types/node": "^12.19.15", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/react-router": "^5.1.11", "@types/react-router-dom": "^5.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "history": "^4.9.0", "input-otp": "^1.4.2", "ionicons": "^7.0.0", "lucide-react": "^0.462.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.0", "recharts": "^2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "typescript": "^4.1.3", "vaul": "^0.9.9", "web-vitals": "^0.2.4", "workbox-background-sync": "^5.1.4", "workbox-broadcast-update": "^5.1.4", "workbox-cacheable-response": "^5.1.4", "workbox-core": "^5.1.4", "workbox-expiration": "^5.1.4", "workbox-google-analytics": "^5.1.4", "workbox-navigation-preload": "^5.1.4", "workbox-precaching": "^5.1.4", "workbox-range-requests": "^5.1.4", "workbox-routing": "^5.1.4", "workbox-strategies": "^5.1.4", "workbox-streams": "^5.1.4", "zod": "^3.25.76"}, "devDependencies": {"@capacitor/cli": "7.4.2", "@tailwindcss/typography": "^0.5.16", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "autoprefixer": "^10.4.21", "eslint": "^8.35.0", "eslint-plugin-react": "^7.32.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "live": "ionic build && ionic cap run android --livereload --external --public-host=*************", "test": "react-scripts test --transformIgnorePatterns 'node_modules/(?!(@ionic/react|@ionic/react-router|@ionic/core|@stencil/core|ionicons)/)'", "eject": "react-scripts eject", "lint": "eslint --ext .ts,.tsx src"}, "browserslist": ["Chrome >=79", "ChromeAndroid >=79", "Firefox >=70", "Edge >=79", "Safari >=14", "iOS >=14"], "description": "An Ionic project"}