/*! For license information please see 82.aac29f79.chunk.js.LICENSE.txt */
"use strict";(globalThis.webpackChunkalphabet_game=globalThis.webpackChunkalphabet_game||[]).push([[82],{82:(e,t,o)=>{o.r(t),o.d(t,{startInputShims:()=>y});var n=o(286),i=o(721),r=o(384),a=o(793);const s=new WeakMap,d=function(e,t,o){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];s.has(e)!==o&&(o?l(e,t,n,i):c(e,t))},l=function(e,t,o){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const i=t.parentNode,r=t.cloneNode(!1);r.classList.add("cloned-input"),r.tabIndex=-1,n&&(r.disabled=!0),i.appendChild(r),s.set(e,r);const a="rtl"===e.ownerDocument.dir?9999:-9999;e.style.pointerEvents="none",t.style.transform=`translate3d(${a}px,${o}px,0) scale(0)`},c=(e,t)=>{const o=s.get(e);o&&(s.delete(e),o.remove()),e.style.pointerEvents="",t.style.transform=""},u="input, textarea, [no-blur], [contenteditable]",v=(e,t,o,n)=>{const i=e.top,r=e.bottom,a=t.top,s=a+15,d=Math.min(t.bottom,n-o)-50-r,l=s-i,c=Math.round(d<0?-d:l>0?-l:0),u=Math.min(c,i-a),v=Math.abs(u)/.3;return{scrollAmount:u,scrollDuration:Math.min(400,Math.max(150,v)),scrollPadding:o,inputSafeY:4-(i-s)}},m="$ionPaddingTimer",w=(e,t,o)=>{const n=e[m];n&&clearTimeout(n),t>0?e.style.setProperty("--keyboard-offset",`${t}px`):e[m]=setTimeout(()=>{e.style.setProperty("--keyboard-offset","0px"),o&&o()},120)},h=(e,t,o)=>{e.addEventListener("focusout",()=>{t&&w(t,0,o)},{once:!0})};let f=0;const p="data-ionic-skip-scroll-assist",g=e=>{document.activeElement!==e&&(e.setAttribute(p,"true"),e.focus())},b=async function(e,t,o,n,a,s){let l=arguments.length>6&&void 0!==arguments[6]&&arguments[6],c=!(arguments.length>8&&void 0!==arguments[8])||arguments[8];if(!o&&!n)return;const u=((e,t,o,n)=>{var i;const r=null!==(i=e.closest("ion-item,[ion-item]"))&&void 0!==i?i:e;return v(r.getBoundingClientRect(),t.getBoundingClientRect(),o,n)})(e,o||n,a,arguments.length>7&&void 0!==arguments[7]?arguments[7]:0);if(o&&Math.abs(u.scrollAmount)<4)return g(t),void(s&&null!==o&&(w(o,f),h(t,o,()=>f=0)));if(d(e,t,!0,u.inputSafeY,l),g(t),(0,r.r)(()=>e.click()),s&&o&&(f=u.scrollPadding,w(o,f)),"undefined"!==typeof window){let n;const r=async()=>{void 0!==n&&clearTimeout(n),window.removeEventListener("ionKeyboardDidShow",a),window.removeEventListener("ionKeyboardDidShow",r),o&&await(0,i.c)(o,0,u.scrollAmount,u.scrollDuration),d(e,t,!1,u.inputSafeY),g(t),s&&h(t,o,()=>f=0)},a=()=>{window.removeEventListener("ionKeyboardDidShow",a),window.addEventListener("ionKeyboardDidShow",r)};if(o){const e=await(0,i.g)(o),s=e.scrollHeight-e.clientHeight;if(c&&u.scrollAmount>s-e.scrollTop)return"password"===t.type?(u.scrollAmount+=50,window.addEventListener("ionKeyboardDidShow",a)):window.addEventListener("ionKeyboardDidShow",r),void(n=setTimeout(r,1e3))}r()}},y=async(e,t)=>{if(void 0===n.d)return;const o="ios"===t,s="android"===t,l=e.getNumber("keyboardHeight",290),c=e.getBoolean("scrollAssist",!0),v=e.getBoolean("hideCaretOnScroll",o),m=e.getBoolean("inputBlurring",o),w=e.getBoolean("scrollPadding",!0),h=Array.from(n.d.querySelectorAll("ion-input, ion-textarea")),f=new WeakMap,g=new WeakMap,y=await a.K.getResizeMode(),E=async e=>{await new Promise(t=>(0,r.c)(e,t));const t=e.shadowRoot||e,o=t.querySelector("input")||t.querySelector("textarea"),u=(0,i.a)(e),m=u?null:e.closest("ion-footer");if(!o)return;if(u&&v&&!f.has(e)){const t=((e,t,o)=>{if(!o||!t)return()=>{};const n=o=>{var n;(n=t)===n.getRootNode().activeElement&&d(e,t,o)},i=()=>d(e,t,!1),a=()=>n(!0),s=()=>n(!1);return(0,r.a)(o,"ionScrollStart",a),(0,r.a)(o,"ionScrollEnd",s),t.addEventListener("blur",i),()=>{(0,r.b)(o,"ionScrollStart",a),(0,r.b)(o,"ionScrollEnd",s),t.removeEventListener("blur",i)}})(e,o,u);f.set(e,t)}if(!("date"===o.type||"datetime-local"===o.type)&&(u||m)&&c&&!g.has(e)){const t=function(e,t,o,i,r,s,d){let l=arguments.length>7&&void 0!==arguments[7]&&arguments[7];const c=s&&(void 0===d||d.mode===a.a.None);let u=!1;const v=void 0!==n.w?n.w.innerHeight:0,m=n=>{!1!==u?b(e,t,o,i,n.detail.keyboardHeight,c,l,v,!1):u=!0},w=()=>{u=!1,null===n.w||void 0===n.w||n.w.removeEventListener("ionKeyboardDidShow",m),e.removeEventListener("focusout",w)},h=async()=>{t.hasAttribute(p)?t.removeAttribute(p):(b(e,t,o,i,r,c,l,v),null===n.w||void 0===n.w||n.w.addEventListener("ionKeyboardDidShow",m),e.addEventListener("focusout",w))};return e.addEventListener("focusin",h),()=>{e.removeEventListener("focusin",h),null===n.w||void 0===n.w||n.w.removeEventListener("ionKeyboardDidShow",m),e.removeEventListener("focusout",w)}}(e,o,u,m,l,w,y,s);g.set(e,t)}};m&&(()=>{let e=!0,t=!1;const o=document,n=()=>{t=!0},i=()=>{e=!0},a=n=>{if(t)return void(t=!1);const i=o.activeElement;if(!i)return;if(i.matches(u))return;const r=n.target;r!==i&&(r.matches(u)||r.closest(u)||(e=!1,setTimeout(()=>{e||i.blur()},50)))};(0,r.a)(o,"ionScrollStart",n),o.addEventListener("focusin",i,!0),o.addEventListener("touchend",a,!1)})();for(const n of h)E(n);n.d.addEventListener("ionInputDidLoad",e=>{E(e.detail)}),n.d.addEventListener("ionInputDidUnload",e=>{(e=>{if(v){const t=f.get(e);t&&t(),f.delete(e)}if(c){const t=g.get(e);t&&t(),g.delete(e)}})(e.detail)})}}}]);
//# sourceMappingURL=82.aac29f79.chunk.js.map