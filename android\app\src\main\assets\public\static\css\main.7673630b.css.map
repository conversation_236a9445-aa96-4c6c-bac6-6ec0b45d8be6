{"version": 3, "file": "static/css/main.7673630b.css", "mappings": "AAAA,WAGE,MAAO,CADP,iBAAkB,CAElB,OAAQ,CAHR,iBAAkB,CAIlB,OAAQ,CACR,0BACF,CAEA,kBACE,cAAe,CACf,gBACF,CAEA,aAGE,aAAc,CAFd,cAAe,CACf,gBAAiB,CAEjB,QACF,CAEA,aACE,oBACF,CChBA,SACE,wFAA6F,CAE/F,QACE,uDAA0D,CAG5D,KACE,6CAA8C,CAC9C,yCAA0C,CAG5C,KACE,uCAGF,wBACE,gBAYF,+LAGE,gBAQF,qIAEE,mBAQF,+BAEE,mDADA,oDACA,CAOF,oCACE,4CACE,uBAAwB,CAAxB,CA+BJ,2DACE,oBAAqB,CACrB,iBAAkB,CAapB,wCACE,4CAyBA,mBATA,4DACA,uEACA,sEACA,qFACA,mEACA,iEAIA,qBATA,8DACA,yEACA,wEACA,uFACA,qEACA,mEAIA,oBATA,6DACA,uEACA,uEACA,sFACA,oEACA,kEAIA,mBATA,4DACA,uEACA,sEACA,qFACA,mEACA,iEAIA,mBATA,4DACA,sEACA,sEACA,+EACA,mEACA,iEAIA,kBATA,2DACA,qEACA,qEACA,oFACA,kEACA,gEAIA,iBATA,0DACA,sEACA,oEACA,6EACA,iEACA,+DAIA,kBATA,2DACA,uEACA,qEACA,oFACA,kEACA,gEAIA,gBATA,yDACA,kEACA,mEACA,kFACA,gEACA,8DAaF,UCmSE,QDlSwB,CAQxB,0BANA,aAGA,sBACA,8BCiPI,MDvPuB,CAG3B,kBCqPI,ODxPiB,CCiSrB,KDjSkB,CASlB,SEtG+B,CFiHjC,oBAGE,qBAEA,YAJA,iBAIA,CAGF,8CACE,kBAGF,iRAeE,uBAGF,oBACE,UAGF,wCACE,cAOF,6CACE,4BAA6B,CAG/B,4BACE,KACE,gDAAiD,CAAjD,CAIJ,gDACE,KACE,4CAA6C,CAC7C,kDAAmD,CACnD,8CAA+C,CAC/C,gDAAiD,CAAjD,CAQJ,mFAEE,cAOF,cCgXM,wBD5WN,mBACE,eAiBA,kBANA,oBAVA,yBAgBA,CAUF,+BACE,iBAAkB,CAGpB,4CACE,gBAGF,0BACE,gCGpR+B,CHuRjC,mCACE,+BGrR+B,CHgSjC,gDACE,+BIvS8B,CJ2ShC,6EACE,2BACA,4BAEF,4EACE,8BACA,+BAEF,qEACE,kBAAmB,CAGrB,0EACE,mDAGF,sCACE,yCAEE,2BASJ,qJAEE,yBAGF,2GACE,kBAAmB,CACnB,wBAAyB,CAG3B,uMAEE,aAMF,6CACE,iBASF,6BACE,mBAAoB,CACpB,oBAAqB,CAUvB,wDAEE,iBADA,WACA,CAaF,oCACE,gBAUF,4CACE,kBACE,iBK1YJ,4BAIE,uBAKF,sBACE,aAEA,SAQF,SAEE,gBAOF,IACE,eAMF,GAGE,eAEA,mBAJA,UAIA,CAIF,IACE,cAIF,kBAIE,gCACA,cAgBF,4BAIE,oBACA,mBAGF,SAME,cADA,aAFA,YAFA,aAKA,CAGF,sBACE,iBAGF,2BAOE,cADA,aAFA,QAGA,CAQF,6DAKE,0BAFA,cAEA,CAIF,qNAkBE,0BAGF,6BAEE,oBAGF,OAME,8BAKA,0BATA,SACA,gBACA,oBACA,mBACA,qBACA,cANA,UAOA,mBAGA,CAGF,kBALE,cAMA,CAIF,kDAGE,eAIF,iDAIE,QAAO,CAFP,SAEA,CAMF,4FAEE,YAMF,+FAEE,wBAQF,MACE,yBACA,iBAGF,MAEE,UC1MF,EAGE,0CACA,wCACA,2BAJA,qBAIA,CAGF,KAGE,8BAEA,sBAHA,YADA,UAIA,CAGF,yBACE,aAGF,iBACE,cAGF,aACE,aAGF,KL0EE,kCACA,mCKnCA,uBAEA,yBAEA,qBAGA,8BAEA,sBAxCA,YLkTA,QKzTgB,CAQhB,gBAFA,eA4BA,gBAUA,2BL6QA,SKxTiB,CAEjB,eA6BA,kCAIA,0BANA,wBAzBA,UA0CA,CCvDF,KACE,mCAOF,sCACE,KAIE,0DAIJ,EACE,yBACA,uCAGF,kBAQE,eArD6B,CAuD7B,gBNiSA,kBMrS4B,CNoS5B,eMpV6B,CAuD/B,GAGE,mBN0RA,eMjV6B,CA0D/B,GAGE,iBNoRA,eM9U6B,CA6D/B,GACE,kBA3D6B,CA8D/B,GACE,iBA5D6B,CA+D/B,GACE,kBA7D6B,CAgE/B,GACE,cA9D6B,CAqE/B,cAHE,aAWA,CARF,QAME,cAJA,kBAMA,uBAGF,IACE,UAGF,IACE,cClGF,gBACE,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,kBAAmB,CP4UnB,SO1UiB,CAGnB,aACE,wCACA,sCACA,sCACA,yCPoTE,0COvUM,CPsVR,uCAjBE,4COrUM,CAwBV,8BP6TE,mCOrVQ,CAwBV,iBACE,qCAzBQ,CA8BV,mBACE,wCPsSE,4COrUM,CAoCV,iBACE,sCPkSE,0COvUM,CA0CV,oBACE,yCP2SA,sCOtVQ,CAgDV,sBACE,sCACA,yCPoSA,uCADA,mCOrVQ,CAuDV,wBACE,wCACA,sCP8QE,2CAFA,4COrUM,CAkEV,eACE,gBAAiB,CACjB,cAAe,CACf,cAAe,CACf,iBAAkB,CPgRlB,QO9QgB,CAGlB,YACE,sCACA,oCACA,oCACA,uCPwPE,wCOtUK,CPoUL,0COpUK,CPqVP,oCOrVO,CAmFT,4BPiQE,iCOpVO,CAmFT,gBACE,mCApFO,CAyFT,kBACE,sCP0OE,0COpUK,CA+FT,gBACE,oCPsOE,wCOtUK,CAqGT,mBACE,uCP+OA,oCOrVO,CA2GT,qBACE,oCACA,uCPwOA,qCADA,iCOpVO,CAkHT,uBACE,sCACA,oCPkNE,yCAFA,0COpUK,CCGL,gBR2fE,qBQvfF,iBRufE,sBQnfF,iBRqeE,qBAzPO,0CA4PP,sBArPO,2BAqPP,sBA/OJ,8BAcW,0BAiOP,uBQpeF,eRweE,sBAhQO,wCAmQP,qBA5PO,yBA4PP,qBAtPJ,8BAcW,wBAwOP,sBAnWF,wBQpJA,mBR2fE,qBQvfF,oBRufE,sBQnfF,oBRqeE,qBAzPO,6CA4PP,sBArPO,8BAqPP,sBA/OJ,8BAcW,6BAiOP,uBQpeF,kBRweE,sBAhQO,2CAmQP,qBA5PO,4BA4PP,qBAtPJ,8BAcW,2BAwOP,uBAnWF,wBQpJA,mBR2fE,qBQvfF,oBRufE,sBQnfF,oBRqeE,qBAzPO,6CA4PP,sBArPO,8BAqPP,sBA/OJ,8BAcW,6BAiOP,uBQpeF,kBRweE,sBAhQO,2CAmQP,qBA5PO,4BA4PP,qBAtPJ,8BAcW,2BAwOP,uBAnWF,wBQpJA,mBR2fE,qBQvfF,oBRufE,sBQnfF,oBRqeE,qBAzPO,6CA4PP,sBArPO,8BAqPP,sBA/OJ,8BAcW,6BAiOP,uBQpeF,kBRweE,sBAhQO,2CAmQP,qBA5PO,4BA4PP,qBAtPJ,8BAcW,2BAwOP,uBAnWF,yBQpJA,mBR2fE,qBQvfF,oBRufE,sBQnfF,oBRqeE,qBAzPO,6CA4PP,sBArPO,8BAqPP,sBA/OJ,8BAcW,6BAiOP,uBQpeF,kBRweE,sBAhQO,2CAmQP,qBA5PO,4BA4PP,qBAtPJ,8BAcW,2BAwOP,uBSvfF,iBACE,4BAGF,kBACE,6BAGF,gBACE,2BAGF,cACE,yBAGF,eACE,0BAGF,gBACE,2BAGF,iBACE,6BAGF,eACE,6BTuHF,wBSpJA,oBACE,4BAGF,qBACE,6BAGF,mBACE,2BAGF,iBACE,yBAGF,kBACE,0BAGF,mBACE,2BAGF,oBACE,6BAGF,kBACE,8BTuHF,wBSpJA,oBACE,4BAGF,qBACE,6BAGF,mBACE,2BAGF,iBACE,yBAGF,kBACE,0BAGF,mBACE,2BAGF,oBACE,6BAGF,kBACE,8BTuHF,wBSpJA,oBACE,4BAGF,qBACE,6BAGF,mBACE,2BAGF,iBACE,yBAGF,kBACE,0BAGF,mBACE,2BAGF,oBACE,6BAGF,kBACE,8BTuHF,yBSpJA,oBACE,4BAGF,qBACE,6BAGF,mBACE,2BAGF,iBACE,yBAGF,kBACE,0BAGF,mBACE,2BAGF,oBACE,6BAGF,kBACE,8BC7BF,oBAEE,mCAGF,oBAEE,mCAGF,qBAEE,oCVwIF,wBUpJA,uBAEE,mCAGF,uBAEE,mCAGF,wBAEE,qCVwIF,wBUpJA,uBAEE,mCAGF,uBAEE,mCAGF,wBAEE,qCVwIF,wBUpJA,uBAEE,mCAGF,uBAEE,mCAGF,wBAEE,qCVwIF,yBUpJA,uBAEE,mCAGF,uBAEE,mCAGF,wBAEE,qCCjBN,sBACE,gCAGF,oBACE,8BAGF,uBACE,4BAGF,wBACE,6BAGF,yBACE,8BAGF,qBACE,0BAOF,UACE,yBAGF,YACE,2BAGF,kBACE,iCAOF,2BACE,qCAGF,4BACE,iCAGF,yBACE,mCAGF,4BACE,uCAGF,6BACE,wCAGF,4BACE,uCAOF,uBACE,iCAGF,wBACE,6BAGF,qBACE,+BAGF,yBACE,8BAGF,0BACE,+BCvEE,sCACE,uBZsIF,wBY/IA,gBACE,wBZuLF,2BYhLA,kBACE,wBZsIF,wBY/IA,gBACE,wBZuLF,2BYhLA,kBACE,wBZsIF,wBY/IA,gBACE,wBZuLF,2BYhLA,kBACE,wBZsIF,yBY/IA,gBACE,wBZuLF,4BYhLA,kBACE,wBCvBN,MAEE,2BAA4B,CAC5B,kCAAqC,CACrC,iCAAqC,CACrC,4CAA+C,CAC/C,iCAAkC,CAClC,gCAAiC,CAGjC,6BAA8B,CAC9B,oCAAuC,CACvC,mCAAuC,CACvC,8CAAiD,CACjD,mCAAoC,CACpC,kCAAmC,CAGnC,4BAA6B,CAC7B,kCAAqC,CACrC,kCAAsC,CACtC,6CAAgD,CAChD,kCAAmC,CACnC,iCAAkC,CAGlC,2BAA4B,CAC5B,kCAAqC,CACrC,iCAAqC,CACrC,4CAA+C,CAC/C,iCAAkC,CAClC,gCAAiC,CAGjC,2BAA4B,CAC5B,iCAAoC,CACpC,iCAAqC,CACrC,sCAAyC,CACzC,iCAAkC,CAClC,gCAAiC,CAGjC,0BAA2B,CAC3B,gCAAmC,CACnC,gCAAoC,CACpC,2CAA8C,CAC9C,gCAAiC,CACjC,+BAAgC,CAGhC,wBAAyB,CACzB,6BAAgC,CAChC,8BAAkC,CAClC,yCAA4C,CAC5C,8BAA+B,CAC/B,6BAA8B,CAG9B,0BAA2B,CAC3B,kCAAqC,CACrC,gCAAoC,CACpC,2CAA8C,CAC9C,gCAAiC,CACjC,+BAAgC,CAGhC,yBAA0B,CAC1B,iCAAoC,CACpC,+BAAmC,CACnC,oCAAuC,CACvC,+BAAgC,CAChC,8BACF,CAEA,mCAME,KACE,2BAA4B,CAC5B,kCAAmC,CACnC,iCAAqC,CACrC,4CAA6C,CAC7C,iCAAkC,CAClC,gCAAiC,CAEjC,6BAA8B,CAC9B,oCAAqC,CACrC,mCAAuC,CACvC,8CAA+C,CAC/C,mCAAoC,CACpC,kCAAmC,CAEnC,4BAA6B,CAC7B,oCAAqC,CACrC,kCAAsC,CACtC,6CAA8C,CAC9C,kCAAmC,CACnC,iCAAkC,CAElC,2BAA4B,CAC5B,kCAAmC,CACnC,iCAAqC,CACrC,sCAAuC,CACvC,iCAAkC,CAClC,gCAAiC,CAEjC,2BAA4B,CAC5B,kCAAmC,CACnC,iCAAqC,CACrC,sCAAuC,CACvC,iCAAkC,CAClC,gCAAiC,CAEjC,0BAA2B,CAC3B,gCAAiC,CACjC,gCAAoC,CACpC,2CAA4C,CAC5C,gCAAiC,CACjC,+BAAgC,CAEhC,wBAAyB,CACzB,gCAAiC,CACjC,8BAAkC,CAClC,mCAAoC,CACpC,8BAA+B,CAC/B,6BAA8B,CAE9B,0BAA2B,CAC3B,kCAAmC,CACnC,gCAAoC,CACpC,qCAAsC,CACtC,gCAAiC,CACjC,+BAAgC,CAEhC,yBAA0B,CAC1B,8BAA+B,CAC/B,+BAAmC,CACnC,0CAA2C,CAC3C,+BAAgC,CAChC,8BACF,CAOA,UACE,2BAA+B,CAC/B,gCAAiC,CAEjC,qBAAyB,CACzB,gCAAiC,CAEjC,2BAA4B,CAC5B,4BAA6B,CAC7B,4BAA6B,CAC7B,yBAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,yBAA6B,CAC7B,4BAA6B,CAC7B,yBAA6B,CAC7B,4BAA6B,CAC7B,yBAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,yBAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAE7B,0BAA8B,CAE9B,6BACF,CAEA,eACE,gDAAiD,CACjD,kDAAmD,CACnD,oDACF,CAQA,SACE,8BAA+B,CAC/B,mCAAoC,CAEpC,qBAAyB,CACzB,gCAAiC,CAEjC,uBAA2B,CAE3B,2BAA4B,CAC5B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAC7B,4BAA6B,CAE7B,6BAA8B,CAE9B,gCAAiC,CAEjC,gCAAiC,CAEjC,6BACF,CACF", "sources": ["components/ExploreContainer.css", "../node_modules/@ionic/react/src/css/core.scss", "../node_modules/@ionic/react/src/themes/ionic.mixins.scss", "../node_modules/@ionic/react/src/themes/ionic.globals.scss", "../node_modules/@ionic/react/src/components/menu/menu.ios.vars.scss", "../node_modules/@ionic/react/src/components/menu/menu.md.vars.scss", "../node_modules/@ionic/react/src/css/normalize.scss", "../node_modules/@ionic/react/src/css/structure.scss", "../node_modules/@ionic/react/src/css/typography.scss", "../node_modules/@ionic/react/src/css/padding.scss", "../node_modules/@ionic/react/src/css/float-elements.scss", "../node_modules/@ionic/react/src/css/text-alignment.scss", "../node_modules/@ionic/react/src/css/text-transformation.scss", "../node_modules/@ionic/react/src/css/flex-utils.scss", "../node_modules/@ionic/react/src/css/display.scss", "theme/variables.css"], "sourcesContent": ["#container {\n  text-align: center;\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n#container strong {\n  font-size: 20px;\n  line-height: 26px;\n}\n\n#container p {\n  font-size: 16px;\n  line-height: 22px;\n  color: #8c8c8c;\n  margin: 0;\n}\n\n#container a {\n  text-decoration: none;\n}", "@import \"../themes/ionic.globals\";\n@import \"../components/menu/menu.ios.vars\";\n@import \"../components/menu/menu.md.vars\";\n\n// Ionic Font Family\n// --------------------------------------------------\n\nhtml.ios {\n  --ion-default-font: -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Roboto\", sans-serif;\n}\nhtml.md {\n  --ion-default-font: \"Roboto\", \"Helvetica Neue\", sans-serif;\n}\n\nhtml {\n  --ion-default-dynamic-font: -apple-system-body;\n  --ion-font-family: var(--ion-default-font);\n}\n\nbody {\n  background: var(--ion-background-color);\n}\n\nbody.backdrop-no-scroll {\n  overflow: hidden;\n}\n\n// Modal - Card Style\n// --------------------------------------------------\n/**\n * Card style modal needs additional padding on the\n * top of the header. We accomplish this by targeting\n * the first toolbar in the header.\n * Footer also needs this. We do not adjust the bottom\n * padding though because of the safe area.\n */\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal ion-footer ion-toolbar:first-of-type {\n  padding-top: 6px;\n}\n\n/**\n* Card style modal needs additional padding on the\n* bottom of the header. We accomplish this by targeting\n* the last toolbar in the header.\n*/\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type {\n  padding-bottom: 6px;\n}\n\n/**\n* Add padding on the left and right\n* of toolbars while accounting for\n* safe area values when in landscape.\n*/\nhtml.ios ion-modal ion-toolbar {\n  padding-right: calc(var(--ion-safe-area-right) + 8px);\n  padding-left: calc(var(--ion-safe-area-left) + 8px);\n}\n\n/**\n * Card style modal on iPadOS\n * should only have backdrop on first instance.\n */\n@media screen and (min-width: 768px) {\n  html.ios ion-modal.modal-card:first-of-type {\n    --backdrop-opacity: 0.18;\n  }\n}\n\n/**\n * Subsequent modals should not have a backdrop/box shadow\n * as it will cause the screen to appear to get progressively\n * darker. With Ionic 6, declarative modals made it\n * possible to have multiple non-presented modals in the DOM,\n * so we could no longer rely on ion-modal:first-of-type.\n * Here we disable the opacity/box-shadow for every modal\n * that comes after the first presented modal.\n *\n * Note: ion-modal:not(.overlay-hidden):first-of-type\n * does not match the first modal to not have\n * the .overlay-hidden class, it will match the\n * first modal in general only if it does not\n * have the .overlay-hidden class.\n * The :nth-child() pseudo-class has support\n * for selectors which would help us here. At the\n * time of writing it does not have great cross browser\n * support.\n *\n * Note 2: This should only apply to non-card and\n * non-sheet modals. Card and sheet modals have their\n * own criteria for displaying backdrops/box shadows.\n *\n * Do not use :not(.overlay-hidden) in place of\n * .show-modal because that triggers a memory\n * leak in Blink: https://bugs.chromium.org/p/chromium/issues/detail?id=1418768\n */\nion-modal.modal-default.show-modal ~ ion-modal.modal-default {\n  --backdrop-opacity: 0;\n  --box-shadow: none;\n}\n\n/**\n * This works around a bug in WebKit where the\n * content will overflow outside of the bottom border\n * radius when re-painting. As long as a single\n * border radius value is set on .ion-page, this\n * issue does not happen. We set the top left radius\n * here because the top left corner will always have a\n * radius no matter the platform.\n * This behavior only applies to card modals.\n */\nhtml.ios ion-modal.modal-card .ion-page {\n  border-top-left-radius: var(--border-radius);\n}\n\n// Ionic Colors\n// --------------------------------------------------\n// Generates the color classes and variables based on the\n// colors map\n\n@mixin generate-color($color-name) {\n  $value: map-get($colors, $color-name);\n\n  $base: map-get($value, base);\n  $contrast: map-get($value, contrast);\n  $shade: map-get($value, shade);\n  $tint: map-get($value, tint);\n\n  --ion-color-base: var(--ion-color-#{$color-name}, #{$base}) !important;\n  --ion-color-base-rgb: var(--ion-color-#{$color-name}-rgb, #{color-to-rgb-list($base)}) !important;\n  --ion-color-contrast: var(--ion-color-#{$color-name}-contrast, #{$contrast}) !important;\n  --ion-color-contrast-rgb: var(--ion-color-#{$color-name}-contrast-rgb, #{color-to-rgb-list($contrast)}) !important;\n  --ion-color-shade: var(--ion-color-#{$color-name}-shade, #{$shade}) !important;\n  --ion-color-tint: var(--ion-color-#{$color-name}-tint, #{$tint}) !important;\n}\n\n@each $color-name, $value in $colors {\n  .ion-color-#{$color-name} {\n    @include generate-color($color-name);\n  }\n}\n\n\n// Page Container Structure\n// --------------------------------------------------\n\n.ion-page {\n  @include position(0, 0, 0, 0);\n\n  display: flex;\n  position: absolute;\n\n  flex-direction: column;\n  justify-content: space-between;\n\n  contain: layout size style;\n  z-index: $z-index-page-container;\n}\n\n/**\n * When making custom dialogs, using\n * ion-content is not required. As a result,\n * some developers may wish to have dialogs\n * that are automatically sized by the browser.\n * These changes allow certain dimension values\n * such as fit-content to work correctly.\n */\nion-modal > .ion-page {\n  position: relative;\n\n  contain: layout style;\n\n  height: 100%;\n}\n\n.split-pane-visible > .ion-page.split-pane-main {\n  position: relative;\n}\n\nion-route,\nion-route-redirect,\nion-router,\nion-select-option,\nion-nav-controller,\nion-menu-controller,\nion-action-sheet-controller,\nion-alert-controller,\nion-loading-controller,\nion-modal-controller,\nion-picker-controller,\nion-popover-controller,\nion-toast-controller,\n.ion-page-hidden {\n  /* stylelint-disable-next-line declaration-no-important */\n  display: none !important;\n}\n\n.ion-page-invisible {\n  opacity: 0;\n}\n\n.can-go-back > ion-header ion-back-button {\n  display: block;\n}\n\n\n// Ionic Safe Margins\n// --------------------------------------------------\n\nhtml.plt-ios.plt-hybrid, html.plt-ios.plt-pwa {\n  --ion-statusbar-padding: 20px;\n}\n\n@supports (padding-top: 20px) {\n  html {\n    --ion-safe-area-top: var(--ion-statusbar-padding);\n  }\n}\n\n@supports (padding-top: env(safe-area-inset-top)) {\n  html {\n    --ion-safe-area-top: env(safe-area-inset-top);\n    --ion-safe-area-bottom: env(safe-area-inset-bottom);\n    --ion-safe-area-left: env(safe-area-inset-left);\n    --ion-safe-area-right: env(safe-area-inset-right);\n  }\n}\n\n\n// Global Card Styles\n// --------------------------------------------------\n\nion-card.ion-color .ion-inherit-color,\nion-card-header.ion-color .ion-inherit-color {\n  color: inherit;\n}\n\n\n// Menu Styles\n// --------------------------------------------------\n\n.menu-content {\n  @include transform(translate3d(0, 0, 0));\n}\n\n.menu-content-open {\n  cursor: pointer;\n  touch-action: manipulation;\n\n  /**\n   * The containing element itself should be clickable but\n   * everything inside of it should not clickable when menu is open\n   *\n   * Setting pointer-events after scrolling has already started\n   * will not cancel scrolling which is why we also set\n   * overflow-y below.\n   */\n  pointer-events: none;\n\n  /**\n   * This accounts for scenarios where the main content itself\n   * is scrollable.\n   */\n  overflow-y: hidden;\n}\n\n/**\n * Setting overflow cancels any in-progress scrolling\n * when the menu opens. This prevents users from accidentally\n * scrolling the main content while also dragging the menu open.\n * The code below accounts for both ion-content and then custom\n * scroll containers within ion-content (such as virtual scroll)\n */\n.menu-content-open ion-content {\n  --overflow: hidden;\n}\n\n.menu-content-open .ion-content-scroll-host {\n  overflow: hidden;\n}\n\n.ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal;\n}\n\n[dir=rtl].ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal-rtl;\n}\n\n.ios .menu-content-push {\n  box-shadow: $menu-ios-box-shadow-push;\n}\n\n.md .menu-content-reveal {\n  box-shadow: $menu-md-box-shadow;\n}\n\n.md .menu-content-push {\n  box-shadow: $menu-md-box-shadow;\n}\n\n// Accordion Styles\nion-accordion-group.accordion-group-expand-inset > ion-accordion:first-of-type {\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\nion-accordion-group.accordion-group-expand-inset > ion-accordion:last-of-type {\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\nion-accordion-group > ion-accordion:last-of-type ion-item[slot=\"header\"] {\n  --border-width: 0px;\n}\n\nion-accordion.accordion-animated > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transition: 300ms transform cubic-bezier(0.25, 0.8, 0.5, 1);\n}\n\n@media (prefers-reduced-motion: reduce) {\n  ion-accordion .ion-accordion-toggle-icon {\n    /* stylelint-disable declaration-no-important */\n    transition: none !important;\n  }\n}\n/**\n * The > [slot=\"header\"] selector ensures that we do\n * not modify toggle icons for any nested accordions. The state\n * of one accordion should not affect any accordions inside\n * of a nested accordion group.\n */\nion-accordion.accordion-expanding > [slot=\"header\"] .ion-accordion-toggle-icon,\nion-accordion.accordion-expanded > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transform: rotate(180deg);\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-previous ion-item[slot=\"header\"] {\n  --border-width: 0px;\n  --inner-border-width: 0px;\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanding:first-of-type,\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanded:first-of-type {\n  margin-top: 0;\n}\n\n// Safari/iOS 15 changes the appearance of input[type=\"date\"].\n// For backwards compatibility from Ionic 5/Safari 14 designs,\n// we override the appearance only when using within an ion-input.\nion-input input::-webkit-date-and-time-value {\n  text-align: start;\n}\n\n/**\n * The .ion-datetime-button-overlay class contains\n * styles that allow any modal/popover to be\n * sized according to the dimensions of the datetime\n * when used with ion-datetime-button.\n */\n.ion-datetime-button-overlay {\n  --width: fit-content;\n  --height: fit-content;\n}\n\n/**\n * The grid variant can scale down when inline.\n * When used in a `fit-content` overlay, this causes\n * the overlay to shrink when the month/year picker is open.\n * Explicitly setting the dimensions lets us have a consistently\n * sized grid interface.\n */\n.ion-datetime-button-overlay ion-datetime.datetime-grid {\n  width: 320px;\n  min-height: 320px;\n}\n\n/**\n * If a popover has a child ion-content (or class equivalent) then the .popover-viewport element\n * should not be scrollable to ensure the inner content does scroll. However, if the popover\n * does not have a child ion-content (or class equivalent) then the .popover-viewport element\n * should remain scrollable. This code exists globally because popover targets\n * .popover-viewport using ::slotted which only supports simple selectors.\n *\n * Note that we do not need to account for .ion-content-scroll-host here because that\n * class should always be placed within ion-content even if ion-content is not scrollable.\n */\n.popover-viewport:has(> ion-content) {\n  overflow: hidden;\n}\n\n/**\n * :has has cross-browser support, but it is still relatively new. As a result,\n * we should fallback to the old behavior for environments that do not support :has.\n * Developers can explicitly enable this behavior by setting overflow: visible\n * on .popover-viewport if they know they are not going to use an ion-content.\n * TODO FW-6106 Remove this\n */\n@supports not selector(:has(> ion-content)) {\n  .popover-viewport {\n    overflow: hidden;\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      @supports (inset-inline-start: 0) {\n        & {\n          inset-inline-start: $start;\n          inset-inline-end: $end;\n        }\n      }\n    }\n\n    // TODO FW-3766\n    @at-root {\n      @supports not (inset-inline-start: 0) {\n        & {\n          @include ltr() {\n            left: $start;\n            right: $end;\n          }\n          @include rtl() {\n            left: unset;\n            right: unset;\n\n            left: $end;\n            right: $start;\n          }\n        }\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    @include multi-dir() {\n      border-radius: $top-start;\n    }\n  } @else {\n    @include ltr() {\n      border-top-left-radius: $top-start;\n      border-top-right-radius: $top-end;\n      border-bottom-right-radius: $bottom-end;\n      border-bottom-left-radius: $bottom-start;\n    }\n\n    @include rtl() {\n      border-top-left-radius: $top-end;\n      border-top-right-radius: $top-start;\n      border-bottom-right-radius: $bottom-start;\n      border-bottom-left-radius: $bottom-end;\n    }\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "\n// Global Utility Functions\n@import \"./ionic.functions.string\";\n\n// Global Color Functions\n@import \"./ionic.functions.color\";\n\n// Global Font Functions\n@import \"./ionic.functions.font\";\n\n// Global Mixins\n@import \"./ionic.mixins\";\n\n// Default Theme\n@import \"./ionic.theme.default\";\n\n\n// Default General\n// --------------------------------------------------\n$font-family-base:                  var(--ion-font-family, inherit) !default;\n\n// Hairlines width\n$hairlines-width: .55px !default;\n\n// The minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries\n$screen-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n// Input placeholder opacity\n// Ensures that the placeholder has the\n// correct color contrast against the background.\n$placeholder-opacity: 0.6 !default;\n\n$form-control-label-margin: 16px !default;\n\n// How much the stacked labels should be scaled by\n/// The value 0.75 is used to match the MD spec.\n/// iOS does not have a floating label design spec, so we standardize on 0.75.\n$form-control-label-stacked-scale: 0.75 !default;\n\n\n// Z-Index\n// --------------------------------------------------\n// Grouped by elements which would be siblings\n\n$z-index-menu-overlay:           1000;\n$z-index-overlay:                1001;\n\n$z-index-fixed-content:          999;\n$z-index-refresher:              -1;\n\n$z-index-page-container:         0;\n$z-index-toolbar:                10;\n$z-index-toolbar-background:     -1;\n$z-index-toolbar-buttons:        99;\n\n$z-index-backdrop:               2;\n$z-index-overlay-wrapper:        10;\n\n$z-index-item-options:           1;\n$z-index-item-input:             2;\n$z-index-item-divider:           100;\n\n$z-index-reorder-selected:       100;\n", "@import \"../../themes/ionic.globals.ios\";\n\n// iOS Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow color of the menu\n$menu-ios-box-shadow-color:      rgba(0, 0, 0, .08) !default;\n\n/// @prop - Box shadow of the menu\n$menu-ios-box-shadow:            -8px 0 42px $menu-ios-box-shadow-color !default;\n\n/// @prop - Box shadow of the menu in rtl mode\n$menu-ios-box-shadow-rtl:        8px 0 42px $menu-ios-box-shadow-color !default;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal:     $menu-ios-box-shadow !default;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal-rtl: $menu-ios-box-shadow-rtl !default;\n\n/// @prop - Box shadow of the push menu\n$menu-ios-box-shadow-push:       null !default;\n\n/// @prop - Box shadow of the overlay menu\n$menu-ios-box-shadow-overlay:    null !default;\n", "@import \"../../themes/ionic.globals.md\";\n\n// Material Design Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow of the menu\n$menu-md-box-shadow:            4px 0px 16px rgba(0, 0, 0, 0.18) !default;\n", "// ! normalize.css v3.0.2 | MIT License | github.com/necolas/normalize.css\n\n\n// HTML5 display definitions\n// ==========================================================================\n\n// 1. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.\naudio,\ncanvas,\nprogress,\nvideo {\n  vertical-align: baseline; // 1\n}\n\n// Prevent modern browsers from displaying `audio` without controls.\n// Remove excess height in iOS 5 devices.\naudio:not([controls]) {\n  display: none;\n\n  height: 0;\n}\n\n\n// Text-level semantics\n// ==========================================================================\n\n// Address style set to `bolder` in Firefox 4+, Safari, and Chrome.\nb,\nstrong {\n  font-weight: bold;\n}\n\n// Embedded content\n// ==========================================================================\n\n// Makes it so the img does not flow outside container\nimg {\n  max-width: 100%;\n}\n\n// Grouping content\n// ==========================================================================\n\nhr {\n  height: 1px;\n\n  border-width: 0;\n\n  box-sizing: content-box;\n}\n\n// Contain overflow in all browsers.\npre {\n  overflow: auto;\n}\n\n// Address odd `em`-unit font size rendering in all browsers.\ncode,\nkbd,\npre,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\n\n// Forms\n// ==========================================================================\n\n// Known limitation: by default, Chrome and Safari on OS X allow very limited\n// styling of `select`, unless a `border` property is set.\n\n// 1. Correct color not being inherited.\n//    Known issue: affects color of disabled elements.\n// 2. Correct font properties not being inherited.\n// 3. Address margins set differently in Firefox 4+, Safari, and Chrome.\n//\n\nlabel,\ninput,\nselect,\ntextarea {\n  font-family: inherit;\n  line-height: normal;\n}\n\ntextarea {\n  overflow: auto;\n\n  height: auto;\n\n  font: inherit;\n  color: inherit;\n}\n\ntextarea::placeholder {\n  padding-left: 2px;\n}\n\nform,\ninput,\noptgroup,\nselect {\n  margin: 0; // 3\n\n  font: inherit; // 2\n  color: inherit; // 1\n}\n\n// 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`\n//    and `video` controls.\n// 2. Correct inability to style clickable `input` types in iOS.\n// 3. Improve usability and consistency of cursor style between image-type\n//    `input` and others.\nhtml input[type=\"button\"], // 1\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n  cursor: pointer; // 3\n\n  -webkit-appearance: button; // 2\n}\n\n// remove 300ms delay\na,\na div,\na span,\na ion-icon,\na ion-label,\nbutton,\nbutton div,\nbutton span,\nbutton ion-icon,\nbutton ion-label,\n.ion-tappable,\n[tappable],\n[tappable] div,\n[tappable] span,\n[tappable] ion-icon,\n[tappable] ion-label,\ninput,\ntextarea {\n  touch-action: manipulation;\n}\n\na ion-label,\nbutton ion-label {\n  pointer-events: none;\n}\n\nbutton {\n  padding: 0;\n  border: 0;\n  border-radius: 0;\n  font-family: inherit;\n  font-style: inherit;\n  font-variant: inherit;\n  line-height: 1;\n  text-transform: none;\n  cursor: pointer;\n\n  -webkit-appearance: button;\n}\n\n[tappable] {\n  cursor: pointer;\n}\n\n// Re-set default cursor for disabled elements.\na[disabled],\nbutton[disabled],\nhtml input[disabled] {\n  cursor: default;\n}\n\n// Remove inner padding and border in Firefox 4+.\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n  padding: 0;\n\n  border: 0;\n}\n\n// Fix the cursor style for Chrome's increment/decrement buttons. For certain\n// `font-size` values of the `input`, it causes the cursor style of the\n// decrement button to change from `default` to `text`.\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n// Remove inner padding and search cancel button in Safari and Chrome on OS X.\n// Safari (but not Chrome) clips the cancel button when the search input has\n// padding (and `textfield` appearance).\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n\n// Tables\n// ==========================================================================//\n\n// Remove most spacing between table cells.\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Structure\n// --------------------------------------------------\n// Adds structural css to the native html elements\n\n* {\n  box-sizing: border-box;\n\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n}\n\nhtml {\n  width: 100%;\n  height: 100%;\n  -webkit-text-size-adjust: 100%;\n\n  text-size-adjust: 100%;\n}\n\nhtml:not(.hydrated) body {\n  display: none;\n}\n\nhtml.ion-ce body {\n  display: block;\n}\n\nhtml.plt-pwa {\n  height: 100vh;\n}\n\nbody {\n  @include font-smoothing();\n  @include margin(0);\n  @include padding(0);\n\n  position: fixed;\n\n  width: 100%;\n  max-width: 100%;\n  height: 100%;\n  max-height: 100%;\n\n  /**\n   * Because body has position: fixed,\n   * it should be promoted to its own\n   * layer.\n   *\n   * WebKit does not always promote\n   * the body to its own layer on page\n   * load in Ionic apps. Once scrolling on\n   * ion-content starts, WebKit will promote\n   * body. Unfortunately, this causes a re-paint\n   * which results in scrolling being halted\n   * until the next user gesture.\n   *\n   * This impacts the Custom Elements build.\n   * The lazy loaded build causes the browser to\n   * re-paint during hydration which causes WebKit\n   * to promote body to its own layer.\n   * In the CE Build, this hydration does not\n   * happen, so the additional re-paint does not occur.\n   */\n  transform: translateZ(0);\n\n  text-rendering: optimizeLegibility;\n\n  overflow: hidden;\n\n  touch-action: manipulation;\n\n  -webkit-user-drag: none;\n\n  -ms-content-zooming: none;\n\n  word-wrap: break-word;\n\n  overscroll-behavior-y: none;\n  -webkit-text-size-adjust: none;\n\n  text-size-adjust: none;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Typography\n// --------------------------------------------------\n\n/// @prop - Font weight of all headings\n$headings-font-weight:         500 !default;\n\n/// @prop - Line height of all headings\n$headings-line-height:         1.2 !default;\n\n/// @prop - Font size of heading level 1\n$h1-font-size:                 dynamic-font(26px) !default;\n\n/// @prop - Font size of heading level 2\n$h2-font-size:                 dynamic-font(24px) !default;\n\n/// @prop - Font size of heading level 3\n$h3-font-size:                 dynamic-font(22px) !default;\n\n/// @prop - Font size of heading level 4\n$h4-font-size:                 dynamic-font(20px) !default;\n\n/// @prop - Font size of heading level 5\n$h5-font-size:                 dynamic-font(18px) !default;\n\n/// @prop - Font size of heading level 6\n$h6-font-size:                 dynamic-font(16px) !default;\n\nhtml {\n  font-family: var(--ion-font-family);\n}\n\n/**\n * Dynamic Type is an iOS-only feature, so\n * this should only be enabled on iOS devices.\n */\n@supports (-webkit-touch-callout: none) {\n  html {\n    /**\n     * Includes fallback if Dynamic Type is not enabled.\n     */\n    font: var(--ion-dynamic-font, 16px var(--ion-font-family));\n  }\n}\n\na {\n  background-color: transparent;\n  color: ion-color(primary, base);\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  @include margin(16px, null, 10px, null);\n\n  font-weight: $headings-font-weight;\n\n  line-height: $headings-line-height;\n}\n\nh1 {\n  @include margin(20px, null, null, null);\n\n  font-size: $h1-font-size;\n}\n\nh2 {\n  @include margin(18px, null, null, null);\n\n  font-size: $h2-font-size;\n}\n\nh3 {\n  font-size: $h3-font-size;\n}\n\nh4 {\n  font-size: $h4-font-size;\n}\n\nh5 {\n  font-size: $h5-font-size;\n}\n\nh6 {\n  font-size: $h6-font-size;\n}\n\nsmall {\n  font-size: 75%;\n}\n\nsub,\nsup {\n  position: relative;\n\n  font-size: 75%;\n\n  line-height: 0;\n\n  vertical-align: baseline;\n}\n\nsup {\n  top: -.5em;\n}\n\nsub {\n  bottom: -.25em;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Element Space\n// --------------------------------------------------\n// Creates padding and margin attributes to be used on\n// any element\n\n$padding: var(--ion-padding, 16px);\n$margin: var(--ion-margin, 16px);\n\n// Padding\n// --------------------------------------------------\n\n.ion-no-padding {\n  --padding-start: 0;\n  --padding-end: 0;\n  --padding-top: 0;\n  --padding-bottom: 0;\n\n  @include padding(0);\n}\n\n.ion-padding {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding);\n}\n\n.ion-padding-top {\n  --padding-top: #{$padding};\n\n  @include padding($padding, null, null, null);\n}\n\n.ion-padding-start {\n  --padding-start: #{$padding};\n\n  @include padding-horizontal($padding, null);\n}\n\n.ion-padding-end {\n  --padding-end: #{$padding};\n\n  @include padding-horizontal(null, $padding);\n}\n\n.ion-padding-bottom {\n  --padding-bottom: #{$padding};\n\n  @include padding(null, null, $padding, null);\n}\n\n.ion-padding-vertical {\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding, null, $padding, null);\n}\n\n.ion-padding-horizontal {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n\n  @include padding-horizontal($padding);\n}\n\n\n// Margin\n// --------------------------------------------------\n\n.ion-no-margin {\n  --margin-start: 0;\n  --margin-end: 0;\n  --margin-top: 0;\n  --margin-bottom: 0;\n\n  @include margin(0);\n}\n\n.ion-margin {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin);\n}\n\n.ion-margin-top {\n  --margin-top: #{$margin};\n\n  @include margin($margin, null, null, null);\n}\n\n.ion-margin-start {\n  --margin-start: #{$margin};\n\n  @include margin-horizontal($margin, null);\n}\n\n.ion-margin-end {\n  --margin-end: #{$margin};\n\n  @include margin-horizontal(null, $margin);\n}\n\n.ion-margin-bottom {\n  --margin-bottom: #{$margin};\n\n  @include margin(null, null, $margin, null);\n}\n\n.ion-margin-vertical {\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin, null, $margin, null);\n}\n\n.ion-margin-horizontal {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n\n  @include margin-horizontal($margin);\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Float Elements\n// --------------------------------------------------\n// Creates float classes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-float-{bp}-{side}` classes for floating the element based\n    // on the breakpoint and side\n    .ion-float#{$infix}-left {\n      @include float(left, !important);\n    }\n\n    .ion-float#{$infix}-right {\n      @include float(right, !important);\n    }\n\n    .ion-float#{$infix}-start {\n      @include float(start, !important);\n    }\n\n    .ion-float#{$infix}-end {\n      @include float(end, !important);\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Alignment\n// --------------------------------------------------\n// Creates text alignment attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for aligning the text based\n    // on the breakpoint\n    .ion-text#{$infix}-center {\n      text-align: center !important;\n    }\n\n    .ion-text#{$infix}-justify {\n      text-align: justify !important;\n    }\n\n    .ion-text#{$infix}-start {\n      text-align: start !important;\n    }\n\n    .ion-text#{$infix}-end {\n      text-align: end !important;\n    }\n\n    .ion-text#{$infix}-left {\n      text-align: left !important;\n    }\n\n    .ion-text#{$infix}-right {\n      text-align: right !important;\n    }\n\n    .ion-text#{$infix}-nowrap {\n      white-space: nowrap !important;\n    }\n\n    .ion-text#{$infix}-wrap {\n      white-space: normal !important;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Transformation\n// --------------------------------------------------\n// Creates text transform attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for transforming the text based\n    // on the breakpoint\n    .ion-text#{$infix}-uppercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: uppercase !important;\n    }\n\n    .ion-text#{$infix}-lowercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: lowercase !important;\n    }\n\n    .ion-text#{$infix}-capitalize {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: capitalize !important;\n    }\n  }\n}\n", "// Flex Utilities\n// --------------------------------------------------\n// Creates flex classes to align flex containers\n// and items\n\n// Align Self\n// --------------------------------------------------\n\n.ion-align-self-start {\n  align-self: flex-start !important;\n}\n\n.ion-align-self-end {\n  align-self: flex-end !important;\n}\n\n.ion-align-self-center {\n  align-self: center !important;\n}\n\n.ion-align-self-stretch {\n  align-self: stretch !important;\n}\n\n.ion-align-self-baseline {\n  align-self: baseline !important;\n}\n\n.ion-align-self-auto {\n  align-self: auto !important;\n}\n\n\n// Flex Wrap\n// --------------------------------------------------\n\n.ion-wrap {\n  flex-wrap: wrap !important;\n}\n\n.ion-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.ion-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n\n// Justify Content\n// --------------------------------------------------\n\n.ion-justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.ion-justify-content-center {\n  justify-content: center !important;\n}\n\n.ion-justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.ion-justify-content-around {\n  justify-content: space-around !important;\n}\n\n.ion-justify-content-between {\n  justify-content: space-between !important;\n}\n\n.ion-justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n\n// Align Items\n// --------------------------------------------------\n\n.ion-align-items-start {\n  align-items: flex-start !important;\n}\n\n.ion-align-items-center {\n  align-items: center !important;\n}\n\n.ion-align-items-end {\n  align-items: flex-end !important;\n}\n\n.ion-align-items-stretch {\n  align-items: stretch !important;\n}\n\n.ion-align-items-baseline {\n  align-items: baseline !important;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Display\n// --------------------------------------------------\n// Modifies display of a particular element based on the given classes\n\n.ion-hide {\n  display: none !important;\n}\n\n// Adds hidden classes\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-up` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-up {\n      display: none !important;\n    }\n  }\n\n  @include media-breakpoint-down($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-down` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-down {\n      display: none !important;\n    }\n  }\n}\n", "/* Ionic Variables and Theming. For more info, please see:\nhttp://ionicframework.com/docs/theming/ */\n\n/** Ionic CSS Variables **/\n:root {\n  /** primary **/\n  --ion-color-primary: #3880ff;\n  --ion-color-primary-rgb: 56, 128, 255;\n  --ion-color-primary-contrast: #ffffff;\n  --ion-color-primary-contrast-rgb: 255, 255, 255;\n  --ion-color-primary-shade: #3171e0;\n  --ion-color-primary-tint: #4c8dff;\n\n  /** secondary **/\n  --ion-color-secondary: #3dc2ff;\n  --ion-color-secondary-rgb: 61, 194, 255;\n  --ion-color-secondary-contrast: #ffffff;\n  --ion-color-secondary-contrast-rgb: 255, 255, 255;\n  --ion-color-secondary-shade: #36abe0;\n  --ion-color-secondary-tint: #50c8ff;\n\n  /** tertiary **/\n  --ion-color-tertiary: #5260ff;\n  --ion-color-tertiary-rgb: 82, 96, 255;\n  --ion-color-tertiary-contrast: #ffffff;\n  --ion-color-tertiary-contrast-rgb: 255, 255, 255;\n  --ion-color-tertiary-shade: #4854e0;\n  --ion-color-tertiary-tint: #6370ff;\n\n  /** success **/\n  --ion-color-success: #2dd36f;\n  --ion-color-success-rgb: 45, 211, 111;\n  --ion-color-success-contrast: #ffffff;\n  --ion-color-success-contrast-rgb: 255, 255, 255;\n  --ion-color-success-shade: #28ba62;\n  --ion-color-success-tint: #42d77d;\n\n  /** warning **/\n  --ion-color-warning: #ffc409;\n  --ion-color-warning-rgb: 255, 196, 9;\n  --ion-color-warning-contrast: #000000;\n  --ion-color-warning-contrast-rgb: 0, 0, 0;\n  --ion-color-warning-shade: #e0ac08;\n  --ion-color-warning-tint: #ffca22;\n\n  /** danger **/\n  --ion-color-danger: #eb445a;\n  --ion-color-danger-rgb: 235, 68, 90;\n  --ion-color-danger-contrast: #ffffff;\n  --ion-color-danger-contrast-rgb: 255, 255, 255;\n  --ion-color-danger-shade: #cf3c4f;\n  --ion-color-danger-tint: #ed576b;\n\n  /** dark **/\n  --ion-color-dark: #222428;\n  --ion-color-dark-rgb: 34, 36, 40;\n  --ion-color-dark-contrast: #ffffff;\n  --ion-color-dark-contrast-rgb: 255, 255, 255;\n  --ion-color-dark-shade: #1e2023;\n  --ion-color-dark-tint: #383a3e;\n\n  /** medium **/\n  --ion-color-medium: #92949c;\n  --ion-color-medium-rgb: 146, 148, 156;\n  --ion-color-medium-contrast: #ffffff;\n  --ion-color-medium-contrast-rgb: 255, 255, 255;\n  --ion-color-medium-shade: #808289;\n  --ion-color-medium-tint: #9d9fa6;\n\n  /** light **/\n  --ion-color-light: #f4f5f8;\n  --ion-color-light-rgb: 244, 245, 248;\n  --ion-color-light-contrast: #000000;\n  --ion-color-light-contrast-rgb: 0, 0, 0;\n  --ion-color-light-shade: #d7d8da;\n  --ion-color-light-tint: #f5f6f9;\n}\n\n@media (prefers-color-scheme: dark) {\n  /*\n   * Dark Colors\n   * -------------------------------------------\n   */\n\n  body {\n    --ion-color-primary: #428cff;\n    --ion-color-primary-rgb: 66,140,255;\n    --ion-color-primary-contrast: #ffffff;\n    --ion-color-primary-contrast-rgb: 255,255,255;\n    --ion-color-primary-shade: #3a7be0;\n    --ion-color-primary-tint: #5598ff;\n\n    --ion-color-secondary: #50c8ff;\n    --ion-color-secondary-rgb: 80,200,255;\n    --ion-color-secondary-contrast: #ffffff;\n    --ion-color-secondary-contrast-rgb: 255,255,255;\n    --ion-color-secondary-shade: #46b0e0;\n    --ion-color-secondary-tint: #62ceff;\n\n    --ion-color-tertiary: #6a64ff;\n    --ion-color-tertiary-rgb: 106,100,255;\n    --ion-color-tertiary-contrast: #ffffff;\n    --ion-color-tertiary-contrast-rgb: 255,255,255;\n    --ion-color-tertiary-shade: #5d58e0;\n    --ion-color-tertiary-tint: #7974ff;\n\n    --ion-color-success: #2fdf75;\n    --ion-color-success-rgb: 47,223,117;\n    --ion-color-success-contrast: #000000;\n    --ion-color-success-contrast-rgb: 0,0,0;\n    --ion-color-success-shade: #29c467;\n    --ion-color-success-tint: #44e283;\n\n    --ion-color-warning: #ffd534;\n    --ion-color-warning-rgb: 255,213,52;\n    --ion-color-warning-contrast: #000000;\n    --ion-color-warning-contrast-rgb: 0,0,0;\n    --ion-color-warning-shade: #e0bb2e;\n    --ion-color-warning-tint: #ffd948;\n\n    --ion-color-danger: #ff4961;\n    --ion-color-danger-rgb: 255,73,97;\n    --ion-color-danger-contrast: #ffffff;\n    --ion-color-danger-contrast-rgb: 255,255,255;\n    --ion-color-danger-shade: #e04055;\n    --ion-color-danger-tint: #ff5b71;\n\n    --ion-color-dark: #f4f5f8;\n    --ion-color-dark-rgb: 244,245,248;\n    --ion-color-dark-contrast: #000000;\n    --ion-color-dark-contrast-rgb: 0,0,0;\n    --ion-color-dark-shade: #d7d8da;\n    --ion-color-dark-tint: #f5f6f9;\n\n    --ion-color-medium: #989aa2;\n    --ion-color-medium-rgb: 152,154,162;\n    --ion-color-medium-contrast: #000000;\n    --ion-color-medium-contrast-rgb: 0,0,0;\n    --ion-color-medium-shade: #86888f;\n    --ion-color-medium-tint: #a2a4ab;\n\n    --ion-color-light: #222428;\n    --ion-color-light-rgb: 34,36,40;\n    --ion-color-light-contrast: #ffffff;\n    --ion-color-light-contrast-rgb: 255,255,255;\n    --ion-color-light-shade: #1e2023;\n    --ion-color-light-tint: #383a3e;\n  }\n\n  /*\n   * iOS Dark Theme\n   * -------------------------------------------\n   */\n\n  .ios body {\n    --ion-background-color: #000000;\n    --ion-background-color-rgb: 0,0,0;\n\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255,255,255;\n\n    --ion-color-step-50: #0d0d0d;\n    --ion-color-step-100: #1a1a1a;\n    --ion-color-step-150: #262626;\n    --ion-color-step-200: #333333;\n    --ion-color-step-250: #404040;\n    --ion-color-step-300: #4d4d4d;\n    --ion-color-step-350: #595959;\n    --ion-color-step-400: #666666;\n    --ion-color-step-450: #737373;\n    --ion-color-step-500: #808080;\n    --ion-color-step-550: #8c8c8c;\n    --ion-color-step-600: #999999;\n    --ion-color-step-650: #a6a6a6;\n    --ion-color-step-700: #b3b3b3;\n    --ion-color-step-750: #bfbfbf;\n    --ion-color-step-800: #cccccc;\n    --ion-color-step-850: #d9d9d9;\n    --ion-color-step-900: #e6e6e6;\n    --ion-color-step-950: #f2f2f2;\n\n    --ion-item-background: #000000;\n\n    --ion-card-background: #1c1c1d;\n  }\n\n  .ios ion-modal {\n    --ion-background-color: var(--ion-color-step-100);\n    --ion-toolbar-background: var(--ion-color-step-150);\n    --ion-toolbar-border-color: var(--ion-color-step-250);\n  }\n\n\n  /*\n   * Material Design Dark Theme\n   * -------------------------------------------\n   */\n\n  .md body {\n    --ion-background-color: #121212;\n    --ion-background-color-rgb: 18,18,18;\n\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255,255,255;\n\n    --ion-border-color: #222222;\n\n    --ion-color-step-50: #1e1e1e;\n    --ion-color-step-100: #2a2a2a;\n    --ion-color-step-150: #363636;\n    --ion-color-step-200: #414141;\n    --ion-color-step-250: #4d4d4d;\n    --ion-color-step-300: #595959;\n    --ion-color-step-350: #656565;\n    --ion-color-step-400: #717171;\n    --ion-color-step-450: #7d7d7d;\n    --ion-color-step-500: #898989;\n    --ion-color-step-550: #949494;\n    --ion-color-step-600: #a0a0a0;\n    --ion-color-step-650: #acacac;\n    --ion-color-step-700: #b8b8b8;\n    --ion-color-step-750: #c4c4c4;\n    --ion-color-step-800: #d0d0d0;\n    --ion-color-step-850: #dbdbdb;\n    --ion-color-step-900: #e7e7e7;\n    --ion-color-step-950: #f3f3f3;\n\n    --ion-item-background: #1e1e1e;\n\n    --ion-toolbar-background: #1f1f1f;\n\n    --ion-tab-bar-background: #1f1f1f;\n\n    --ion-card-background: #1e1e1e;\n  }\n}\n"], "names": [], "sourceRoot": ""}