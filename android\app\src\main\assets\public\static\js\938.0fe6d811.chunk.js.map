{"version": 3, "file": "static/js/938.0fe6d811.chunk.js", "mappings": ";kLAMA,MAAMA,EAAiBC,IACnB,QAAYC,IAARC,EAAAA,EACA,OAEJ,IAEIC,EACAC,EACAC,EAJAC,EAA0B,IAAbC,EACbC,EAAgB,EAIpB,MAAMC,EAAkBT,EAAOU,WAAW,YAAY,IAASV,EAAOU,WAAW,gBAAgB,GAC3FC,EAAc,IAAIC,QAMlBC,EAAcC,IAChBR,GAAYS,EAAAA,EAAAA,GAAID,GAChBE,EAAUF,IAkBRG,EAAeA,KACbZ,GACAa,aAAab,GACjBA,OAAcJ,EACVE,IACAgB,GAAgB,GAChBhB,OAAiBF,IAGnBmB,EAAeN,IACbX,GAGJkB,EAAoBC,EAAqBR,GAAKA,IAE5CE,EAAaF,IACfO,OAAoBpB,EAAWa,IAE7BO,EAAsBA,CAACE,EAAIT,KAE7B,GAAIS,GAAMA,IAAOpB,EACb,OAEAE,GACAa,aAAab,GACjBA,OAAcJ,EACd,MAAM,EAAEuB,EAAC,EAAEC,IAAMC,EAAAA,EAAAA,GAAaZ,GAE9B,GAAIX,EAAgB,CAChB,GAAIQ,EAAYgB,IAAIxB,GAChB,MAAM,IAAIyB,MAAM,kBAEfzB,EAAe0B,UAAUC,SAASC,IACnCC,EAAa7B,EAAgBqB,EAAGC,GAEpCN,GAAgB,EACpB,CAEA,GAAII,EAAI,CACJ,MAAMU,EAAUtB,EAAYuB,IAAIX,GAC5BU,IACAf,aAAae,GACbtB,EAAYwB,OAAOZ,IAEvBA,EAAGM,UAAUO,OAAOL,GACpB,MAAMM,EAAWA,KACbL,EAAaT,EAAIC,EAAGC,GACpBpB,OAAcJ,GAEdqC,EAAUf,GACVc,IAGAhC,EAAckC,WAAWF,EAAUG,EAE3C,CACArC,EAAiBoB,GAEfS,EAAeA,CAACT,EAAIC,EAAGC,KAGzB,GAFAjB,EAAgBiC,KAAK1B,MACrBQ,EAAGM,UAAUa,IAAIX,IACZtB,EACD,OACJ,MAAMkC,EAAeC,EAAgBrB,GAChB,OAAjBoB,IACAE,IACAzC,EAAeuC,EAAaG,UAAUtB,EAAGC,KAG3CoB,EAAeA,UACI5C,IAAjBG,IACAA,EAAa2C,KAAMX,GAAWA,KAC9BhC,OAAeH,IAGjBkB,EAAmB6B,IACrBH,IACA,MAAMI,EAAS9C,EACf,IAAK8C,EACD,OAEJ,MAAMC,EAAOC,EAAqBV,KAAK1B,MAAQP,EAC/C,GAAIwC,GAAUE,EAAO,IAAMZ,EAAUW,GAAS,CAC1C,MAAMhB,EAAUM,WAAW,KACvBU,EAAOpB,UAAUO,OAAOL,GACxBpB,EAAYwB,OAAOc,IACpBE,GACHxC,EAAYyC,IAAIH,EAAQhB,EAC5B,MAEIgB,EAAOpB,UAAUO,OAAOL,IAGhC7B,EAAAA,EAAImD,iBAAiB,qBAAsBpC,GAC3Cf,EAAAA,EAAImD,iBAAiB,aAtHCvC,IAClBR,GAAYS,EAAAA,EAAAA,GAAID,GAChBM,EAAYN,KAoHiC,GACjDZ,EAAAA,EAAImD,iBAAiB,cAAexC,GAAY,GAChDX,EAAAA,EAAImD,iBAAiB,WAAYxC,GAAY,GAa7CX,EAAAA,EAAImD,iBAAiB,gBAAiBpC,GAAc,GACpDf,EAAAA,EAAImD,iBAAiB,YA9HAvC,IAEjB,GAAkB,IAAdA,EAAGwC,OACH,OAEJ,MAAMC,GAAIxC,EAAAA,EAAAA,GAAID,GAAMP,EAChBD,EAAYiD,GACZnC,EAAYN,KAuH2B,GAC/CZ,EAAAA,EAAImD,iBAAiB,UArHFvC,IACf,MAAMyC,GAAIxC,EAAAA,EAAAA,GAAID,GAAMP,EAChBD,EAAYiD,GACZvC,EAAUF,KAkHyB,IAGzCQ,EAAwBR,IAC1B,QAAwBb,IAApBa,EAAG0C,aAkBH,OAAO1C,EAAG2C,OAAOC,QAAQ,oBAlBM,CAS/B,MAAMC,EAAO7C,EAAG0C,eAChB,IAAK,IAAII,EAAI,EAAGA,EAAID,EAAKE,OAAS,EAAGD,IAAK,CACtC,MAAMrC,EAAKoC,EAAKC,GAChB,KAAMrC,aAAcuC,aAAevC,EAAGM,UAAUC,SAAS,mBACrD,OAAOP,CAEf,CACJ,GAKEe,EAAaf,GACRA,EAAGM,UAAUC,SAAS,2BAE3Bc,EAAmBrB,IACrB,GAAIA,EAAGwC,WAAY,CACf,MAAMC,EAASzC,EAAGwC,WAAWE,cAAc,qBAC3C,GAAID,EACA,OAAOA,CAEf,CACA,OAAOzC,EAAG0C,cAAc,sBAEtBlC,EAAY,gBACZS,EAAuB,IACvBW,EAAqB,IACrB5C,EAAa,I", "sources": ["../node_modules/@ionic/core/components/index9.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index5.js';\nimport { u as now, v as pointerCoord } from './helpers.js';\n\nconst startTapClick = (config) => {\n    if (doc === undefined) {\n        return;\n    }\n    let lastTouch = -MOUSE_WAIT * 10;\n    let lastActivated = 0;\n    let activatableEle;\n    let activeRipple;\n    let activeDefer;\n    const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n    const clearDefers = new WeakMap();\n    // Touch Events\n    const onTouchStart = (ev) => {\n        lastTouch = now(ev);\n        pointerDown(ev);\n    };\n    const onTouchEnd = (ev) => {\n        lastTouch = now(ev);\n        pointerUp(ev);\n    };\n    const onMouseDown = (ev) => {\n        // Ignore right clicks\n        if (ev.button === 2) {\n            return;\n        }\n        const t = now(ev) - MOUSE_WAIT;\n        if (lastTouch < t) {\n            pointerDown(ev);\n        }\n    };\n    const onMouseUp = (ev) => {\n        const t = now(ev) - MOUSE_WAIT;\n        if (lastTouch < t) {\n            pointerUp(ev);\n        }\n    };\n    const cancelActive = () => {\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        if (activatableEle) {\n            removeActivated(false);\n            activatableEle = undefined;\n        }\n    };\n    const pointerDown = (ev) => {\n        if (activatableEle) {\n            return;\n        }\n        setActivatedElement(getActivatableTarget(ev), ev);\n    };\n    const pointerUp = (ev) => {\n        setActivatedElement(undefined, ev);\n    };\n    const setActivatedElement = (el, ev) => {\n        // do nothing\n        if (el && el === activatableEle) {\n            return;\n        }\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        const { x, y } = pointerCoord(ev);\n        // deactivate selected\n        if (activatableEle) {\n            if (clearDefers.has(activatableEle)) {\n                throw new Error('internal error');\n            }\n            if (!activatableEle.classList.contains(ACTIVATED)) {\n                addActivated(activatableEle, x, y);\n            }\n            removeActivated(true);\n        }\n        // activate\n        if (el) {\n            const deferId = clearDefers.get(el);\n            if (deferId) {\n                clearTimeout(deferId);\n                clearDefers.delete(el);\n            }\n            el.classList.remove(ACTIVATED);\n            const callback = () => {\n                addActivated(el, x, y);\n                activeDefer = undefined;\n            };\n            if (isInstant(el)) {\n                callback();\n            }\n            else {\n                activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n            }\n        }\n        activatableEle = el;\n    };\n    const addActivated = (el, x, y) => {\n        lastActivated = Date.now();\n        el.classList.add(ACTIVATED);\n        if (!useRippleEffect)\n            return;\n        const rippleEffect = getRippleEffect(el);\n        if (rippleEffect !== null) {\n            removeRipple();\n            activeRipple = rippleEffect.addRipple(x, y);\n        }\n    };\n    const removeRipple = () => {\n        if (activeRipple !== undefined) {\n            activeRipple.then((remove) => remove());\n            activeRipple = undefined;\n        }\n    };\n    const removeActivated = (smooth) => {\n        removeRipple();\n        const active = activatableEle;\n        if (!active) {\n            return;\n        }\n        const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n        if (smooth && time > 0 && !isInstant(active)) {\n            const deferId = setTimeout(() => {\n                active.classList.remove(ACTIVATED);\n                clearDefers.delete(active);\n            }, CLEAR_STATE_DEFERS);\n            clearDefers.set(active, deferId);\n        }\n        else {\n            active.classList.remove(ACTIVATED);\n        }\n    };\n    doc.addEventListener('ionGestureCaptured', cancelActive);\n    doc.addEventListener('touchstart', onTouchStart, true);\n    doc.addEventListener('touchcancel', onTouchEnd, true);\n    doc.addEventListener('touchend', onTouchEnd, true);\n    /**\n     * Tap click effects such as the ripple effect should\n     * not happen when scrolling. For example, if a user scrolls\n     * the page but also happens to do a touchstart on a button\n     * as part of the scroll, the ripple effect should not\n     * be dispatched. The ripple effect should only happen\n     * if the button is activated and the page is not scrolling.\n     *\n     * pointercancel is dispatched on a gesture when scrolling\n     * starts, so this lets us avoid having to listen for\n     * ion-content's scroll events.\n     */\n    doc.addEventListener('pointercancel', cancelActive, true);\n    doc.addEventListener('mousedown', onMouseDown, true);\n    doc.addEventListener('mouseup', onMouseUp, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = (ev) => {\n    if (ev.composedPath !== undefined) {\n        /**\n         * composedPath returns EventTarget[]. However,\n         * objects other than Element can be targets too.\n         * For example, AudioContext can be a target. In this\n         * case, we know that the event is a UIEvent so we\n         * can assume that the path will contain either Element\n         * or ShadowRoot.\n         */\n        const path = ev.composedPath();\n        for (let i = 0; i < path.length - 2; i++) {\n            const el = path[i];\n            if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n                return el;\n            }\n        }\n    }\n    else {\n        return ev.target.closest('.ion-activatable');\n    }\n};\nconst isInstant = (el) => {\n    return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = (el) => {\n    if (el.shadowRoot) {\n        const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n        if (ripple) {\n            return ripple;\n        }\n    }\n    return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\nconst MOUSE_WAIT = 2500;\n\nexport { startTapClick };\n"], "names": ["startTapClick", "config", "undefined", "doc", "activatableEle", "activeRipple", "activeDefer", "lastTouch", "MOUSE_WAIT", "lastActivated", "useRippleEffect", "getBoolean", "clearDefers", "WeakMap", "onTouchEnd", "ev", "now", "pointerUp", "cancelActive", "clearTimeout", "removeActivated", "pointerDown", "setActivatedElement", "getActivatableTarget", "el", "x", "y", "pointerCoord", "has", "Error", "classList", "contains", "ACTIVATED", "addActivated", "deferId", "get", "delete", "remove", "callback", "isInstant", "setTimeout", "ADD_ACTIVATED_DEFERS", "Date", "add", "rippleEffect", "getRippleEffect", "removeRipple", "addRipple", "then", "smooth", "active", "time", "CLEAR_STATE_DEFERS", "set", "addEventListener", "button", "t", "<PERSON><PERSON><PERSON>", "target", "closest", "path", "i", "length", "ShadowRoot", "shadowRoot", "ripple", "querySelector"], "sourceRoot": ""}