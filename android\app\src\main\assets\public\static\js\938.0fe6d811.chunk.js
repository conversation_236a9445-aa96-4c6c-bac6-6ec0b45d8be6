/*! For license information please see 938.0fe6d811.chunk.js.LICENSE.txt */
"use strict";(globalThis.webpackChunkalphabet_game=globalThis.webpackChunkalphabet_game||[]).push([[938],{938:(e,t,o)=>{o.r(t),o.d(t,{startTapClick:()=>i});var n=o(286),a=o(384);const i=e=>{if(void 0===n.d)return;let t,o,i,f=10*-v,p=0;const h=e.getBoolean("animated",!0)&&e.getBoolean("rippleEffect",!0),m=new WeakMap,L=e=>{f=(0,a.u)(e),g(e)},w=()=>{i&&clearTimeout(i),i=void 0,t&&(C(!1),t=void 0)},b=e=>{t||E(s(e),e)},g=e=>{E(void 0,e)},E=(e,o)=>{if(e&&e===t)return;i&&clearTimeout(i),i=void 0;const{x:n,y:s}=(0,a.v)(o);if(t){if(m.has(t))throw new Error("internal error");t.classList.contains(d)||T(t,n,s),C(!0)}if(e){const t=m.get(e);t&&(clearTimeout(t),m.delete(e)),e.classList.remove(d);const o=()=>{T(e,n,s),i=void 0};r(e)?o():i=setTimeout(o,l)}t=e},T=(e,t,n)=>{if(p=Date.now(),e.classList.add(d),!h)return;const a=c(e);null!==a&&(k(),o=a.addRipple(t,n))},k=()=>{void 0!==o&&(o.then(e=>e()),o=void 0)},C=e=>{k();const o=t;if(!o)return;const n=u-Date.now()+p;if(e&&n>0&&!r(o)){const e=setTimeout(()=>{o.classList.remove(d),m.delete(o)},u);m.set(o,e)}else o.classList.remove(d)};n.d.addEventListener("ionGestureCaptured",w),n.d.addEventListener("touchstart",e=>{f=(0,a.u)(e),b(e)},!0),n.d.addEventListener("touchcancel",L,!0),n.d.addEventListener("touchend",L,!0),n.d.addEventListener("pointercancel",w,!0),n.d.addEventListener("mousedown",e=>{if(2===e.button)return;const t=(0,a.u)(e)-v;f<t&&b(e)},!0),n.d.addEventListener("mouseup",e=>{const t=(0,a.u)(e)-v;f<t&&g(e)},!0)},s=e=>{if(void 0===e.composedPath)return e.target.closest(".ion-activatable");{const t=e.composedPath();for(let e=0;e<t.length-2;e++){const o=t[e];if(!(o instanceof ShadowRoot)&&o.classList.contains("ion-activatable"))return o}}},r=e=>e.classList.contains("ion-activatable-instant"),c=e=>{if(e.shadowRoot){const t=e.shadowRoot.querySelector("ion-ripple-effect");if(t)return t}return e.querySelector("ion-ripple-effect")},d="ion-activated",l=100,u=150,v=2500}}]);
//# sourceMappingURL=938.0fe6d811.chunk.js.map