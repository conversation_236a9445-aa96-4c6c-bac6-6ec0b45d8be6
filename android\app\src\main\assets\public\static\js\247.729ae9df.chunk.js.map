{"version": 3, "file": "static/js/247.729ae9df.chunk.js", "mappings": ";4LAOA,MAAMA,EAAiBA,KACnB,MAAMC,EAAMC,OACZD,EAAIE,iBAAiB,YAAa,MAC9BC,EAAAA,EAAAA,IAAS,KACL,MAAMC,EAAQJ,EAAIK,WACZC,EAASN,EAAIO,YACbC,EAAKC,SAASC,iBAAiBN,EAAQ,EAAGE,EAAS,GACzD,IAAKE,EACD,OAEJ,MAAMG,GAAYC,EAAAA,EAAAA,GAAsBJ,GACpCG,GACA,IAAIE,QAASC,IAAYC,EAAAA,EAAAA,GAAiBJ,EAAWG,IAAUE,KAAK,MAChEC,EAAAA,EAAAA,IAAUC,UAQNP,EAAUQ,MAAMC,YAAY,aAAc,gBACpCC,EAAAA,EAAAA,GAAYV,EAAW,KAC7BA,EAAUQ,MAAMG,eAAe,sB", "sources": ["../node_modules/@ionic/core/components/status-tap.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { readTask, writeTask } from '@stencil/core/internal/client';\nimport { a as findClosestIonContent, s as scrollToTop } from './index8.js';\nimport { c as componentOnReady } from './helpers.js';\n\nconst startStatusTap = () => {\n    const win = window;\n    win.addEventListener('statusTap', () => {\n        readTask(() => {\n            const width = win.innerWidth;\n            const height = win.innerHeight;\n            const el = document.elementFromPoint(width / 2, height / 2);\n            if (!el) {\n                return;\n            }\n            const contentEl = findClosestIonContent(el);\n            if (contentEl) {\n                new Promise((resolve) => componentOnReady(contentEl, resolve)).then(() => {\n                    writeTask(async () => {\n                        /**\n                         * If scrolling and user taps status bar,\n                         * only calling scrollToTop is not enough\n                         * as engines like WebKit will jump the\n                         * scroll position back down and complete\n                         * any in-progress momentum scrolling.\n                         */\n                        contentEl.style.setProperty('--overflow', 'hidden');\n                        await scrollToTop(contentEl, 300);\n                        contentEl.style.removeProperty('--overflow');\n                    });\n                });\n            }\n        });\n    });\n};\n\nexport { startStatusTap };\n"], "names": ["startStatusTap", "win", "window", "addEventListener", "readTask", "width", "innerWidth", "height", "innerHeight", "el", "document", "elementFromPoint", "contentEl", "findClosestIonContent", "Promise", "resolve", "componentOnReady", "then", "writeTask", "async", "style", "setProperty", "scrollToTop", "removeProperty"], "sourceRoot": ""}