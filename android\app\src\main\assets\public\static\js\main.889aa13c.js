/*! For license information please see main.889aa13c.js.LICENSE.txt */
(()=>{var e={5:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,o=n?Symbol.for("react.element"):60103,r=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,p=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,b=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,y=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function $(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case o:switch(e=e.type){case u:case d:case i:case s:case a:case h:return e;default:switch(e=e&&e.$$typeof){case c:case f:case g:case m:case l:return e;default:return t}}case r:return t}}}function x(e){return $(e)===d}},43:(e,t,n)=>{"use strict";e.exports=n(202)},123:(e,t,n)=>{var o=n(141);e.exports=m,e.exports.parse=i,e.exports.compile=function(e,t){return c(i(e,t),t)},e.exports.tokensToFunction=c,e.exports.tokensToRegExp=p;var r=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function i(e,t){for(var n,o=[],i=0,s=0,l="",c=t&&t.delimiter||"/";null!=(n=r.exec(e));){var u=n[0],f=n[1],h=n.index;if(l+=e.slice(s,h),s=h+u.length,f)l+=f[1];else{var p=e[s],m=n[2],g=n[3],b=n[4],v=n[5],y=n[6],w=n[7];l&&(o.push(l),l="");var $=null!=m&&null!=p&&p!==m,x="+"===y||"*"===y,k="?"===y||"*"===y,S=m||c,E=b||v,C=m||("string"===typeof o[o.length-1]?o[o.length-1]:"");o.push({name:g||i++,prefix:m||"",delimiter:S,optional:k,repeat:x,partial:$,asterisk:!!w,pattern:E?d(E):w?".*":a(S,C)})}}return s<e.length&&(l+=e.substr(s)),l&&o.push(l),o}function a(e,t){return!t||t.indexOf(e)>-1?"[^"+u(e)+"]+?":u(t)+"|(?:(?!"+u(t)+")[^"+u(e)+"])+?"}function s(e){return encodeURI(e).replace(/[\/?#]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}function l(e){return encodeURI(e).replace(/[?#]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}function c(e,t){for(var n=new Array(e.length),r=0;r<e.length;r++)"object"===typeof e[r]&&(n[r]=new RegExp("^(?:"+e[r].pattern+")$",h(t)));return function(t,r){for(var i="",a=t||{},c=(r||{}).pretty?s:encodeURIComponent,u=0;u<e.length;u++){var d=e[u];if("string"!==typeof d){var f,h=a[d.name];if(null==h){if(d.optional){d.partial&&(i+=d.prefix);continue}throw new TypeError('Expected "'+d.name+'" to be defined')}if(o(h)){if(!d.repeat)throw new TypeError('Expected "'+d.name+'" to not repeat, but received `'+JSON.stringify(h)+"`");if(0===h.length){if(d.optional)continue;throw new TypeError('Expected "'+d.name+'" to not be empty')}for(var p=0;p<h.length;p++){if(f=c(h[p]),!n[u].test(f))throw new TypeError('Expected all "'+d.name+'" to match "'+d.pattern+'", but received `'+JSON.stringify(f)+"`");i+=(0===p?d.prefix:d.delimiter)+f}}else{if(f=d.asterisk?l(h):c(h),!n[u].test(f))throw new TypeError('Expected "'+d.name+'" to match "'+d.pattern+'", but received "'+f+'"');i+=d.prefix+f}}else i+=d}return i}}function u(e){return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function d(e){return e.replace(/([=!:$\/()])/g,"\\$1")}function f(e,t){return e.keys=t,e}function h(e){return e&&e.sensitive?"":"i"}function p(e,t,n){o(t)||(n=t||n,t=[]);for(var r=(n=n||{}).strict,i=!1!==n.end,a="",s=0;s<e.length;s++){var l=e[s];if("string"===typeof l)a+=u(l);else{var c=u(l.prefix),d="(?:"+l.pattern+")";t.push(l),l.repeat&&(d+="(?:"+c+d+")*"),a+=d=l.optional?l.partial?c+"("+d+")?":"(?:"+c+"("+d+"))?":c+"("+d+")"}}var p=u(n.delimiter||"/"),m=a.slice(-p.length)===p;return r||(a=(m?a.slice(0,-p.length):a)+"(?:"+p+"(?=$))?"),a+=i?"$":r&&m?"":"(?="+p+"|$)",f(new RegExp("^"+a,h(n)),t)}function m(e,t,n){return o(t)||(n=t||n,t=[]),n=n||{},e instanceof RegExp?function(e,t){var n=e.source.match(/\((?!\?)/g);if(n)for(var o=0;o<n.length;o++)t.push({name:o,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return f(e,t)}(e,t):o(e)?function(e,t,n){for(var o=[],r=0;r<e.length;r++)o.push(m(e[r],t,n).source);return f(new RegExp("(?:"+o.join("|")+")",h(n)),t)}(e,t,n):function(e,t,n){return p(i(e,n),t,n)}(e,t,n)}},140:e=>{function t(e){return Promise.resolve().then(()=>{var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t})}t.keys=()=>[],t.resolve=t,t.id=140,e.exports=t},141:e=>{e.exports=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)}},153:(e,t,n)=>{"use strict";var o=n(43),r=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var o,i={},c=null,u=null;for(o in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)a.call(t,o)&&!l.hasOwnProperty(o)&&(i[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===i[o]&&(i[o]=t[o]);return{$$typeof:r,type:e,key:c,ref:u,props:i,_owner:s.current}}t.jsx=c,t.jsxs=c},173:(e,t,n)=>{e.exports=n(497)()},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),o=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function v(){}function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=b.prototype;var w=y.prototype=new v;w.constructor=y,m(w,b.prototype),w.isPureReactComponent=!0;var $=Array.isArray,x=Object.prototype.hasOwnProperty,k={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,o){var r,i={},a=null,s=null;if(null!=t)for(r in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)x.call(t,r)&&!S.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(1===l)i.children=o;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];i.children=c}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===i[r]&&(i[r]=l[r]);return{$$typeof:n,type:e,key:a,ref:s,props:i,_owner:k.current}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var T=/\/+/g;function _(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(e,t,r,i,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case o:l=!0}}if(l)return a=a(l=e),e=""===i?"."+_(l,0):i,$(a)?(r="",null!=e&&(r=e.replace(T,"$&/")+"/"),P(a,t,r,"",function(e){return e})):null!=a&&(C(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,r+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(T,"$&/")+"/")+e)),t.push(a)),1;if(l=0,i=""===i?".":i+":",$(e))for(var c=0;c<e.length;c++){var u=i+_(s=e[c],c);l+=P(s,t,r,u,a)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(s=e.next()).done;)l+=P(s=s.value,t,r,u=i+_(s,c++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function L(e,t,n){if(null==e)return e;var o=[],r=0;return P(e,o,"","",function(e){return t.call(n,e,r++)}),o}function R(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var N={current:null},O={transition:null},I={ReactCurrentDispatcher:N,ReactCurrentBatchConfig:O,ReactCurrentOwner:k};function z(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:L,forEach:function(e,t,n){L(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return L(e,function(){t++}),t},toArray:function(e){return L(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=r,t.Profiler=a,t.PureComponent=y,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,t.act=z,t.cloneElement=function(e,t,o){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=m({},e.props),i=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=k.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)x.call(t,c)&&!S.hasOwnProperty(c)&&(r[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)r.children=o;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:n,type:e.type,key:i,ref:a,props:r,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=O.transition;O.transition={};try{e()}finally{O.transition=t}},t.unstable_act=z,t.useCallback=function(e,t){return N.current.useCallback(e,t)},t.useContext=function(e){return N.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return N.current.useDeferredValue(e)},t.useEffect=function(e,t){return N.current.useEffect(e,t)},t.useId=function(){return N.current.useId()},t.useImperativeHandle=function(e,t,n){return N.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return N.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return N.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return N.current.useMemo(e,t)},t.useReducer=function(e,t,n){return N.current.useReducer(e,t,n)},t.useRef=function(e){return N.current.useRef(e)},t.useState=function(e){return N.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return N.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return N.current.useTransition()},t.version="18.3.1"},218:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},219:(e,t,n)=>{"use strict";var o=n(763),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return o.isMemo(e)?a:s[e.$$typeof]||r}s[o.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[o.Memo]=a;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,n,o){if("string"!==typeof n){if(p){var r=h(n);r&&r!==p&&e(t,r,o)}var a=u(n);d&&(a=a.concat(d(n)));for(var s=l(t),m=l(n),g=0;g<a.length;++g){var b=a[g];if(!i[b]&&(!o||!o[b])&&(!m||!m[b])&&(!s||!s[b])){var v=f(n,b);try{c(t,b,v)}catch(y){}}}}return t}},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var o=n-1>>>1,r=e[o];if(!(0<i(r,t)))break e;e[o]=t,e[n]=r,n=o}}function o(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var o=0,r=e.length,a=r>>>1;o<a;){var s=2*(o+1)-1,l=e[s],c=s+1,u=e[c];if(0>i(l,n))c<r&&0>i(u,l)?(e[o]=u,e[c]=n,o=c):(e[o]=l,e[s]=n,o=s);else{if(!(c<r&&0>i(u,n)))break e;e[o]=u,e[c]=n,o=c}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var c=[],u=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,b="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,y="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=o(u);null!==t;){if(null===t.callback)r(u);else{if(!(t.startTime<=e))break;r(u),t.sortIndex=t.expirationTime,n(c,t)}t=o(u)}}function $(e){if(g=!1,w(e),!m)if(null!==o(c))m=!0,O(x);else{var t=o(u);null!==t&&I($,t.startTime-e)}}function x(e,n){m=!1,g&&(g=!1,v(C),C=-1),p=!0;var i=h;try{for(w(n),f=o(c);null!==f&&(!(f.expirationTime>n)||e&&!P());){var a=f.callback;if("function"===typeof a){f.callback=null,h=f.priorityLevel;var s=a(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===o(c)&&r(c),w(n)}else r(c);f=o(c)}if(null!==f)var l=!0;else{var d=o(u);null!==d&&I($,d.startTime-n),l=!1}return l}finally{f=null,h=i,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,S=!1,E=null,C=-1,T=5,_=-1;function P(){return!(t.unstable_now()-_<T)}function L(){if(null!==E){var e=t.unstable_now();_=e;var n=!0;try{n=E(!0,e)}finally{n?k():(S=!1,E=null)}}else S=!1}if("function"===typeof y)k=function(){y(L)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,N=R.port2;R.port1.onmessage=L,k=function(){N.postMessage(null)}}else k=function(){b(L,0)};function O(e){E=e,S||(S=!0,k())}function I(e,n){C=b(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,O(x))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return o(c)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,r,i){var a=t.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?a+i:a:i=a,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:r,priorityLevel:e,startTime:i,expirationTime:s=i+s,sortIndex:-1},i>a?(e.sortIndex=i,n(u,e),null===o(c)&&e===o(u)&&(g?(v(C),C=-1):g=!0,I($,i-a))):(e.sortIndex=s,n(c,e),m||p||(m=!0,O(x))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},286:(e,t,n)=>{"use strict";n.d(t,{d:()=>r,w:()=>o});const o="undefined"!==typeof window?window:void 0,r="undefined"!==typeof document?document:void 0},384:(e,t,n)=>{"use strict";n.d(t,{a:()=>l,b:()=>c,c:()=>o,d:()=>i,i:()=>s,l:()=>r,m:()=>d,p:()=>f,r:()=>u,s:()=>m,u:()=>h,v:()=>p});const o=(e,t)=>{e.componentOnReady?e.componentOnReady().then(e=>t(e)):u(()=>t(e))},r=e=>void 0!==e.componentOnReady,i=function(e){const t={};return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).forEach(n=>{if(e.hasAttribute(n)){null!==e.getAttribute(n)&&(t[n]=e.getAttribute(n)),e.removeAttribute(n)}}),t},a=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],s=(e,t)=>{let n=a;return t&&t.length>0&&(n=n.filter(e=>!t.includes(e))),i(e,n)},l=(e,t,n,o)=>{var r;if("undefined"!==typeof window){const i=window,a=null===(r=null===i||void 0===i?void 0:i.Ionic)||void 0===r?void 0:r.config;if(a){const r=a.get("_ael");if(r)return r(e,t,n,o);if(a._ael)return a._ael(e,t,n,o)}}return e.addEventListener(t,n,o)},c=(e,t,n,o)=>{var r;if("undefined"!==typeof window){const i=window,a=null===(r=null===i||void 0===i?void 0:i.Ionic)||void 0===r?void 0:r.config;if(a){const r=a.get("_rel");if(r)return r(e,t,n,o);if(a._rel)return a._rel(e,t,n,o)}}return e.removeEventListener(t,n,o)},u=e=>"function"===typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(e):"function"===typeof requestAnimationFrame?requestAnimationFrame(e):setTimeout(e),d=(e,t,n)=>Math.max(e,Math.min(t,n)),f=(e,t)=>{if(!e){const e="ASSERT: "+t;throw console.error(e),new Error(e)}},h=e=>e.timeStamp||Date.now(),p=e=>{if(e){const t=e.changedTouches;if(t&&t.length>0){const e=t[0];return{x:e.clientX,y:e.clientY}}if(void 0!==e.pageX)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}},m=(e,t)=>{if(null!==e&&void 0!==e||(e={}),null!==t&&void 0!==t||(t={}),e===t)return!0;const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(const o of n){if(!(o in t))return!1;if(e[o]!==t[o])return!1}return!0}},391:(e,t,n)=>{"use strict";var o=n(950);t.H=o.createRoot,o.hydrateRoot},406:(e,t,n)=>{"use strict";n.d(t,{i:()=>o});const o=e=>e&&""!==e.dir?"rtl"===e.dir.toLowerCase():"rtl"===(null===document||void 0===document?void 0:document.dir.toLowerCase())},434:(e,t,n)=>{"use strict";n.r(t),n.d(t,{MENU_BACK_BUTTON_PRIORITY:()=>c,OVERLAY_BACK_BUTTON_PRIORITY:()=>l,blockHardwareBackButton:()=>a,shouldUseCloseWatcher:()=>i,startHardwareBackButton:()=>s});var o=n(286),r=n(619);const i=()=>r.c.get("experimentalCloseWatcher",!1)&&void 0!==o.w&&"CloseWatcher"in o.w,a=()=>{document.addEventListener("backbutton",()=>{})},s=()=>{const e=document;let t=!1;const n=()=>{if(t)return;let n=0,o=[];const r=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(e,t){o.push({priority:e,handler:t,id:n++})}}});e.dispatchEvent(r);const i=()=>{if(o.length>0){let e={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};o.forEach(t=>{t.priority>=e.priority&&(e=t)}),t=!0,o=o.filter(t=>t.id!==e.id),(async e=>{try{if(null===e||void 0===e?void 0:e.handler){const t=e.handler(i);null!=t&&await t}}catch(t){console.error(t)}})(e).then(()=>t=!1)}};i()};if(i()){let e;const t=()=>{null===e||void 0===e||e.destroy(),e=new o.w.CloseWatcher,e.onclose=()=>{n(),t()}};t()}else e.addEventListener("backbutton",n)},l=100,c=99},435:(e,t,n)=>{"use strict";n.d(t,{b:()=>r,p:()=>o});const o=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return console.warn(`[Ionic Warning]: ${e}`,...n)},r=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return console.error(`<${e.tagName.toLowerCase()}> must be used inside ${n.join(" or ")}.`)}},441:(e,t,n)=>{"use strict";n.d(t,{L2:()=>s,wt:()=>N,xr:()=>et,lh:()=>en,$x:()=>Mn,OX:()=>G,Wi:()=>Zt,h:()=>Ze,w$:()=>Jn,gv:()=>X,iY:()=>Gt,zb:()=>I,bN:()=>Y});var o,r={allRenderFn:!1,element:!0,event:!0,hasRenderFn:!0,hostListener:!0,hostListenerTargetWindow:!0,hostListenerTargetDocument:!0,hostListenerTargetBody:!0,hostListenerTargetParent:!1,hostListenerTarget:!0,member:!0,method:!0,mode:!0,observeAttribute:!0,prop:!0,propMutable:!0,reflect:!0,scoped:!0,shadowDom:!0,slot:!0,cssAnnotations:!0,state:!0,style:!0,formAssociated:!1,svg:!0,updatable:!0,vdomAttribute:!0,vdomXlink:!0,vdomClass:!0,vdomFunctional:!0,vdomKey:!0,vdomListener:!0,vdomRef:!0,vdomPropOrAttr:!0,vdomRender:!0,vdomStyle:!0,vdomText:!0,watchCallback:!0,taskQueue:!0,hotModuleReplacement:!1,isDebug:!1,isDev:!1,isTesting:!1,hydrateServerSide:!1,hydrateClientSide:!1,lifecycleDOMEvents:!1,lazyLoad:!1,profile:!1,slotRelocation:!0,appendChildSlotFix:!1,cloneNodeFix:!1,hydratedAttribute:!1,hydratedClass:!0,scriptDataOpts:!1,scopedSlotTextContentFix:!1,shadowDomShim:!1,slotChildNodesFix:!1,invisiblePrehydration:!0,propBoolean:!0,propNumber:!0,propString:!0,constructableCSS:!0,devTools:!1,shadowDelegatesFocus:!0,initializeNextTick:!1,asyncLoading:!0,asyncQueue:!1,transformTagName:!1,attachStyles:!0,experimentalSlotFixes:!1},i="app",a=Object.defineProperty,s={isDev:!!r.isDev,isBrowser:!0,isServer:!1,isTesting:!!r.isTesting},l=(e=>(e.Undefined="undefined",e.Null="null",e.String="string",e.Number="number",e.SpecialNumber="number",e.Boolean="boolean",e.BigInt="bigint",e))(l||{}),c=(e=>(e.Array="array",e.Date="date",e.Map="map",e.Object="object",e.RegularExpression="regexp",e.Set="set",e.Channel="channel",e.Symbol="symbol",e))(c||{}),u="type",d="value",f="serialized:",h=(e,t)=>{var n;const o=t.$cmpMeta$;Object.entries(null!=(n=o.$members$)?n:{}).map(n=>{let[o,[i]]=n;if((r.state||r.prop)&&(31&i||32&i)){const n=e[o],r=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(e),o);Object.defineProperty(e,o,{get(){return r.get.call(this)},set(e){r.set.call(this,e)},configurable:!0,enumerable:!0}),e[o]=t.$instanceValues$.has(o)?t.$instanceValues$.get(o):n}})},p=e=>{if(e.__stencil__getHostRef)return e.__stencil__getHostRef()},m=(e,t)=>{const n={$flags$:0,$hostElement$:e,$cmpMeta$:t,$instanceValues$:new Map};r.isDev&&(n.$renderCount$=0),r.method&&r.lazyLoad&&(n.$onInstancePromise$=new Promise(e=>n.$onInstanceResolve$=e)),r.asyncLoading&&(n.$onReadyPromise$=new Promise(e=>n.$onReadyResolve$=e),e["s-p"]=[],e["s-rc"]=[]);const o=n;return e.__stencil__getHostRef=()=>o,!r.lazyLoad&&r.modernPropertyDecls&&(r.state||r.prop)&&h(e,n),o},g=(e,t)=>t in e,b=(e,t)=>(o||console.error)(e,t),v=r.isTesting?["STENCIL:"]:["%cstencil","color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px"],y=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return console.error(...v,...t)},w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return console.warn(...v,...t)},$=new Map,x=new Map,k=[],S="s",E="s-id",C="sty-id",T="c-id",_="slot-fb{display:contents}slot-fb[hidden]{display:none}",P="http://www.w3.org/1999/xlink",L=["formAssociatedCallback","formResetCallback","formDisabledCallback","formStateRestoreCallback"],R="undefined"!==typeof window?window:{},N=R.HTMLElement||class{},O={$flags$:0,$resourcesUrl$:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,n,o)=>e.addEventListener(t,n,o),rel:(e,t,n,o)=>e.removeEventListener(t,n,o),ce:(e,t)=>new CustomEvent(e,t)},I=e=>{Object.assign(O,e)},z=r.shadowDom,D=(()=>{var e;let t=!1;try{null==(e=R.document)||e.addEventListener("e",null,Object.defineProperty({},"passive",{get(){t=!0}}))}catch(n){}return t})(),A=!!r.constructableCSS&&(()=>{try{return new CSSStyleSheet,"function"===typeof(new CSSStyleSheet).replaceSync}catch(e){}return!1})(),M=!!A&&(()=>!!R.document&&Object.getOwnPropertyDescriptor(R.document.adoptedStyleSheets,"length").writable)(),B=0,V=!1,j=[],H=[],F=[],W=(e,t)=>n=>{e.push(n),V||(V=!0,t&&4&O.$flags$?K(Q):O.raf(Q))},U=e=>{for(let n=0;n<e.length;n++)try{e[n](performance.now())}catch(t){b(t)}e.length=0},q=(e,t)=>{let n=0,o=0;for(;n<e.length&&(o=performance.now())<t;)try{e[n++](o)}catch(r){b(r)}n===e.length?e.length=0:0!==n&&e.splice(0,n)},Q=()=>{if(r.asyncQueue&&B++,U(j),r.asyncQueue){const e=2===(6&O.$flags$)?performance.now()+14*Math.ceil(.1*B):1/0;q(H,e),q(F,e),H.length>0&&(F.push(...H),H.length=0),(V=j.length+H.length+F.length>0)?O.raf(Q):B=0}else U(H),(V=j.length>0)&&O.raf(Q)},K=e=>{return Promise.resolve(t).then(e);var t},X=W(j,!1),Y=W(H,!0),G=e=>{const t=new URL(e,O.$resourcesUrl$);return t.origin!==R.location.origin?t.href:t.pathname},Z=e=>"object"===(e=typeof e)||"function"===e;function J(e){var t,n,o;return null!=(o=null==(n=null==(t=e.head)?void 0:t.querySelector('meta[name="csp-nonce"]'))?void 0:n.getAttribute("content"))?o:void 0}var ee=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),te=class e{static fromLocalValue(t){const n=t[u],o=d in t?t[d]:void 0;switch(n){case"string":case"boolean":return o;case"bigint":return BigInt(o);case"undefined":return;case"null":return null;case"number":return"NaN"===o?NaN:"-0"===o?-0:"Infinity"===o?1/0:"-Infinity"===o?-1/0:o;case"array":return o.map(t=>e.fromLocalValue(t));case"date":return new Date(o);case"map":const t=new Map;for(const[n,l]of o){const o="object"===typeof n&&null!==n?e.fromLocalValue(n):n,r=e.fromLocalValue(l);t.set(o,r)}return t;case"object":const r={};for(const[n,l]of o)r[n]=e.fromLocalValue(l);return r;case"regexp":const{pattern:i,flags:a}=o;return new RegExp(i,a);case"set":const s=new Set;for(const n of o)s.add(e.fromLocalValue(n));return s;case"symbol":return Symbol(o);default:throw new Error(`Unsupported type: ${n}`)}}static fromLocalValueArray(t){return t.map(t=>e.fromLocalValue(t))}static isLocalValueObject(e){if("object"!==typeof e||null===e)return!1;if(!e.hasOwnProperty(u))return!1;const t=e[u];return!!Object.values({...l,...c}).includes(t)&&("null"===t||"undefined"===t||e.hasOwnProperty(d))}};((e,t)=>{for(var n in t)a(e,n,{get:t[n],enumerable:!0})})({},{err:()=>oe,map:()=>re,ok:()=>ne,unwrap:()=>ae,unwrapErr:()=>se});var ne=e=>({isOk:!0,isErr:!1,value:e}),oe=e=>({isOk:!1,isErr:!0,value:e});function re(e,t){if(e.isOk){const n=t(e.value);return n instanceof Promise?n.then(e=>ne(e)):ne(n)}if(e.isErr){const t=e.value;return oe(t)}throw"should never get here"}var ie,ae=e=>{if(e.isOk)return e.value;throw e.value},se=e=>{if(e.isErr)return e.value;throw e.value};function le(e){var t;const n=r.shadowDelegatesFocus?this.attachShadow({mode:"open",delegatesFocus:!!(16&e.$flags$)}):this.attachShadow({mode:"open"});void 0===ie&&(ie=null!=(t=function(e){if(!e||!A)return;const t=new CSSStyleSheet;return t.replaceSync(e),t}(""))?t:null),ie&&(M?n.adoptedStyleSheets.push(ie):n.adoptedStyleSheets=[...n.adoptedStyleSheets,ie])}var ce=e=>t=>e(t.toLowerCase()),ue=ce(e=>e.endsWith(".d.ts")||e.endsWith(".d.mts")||e.endsWith(".d.cts")),de=(ce(e=>!ue(e)&&(e.endsWith(".ts")||e.endsWith(".mts")||e.endsWith(".cts"))),ce(e=>e.endsWith(".tsx")||e.endsWith(".mtsx")||e.endsWith(".ctsx")),ce(e=>e.endsWith(".jsx")||e.endsWith(".mjsx")||e.endsWith(".cjsx")),ce(e=>e.endsWith(".js")||e.endsWith(".mjs")||e.endsWith(".cjs")),e=>{const t=He(e,"childNodes");e.tagName&&e.tagName.includes("-")&&e["s-cr"]&&"SLOT-FB"!==e.tagName&&he(t,e.tagName).forEach(e=>{1===e.nodeType&&"SLOT-FB"===e.tagName&&(pe(e,be(e),!1).length?e.hidden=!0:e.hidden=!1)});let n=0;for(n=0;n<t.length;n++){const e=t[n];1===e.nodeType&&He(e,"childNodes").length&&de(e)}}),fe=e=>{const t=[];for(let n=0;n<e.length;n++){const o=e[n]["s-nr"]||void 0;o&&o.isConnected&&t.push(o)}return t};function he(e,t,n){let o,r=0,i=[];for(;r<e.length;r++){if(o=e[r],o["s-sr"]&&(!t||o["s-hn"]===t)&&(void 0===n||be(o)===n)&&(i.push(o),"undefined"!==typeof n))return i;i=[...i,...he(o.childNodes,t,n)]}return i}var pe=function(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const o=[];(n&&e["s-sr"]||!e["s-sr"])&&o.push(e);let r=e;for(;r=r.nextSibling;)be(r)!==t||!n&&r["s-sr"]||o.push(r);return o},me=(e,t)=>1===e.nodeType?null===e.getAttribute("slot")&&""===t||e.getAttribute("slot")===t:e["s-sn"]===t||""===t,ge=(e,t,n,o)=>{if(e["s-ol"]&&e["s-ol"].isConnected)return;const i=document.createTextNode("");if(i["s-nr"]=e,!t["s-cr"]||!t["s-cr"].parentNode)return;const a=t["s-cr"].parentNode,s=He(a,n?"prepend":"appendChild");if(r.hydrateClientSide&&"undefined"!==typeof o){i["s-oo"]=o;const e=He(a,"childNodes"),t=[i];e.forEach(e=>{e["s-nr"]&&t.push(e)}),t.sort((e,t)=>!e["s-oo"]||e["s-oo"]<(t["s-oo"]||0)?-1:!t["s-oo"]||t["s-oo"]<e["s-oo"]?1:0),t.forEach(e=>s.call(a,e))}else s.call(a,i);e["s-ol"]=i,e["s-sh"]=t["s-hn"]},be=e=>"string"===typeof e["s-sn"]?e["s-sn"]:1===e.nodeType&&e.getAttribute("slot")||void 0;function ve(e){if(e.assignedElements||e.assignedNodes||!e["s-sr"])return;const t=t=>function(e){const n=[],o=this["s-sn"];(null==e?void 0:e.flatten)&&console.error("\n          Flattening is not supported for Stencil non-shadow slots.\n          You can use `.childNodes` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        ");const r=this["s-cr"].parentElement;return(r.__childNodes?r.childNodes:fe(r.childNodes)).forEach(e=>{o===be(e)&&n.push(e)}),t?n.filter(e=>1===e.nodeType):n}.bind(e);e.assignedElements=t(!0),e.assignedNodes=t(!1)}function ye(e){e.dispatchEvent(new CustomEvent("slotchange",{bubbles:!1,cancelable:!1,composed:!1}))}function we(e,t){var n;if(!(t=t||(null==(n=e["s-ol"])?void 0:n.parentElement)))return{slotNode:null,slotName:""};const o=e["s-sn"]=be(e)||"";return{slotNode:he(He(t,"childNodes"),t.tagName,o)[0],slotName:o}}var $e=e=>{xe(e),ke(e),Ce(e),Ee(e),Le(e),Te(e),_e(e),Pe(e),Re(e),Ne(e),Se(e)},xe=e=>{const t=e.cloneNode;e.cloneNode=function(e){const n=!!r.shadowDom&&(this.shadowRoot&&z),o=t.call(this,!!n&&e);if(r.slot&&!n&&e){let e,t,n=0;const i=["s-id","s-cr","s-lr","s-rc","s-sc","s-p","s-cn","s-sr","s-sn","s-hn","s-ol","s-nr","s-si","s-rf","s-scs"],a=this.__childNodes||this.childNodes;for(;n<a.length;n++)e=a[n]["s-nr"],t=i.every(e=>!a[n][e]),e&&(r.appendChildSlotFix&&o.__appendChild?o.__appendChild(e.cloneNode(!0)):o.appendChild(e.cloneNode(!0))),t&&o.appendChild(a[n].cloneNode(!0))}return o}},ke=e=>{e.__appendChild=e.appendChild,e.appendChild=function(e){const{slotName:t,slotNode:n}=we(e,this);if(n){ge(e,n);const o=pe(n,t),r=o[o.length-1],i=He(r,"parentNode"),a=He(i,"insertBefore")(e,r.nextSibling);return ye(n),de(this),a}return this.__appendChild(e)}},Se=e=>{e.__removeChild=e.removeChild,e.removeChild=function(e){if(e&&"undefined"!==typeof e["s-sn"]){if(he(this.__childNodes||this.childNodes,this.tagName,e["s-sn"])&&e.isConnected)return e.remove(),void de(this)}return this.__removeChild(e)}},Ee=e=>{e.__prepend=e.prepend,e.prepend=function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];n.forEach(t=>{"string"===typeof t&&(t=this.ownerDocument.createTextNode(t));const n=(t["s-sn"]=be(t))||"",o=he(He(this,"childNodes"),this.tagName,n)[0];if(o){ge(t,o,!0);const e=pe(o,n)[0],r=He(e,"parentNode"),i=He(r,"insertBefore")(t,He(e,"nextSibling"));return ye(o),i}return 1===t.nodeType&&t.getAttribute("slot")&&(t.hidden=!0),e.__prepend(t)})}},Ce=e=>{e.__append=e.append,e.append=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t.forEach(e=>{"string"===typeof e&&(e=this.ownerDocument.createTextNode(e)),this.appendChild(e)})}},Te=e=>{const t=e.insertAdjacentHTML;e.insertAdjacentHTML=function(e,n){if("afterbegin"!==e&&"beforeend"!==e)return t.call(this,e,n);const o=this.ownerDocument.createElement("_");let r;if(o.innerHTML=n,"afterbegin"===e)for(;r=o.firstChild;)this.prepend(r);else if("beforeend"===e)for(;r=o.firstChild;)this.append(r)}},_e=e=>{e.insertAdjacentText=function(e,t){this.insertAdjacentHTML(e,t)}},Pe=e=>{const t=e;t.__insertBefore||(t.__insertBefore=e.insertBefore,e.insertBefore=function(e,t){const{slotName:n,slotNode:o}=we(e,this),r=this.__childNodes?this.childNodes:fe(this.childNodes);if(o){let i=!1;if(r.forEach(r=>{if(r!==t&&null!==t);else{if(i=!0,null===t||n!==t["s-sn"])return void this.appendChild(e);if(n===t["s-sn"]){ge(e,o);const n=He(t,"parentNode");He(n,"insertBefore")(e,t),ye(o)}}}),i)return e}const i=null==t?void 0:t.__parentNode;return i&&!this.isSameNode(i)?this.appendChild(e):this.__insertBefore(e,t)})},Le=e=>{const t=e.insertAdjacentElement;e.insertAdjacentElement=function(e,n){return"afterbegin"!==e&&"beforeend"!==e?t.call(this,e,n):"afterbegin"===e?(this.prepend(n),n):"beforeend"===e?(this.append(n),n):n}},Re=e=>{je("textContent",e),Object.defineProperty(e,"textContent",{get:function(){let e="";return(this.__childNodes?this.childNodes:fe(this.childNodes)).forEach(t=>e+=t.textContent||""),e},set:function(e){(this.__childNodes?this.childNodes:fe(this.childNodes)).forEach(e=>{e["s-ol"]&&e["s-ol"].remove(),e.remove()}),this.insertAdjacentHTML("beforeend",e)}})},Ne=e=>{class t extends Array{item(e){return this[e]}}je("children",e),Object.defineProperty(e,"children",{get(){return this.childNodes.filter(e=>1===e.nodeType)}}),Object.defineProperty(e,"childElementCount",{get(){return this.children.length}}),je("firstChild",e),Object.defineProperty(e,"firstChild",{get(){return this.childNodes[0]}}),je("lastChild",e),Object.defineProperty(e,"lastChild",{get(){return this.childNodes[this.childNodes.length-1]}}),je("childNodes",e),Object.defineProperty(e,"childNodes",{get(){const e=new t;return e.push(...fe(this.__childNodes)),e}})},Oe=e=>{e&&void 0===e.__nextSibling&&globalThis.Node&&(Ie(e),De(e),Me(e),e.nodeType===Node.ELEMENT_NODE&&(ze(e),Ae(e)))},Ie=e=>{e&&!e.__nextSibling&&(je("nextSibling",e),Object.defineProperty(e,"nextSibling",{get:function(){var e;const t=null==(e=this["s-ol"])?void 0:e.parentNode.childNodes,n=null==t?void 0:t.indexOf(this);return t&&n>-1?t[n+1]:this.__nextSibling}}))},ze=e=>{e&&!e.__nextElementSibling&&(je("nextElementSibling",e),Object.defineProperty(e,"nextElementSibling",{get:function(){var e;const t=null==(e=this["s-ol"])?void 0:e.parentNode.children,n=null==t?void 0:t.indexOf(this);return t&&n>-1?t[n+1]:this.__nextElementSibling}}))},De=e=>{e&&!e.__previousSibling&&(je("previousSibling",e),Object.defineProperty(e,"previousSibling",{get:function(){var e;const t=null==(e=this["s-ol"])?void 0:e.parentNode.childNodes,n=null==t?void 0:t.indexOf(this);return t&&n>-1?t[n-1]:this.__previousSibling}}))},Ae=e=>{e&&!e.__previousElementSibling&&(je("previousElementSibling",e),Object.defineProperty(e,"previousElementSibling",{get:function(){var e;const t=null==(e=this["s-ol"])?void 0:e.parentNode.children,n=null==t?void 0:t.indexOf(this);return t&&n>-1?t[n-1]:this.__previousElementSibling}}))},Me=e=>{e&&!e.__parentNode&&(je("parentNode",e),Object.defineProperty(e,"parentNode",{get:function(){var e;return(null==(e=this["s-ol"])?void 0:e.parentNode)||this.__parentNode},set:function(e){this.__parentNode=e}}))},Be=["children","nextElementSibling","previousElementSibling"],Ve=["childNodes","firstChild","lastChild","nextSibling","previousSibling","textContent","parentNode"];function je(e,t){if(!globalThis.Node||!globalThis.Element)return;let n;Be.includes(e)?n=Object.getOwnPropertyDescriptor(Element.prototype,e):Ve.includes(e)&&(n=Object.getOwnPropertyDescriptor(Node.prototype,e)),n||(n=Object.getOwnPropertyDescriptor(t,e)),n&&Object.defineProperty(t,"__"+e,n)}function He(e,t){if("__"+t in e){const n=e["__"+t];return"function"!==typeof n?n:n.bind(e)}return"function"!==typeof e[t]?e[t]:e[t].bind(e)}var Fe=0,We=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(r.profile&&performance.mark){const n=`st:${e}:${t}:${Fe++}`;return performance.mark(n),()=>performance.measure(`[Stencil] ${e}() <${t}>`,n)}return()=>{}},Ue=new WeakMap,qe=(e,t,n)=>{let o=x.get(e);A&&n?(o=o||new CSSStyleSheet,"string"===typeof o?o=t:o.replaceSync(t)):o=t,x.set(e,o)},Qe=(e,t,n)=>{var o;const i=Xe(t,n),a=x.get(i);if(!r.attachStyles||!R.document)return i;if(e=11===e.nodeType?e:R.document,a)if("string"===typeof a){e=e.head||e;let n,s=Ue.get(e);if(s||Ue.set(e,s=new Set),!s.has(i)){if(r.hydrateClientSide&&e.host&&(n=e.querySelector(`[${C}="${i}"]`)))n.innerHTML=a;else{n=R.document.createElement("style"),n.innerHTML=a;const s=null!=(o=O.$nonce$)?o:J(R.document);if(null!=s&&n.setAttribute("nonce",s),(r.hydrateServerSide||r.hotModuleReplacement)&&(2&t.$flags$||128&t.$flags$)&&n.setAttribute(C,i),!(1&t.$flags$))if("HEAD"===e.nodeName){const t=e.querySelectorAll("link[rel=preconnect]"),o=t.length>0?t[t.length-1].nextSibling:e.querySelector("style");e.insertBefore(n,(null==o?void 0:o.parentNode)===e?o:null)}else if("host"in e)if(A){const t=new CSSStyleSheet;t.replaceSync(a),M?e.adoptedStyleSheets.unshift(t):e.adoptedStyleSheets=[t,...e.adoptedStyleSheets]}else{const t=e.querySelector("style");t?t.innerHTML=a+t.innerHTML:e.prepend(n)}else e.append(n);1&t.$flags$&&e.insertBefore(n,null)}4&t.$flags$&&(n.innerHTML+=_),s&&s.add(i)}}else r.constructableCSS&&!e.adoptedStyleSheets.includes(a)&&(M?e.adoptedStyleSheets.push(a):e.adoptedStyleSheets=[...e.adoptedStyleSheets,a]);return i},Ke=e=>{const t=e.$cmpMeta$,n=e.$hostElement$,o=t.$flags$,i=We("attachStyles",t.$tagName$),a=Qe(r.shadowDom&&z&&n.shadowRoot?n.shadowRoot:n.getRootNode(),t,e.$modeName$);(r.shadowDom||r.scoped)&&r.cssAnnotations&&10&o&&(n["s-sc"]=a,n.classList.add(a+"-h")),i()},Xe=(e,t)=>"sc-"+(r.mode&&t&&32&e.$flags$?e.$tagName$+"-"+t:e.$tagName$),Ye=e=>e.replace(/\/\*!@([^\/]+)\*\/[^\{]+\{/g,"$1{"),Ge=()=>{if(!R.document)return;const e=R.document.querySelectorAll(`[${C}]`);let t=0;for(;t<e.length;t++)qe(e[t].getAttribute(C),Ye(e[t].innerHTML),!0)},Ze=function(e,t){let n=null,o=null,i=null,a=!1,s=!1;const l=[],c=t=>{for(let o=0;o<t.length;o++)n=t[o],Array.isArray(n)?c(n):null!=n&&"boolean"!==typeof n&&((a="function"!==typeof e&&!Z(n))?n=String(n):r.isDev&&"function"!==typeof e&&void 0===n.$flags$&&y("vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects."),a&&s?l[l.length-1].$text$+=n:l.push(a?Je(null,n):n),s=a)};for(var u=arguments.length,d=new Array(u>2?u-2:0),f=2;f<u;f++)d[f-2]=arguments[f];if(c(d),t&&(r.isDev&&"input"===e&&it(t),r.vdomKey&&t.key&&(o=t.key),r.slotRelocation&&t.name&&(i=t.name),r.vdomClass)){const e=t.className||t.class;e&&(t.class="object"!==typeof e?e:Object.keys(e).filter(t=>e[t]).join(" "))}if(r.isDev&&l.some(tt)&&y("The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function."),r.vdomFunctional&&"function"===typeof e)return e(null===t?{}:t,l,nt);const h=Je(e,null);return h.$attrs$=t,l.length>0&&(h.$children$=l),r.vdomKey&&(h.$key$=o),r.slotRelocation&&(h.$name$=i),h},Je=(e,t)=>{const n={$flags$:0,$tag$:e,$text$:t,$elm$:null,$children$:null};return r.vdomAttribute&&(n.$attrs$=null),r.vdomKey&&(n.$key$=null),r.slotRelocation&&(n.$name$=null),n},et={},tt=e=>e&&e.$tag$===et,nt={forEach:(e,t)=>e.map(ot).forEach(t),map:(e,t)=>e.map(ot).map(t).map(rt)},ot=e=>({vattrs:e.$attrs$,vchildren:e.$children$,vkey:e.$key$,vname:e.$name$,vtag:e.$tag$,vtext:e.$text$}),rt=e=>{if("function"===typeof e.vtag){const t={...e.vattrs};return e.vkey&&(t.key=e.vkey),e.vname&&(t.name=e.vname),Ze(e.vtag,t,...e.vchildren||[])}const t=Je(e.vtag,e.vtext);return t.$attrs$=e.vattrs,t.$children$=e.vchildren,t.$key$=e.vkey,t.$name$=e.vname,t},it=e=>{const t=Object.keys(e),n=t.indexOf("value");if(-1===n)return;const o=t.indexOf("type"),r=t.indexOf("min"),i=t.indexOf("max"),a=t.indexOf("step");(n<o||n<r||n<i||n<a)&&w('The "value" prop of <input> should be set after "min", "max", "type" and "step"')},at=function(e,t,n,o,i,a,s){let l,c,u,d,f=arguments.length>7&&void 0!==arguments[7]?arguments[7]:[];const h=i["s-sc"];if(1===a.nodeType){if(l=a.getAttribute(T),l&&(c=l.split("."),c[0]===s||"0"===c[0])){u=lt({$flags$:0,$hostId$:c[0],$nodeId$:c[1],$depth$:c[2],$index$:c[3],$tag$:a.tagName.toLowerCase(),$elm$:a,$attrs$:{class:a.className||""}}),t.push(u),a.removeAttribute(T),e.$children$||(e.$children$=[]),r.scoped&&h&&c[0]===s&&(a["s-si"]=h,u.$attrs$.class+=" "+h);const i=u.$elm$.getAttribute("s-sn");"string"===typeof i&&("slot-fb"===u.$tag$&&(ct(i,c[2],u,a,e,t,n,o,f),r.scoped&&h&&a.classList.add(h)),u.$elm$["s-sn"]=i,u.$elm$.removeAttribute("s-sn")),void 0!==u.$index$&&(e.$children$[u.$index$]=u),e=u,o&&"0"===u.$depth$&&(o[u.$index$]=u.$elm$)}if(a.shadowRoot)for(d=a.shadowRoot.childNodes.length-1;d>=0;d--)at(e,t,n,o,i,a.shadowRoot.childNodes[d],s,f);const p=a.__childNodes||a.childNodes;for(d=p.length-1;d>=0;d--)at(e,t,n,o,i,p[d],s,f)}else if(8===a.nodeType){if(c=a.nodeValue.split("."),c[1]===s||"0"===c[1])if(l=c[0],u=lt({$hostId$:c[1],$nodeId$:c[2],$depth$:c[3],$index$:c[4]||"0",$elm$:a,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null}),"t"===l)u.$elm$=pt(a,3),u.$elm$&&3===u.$elm$.nodeType&&(u.$text$=u.$elm$.textContent,t.push(u),a.remove(),s===u.$hostId$&&(e.$children$||(e.$children$=[]),e.$children$[u.$index$]=u),o&&"0"===u.$depth$&&(o[u.$index$]=u.$elm$));else if("c"===l)u.$elm$=pt(a,8),u.$elm$&&8===u.$elm$.nodeType&&(t.push(u),a.remove());else if(u.$hostId$===s)if(l===S){ct(a["s-sn"]=c[5]||"",c[2],u,a,e,t,n,o,f)}else"r"===l&&(r.shadowDom&&o?a.remove():r.slotRelocation&&(i["s-cr"]=a,a["s-cn"]=!0))}else if(e&&"style"===e.$tag$){const t=Je(null,a.textContent);t.$elm$=a,t.$index$="0",e.$children$=[t]}else 3!==a.nodeType||a.wholeText.trim()||a["s-nr"]||a.remove();return e},st=(e,t)=>{if(1===e.nodeType){const n=e[E]||e.getAttribute(E);n&&t.set(n,e);let o=0;if(e.shadowRoot)for(;o<e.shadowRoot.childNodes.length;o++)st(e.shadowRoot.childNodes[o],t);const r=e.__childNodes||e.childNodes;for(o=0;o<r.length;o++)st(r[o],t)}else if(8===e.nodeType){const n=e.nodeValue.split(".");"o"===n[0]&&(t.set(n[1]+"."+n[2],e),e.nodeValue="",e["s-en"]=n[3])}},lt=e=>({$flags$:0,$hostId$:null,$nodeId$:null,$depth$:null,$index$:"0",$elm$:null,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null,...e});function ct(e,t,n,o,i,a,s,l,c){o["s-sr"]=!0,n.$name$=e||null,n.$tag$="slot";const u=(null==i?void 0:i.$elm$)?i.$elm$["s-id"]||i.$elm$.getAttribute("s-id"):"";if(r.shadowDom&&l&&R.document){const r=n.$elm$=R.document.createElement(n.$tag$);n.$name$&&n.$elm$.setAttribute("name",e),i.$elm$.shadowRoot&&u&&u!==n.$hostId$?He(i.$elm$,"insertBefore")(r,He(i.$elm$,"children")[0]):He(He(o,"parentNode"),"insertBefore")(r,o),ht(c,t,e,o,n.$hostId$),o.remove(),"0"===n.$depth$&&(l[n.$index$]=n.$elm$)}else{const r=n.$elm$,a=u&&u!==n.$hostId$&&i.$elm$.shadowRoot;ht(c,t,e,o,a?u:n.$hostId$),ve(o),a&&i.$elm$.insertBefore(r,i.$elm$.children[0])}a.push(n),s.push(n),i.$children$||(i.$children$=[]),i.$children$[n.$index$]=n}var ut,dt,ft,ht=(e,t,n,o,r)=>{var i,a;let s=o.nextSibling;if(e[t]=e[t]||[],s&&!(null==(i=s.nodeValue)?void 0:i.startsWith("s.")))do{!s||(s.getAttribute&&s.getAttribute("slot")||s["s-sn"])!==n&&(""!==n||s["s-sn"]||s.getAttribute&&s.getAttribute("slot")||8!==s.nodeType&&3!==s.nodeType)||(s["s-sn"]=n,e[t].push({slot:o,node:s,hostId:r})),s=null==s?void 0:s.nextSibling}while(s&&!(null==(a=s.nodeValue)?void 0:a.startsWith("s.")))},pt=(e,t)=>{let n=e;do{n=n.nextSibling}while(n&&(n.nodeType!==t||!n.nodeValue));return n},mt="-shadowcsshost",gt="-shadowcssslotted",bt="-shadowcsscontext",vt=")(?:\\(((?:\\([^)(]*\\)|[^)(]*)+?)\\))?([^,{]*)",yt=new RegExp("("+mt+vt,"gim"),wt=new RegExp("("+bt+vt,"gim"),$t=new RegExp("("+gt+vt,"gim"),xt=mt+"-no-combinator",kt=/-shadowcsshost-no-combinator([^\s]*)/,St=[/::shadow/g,/::content/g],Et=/__part-(\d+)__/g,Ct=/-shadowcsshost/gim,Tt=e=>{const t=ee(e);return new RegExp(`(^|[^@]|@(?!supports\\s+selector\\s*\\([^{]*?${t}))(${t}\\b)`,"g")},_t=Tt("::slotted"),Pt=Tt(":host"),Lt=Tt(":host-context"),Rt=/\/\*\s*[\s\S]*?\*\//g,Nt=/\/\*\s*#\s*source(Mapping)?URL=[\s\S]+?\*\//g,Ot=/(\s*)([^;\{\}]+?)(\s*)((?:{%BLOCK%}?\s*;?)|(?:\s*;))/g,It=/([{}])/g,zt=/(^.*?[^\\])??((:+)(.*)|$)/,Dt="%BLOCK%",At=(e,t)=>{const n=Mt(e);let o=0;return n.escapedString.replace(Ot,function(){const e=arguments.length<=2?void 0:arguments[2];let r="",i=arguments.length<=4?void 0:arguments[4],a="";i&&i.startsWith("{"+Dt)&&(r=n.blocks[o++],i=i.substring(8),a="{");const s=t({selector:e,content:r});return`${arguments.length<=1?void 0:arguments[1]}${s.selector}${arguments.length<=3?void 0:arguments[3]}${a}${s.content}${i}`})},Mt=e=>{const t=e.split(It),n=[],o=[];let r=0,i=[];for(let a=0;a<t.length;a++){const e=t[a];"}"===e&&r--,r>0?i.push(e):(i.length>0&&(o.push(i.join("")),n.push(Dt),i=[]),n.push(e)),"{"===e&&r++}i.length>0&&(o.push(i.join("")),n.push(Dt));return{escapedString:n.join(""),blocks:o}},Bt=(e,t,n)=>e.replace(t,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];if(t[2]){const e=t[2].split(","),o=[];for(let r=0;r<e.length;r++){const i=e[r].trim();if(!i)break;o.push(n(xt,i,t[3]))}return o.join(",")}return xt+t[3]}),Vt=(e,t,n)=>e+t.replace(mt,"")+n,jt=(e,t,n)=>t.indexOf(mt)>-1?Vt(e,t,n):e+t+n+", "+t+" "+e+n,Ht=(e,t)=>{const n=(e=>(e=e.replace(/\[/g,"\\[").replace(/\]/g,"\\]"),new RegExp("^("+e+")([>\\s~+[.,{:][\\s\\S]*)?$","m")))(t);return!n.test(e)},Ft=(e,t)=>e.replace(zt,function(e){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"")+t+(arguments.length>3&&void 0!==arguments[3]?arguments[3]:"")+(arguments.length>4&&void 0!==arguments[4]?arguments[4]:"")}),Wt=(e,t,n)=>{const o="."+(t=t.replace(/\[is=([^\]]*)\]/g,function(e){return arguments.length<=1?void 0:arguments[1]})),r=e=>{let r=e.trim();if(!r)return"";if(e.indexOf(xt)>-1)r=((e,t,n)=>{if(Ct.lastIndex=0,Ct.test(e)){const t=`.${n}`;return e.replace(kt,(e,n)=>Ft(n,t)).replace(Ct,t+" ")}return t+" "+e})(e,t,n);else{const t=e.replace(Ct,"");t.length>0&&(r=Ft(t,o))}return r},i=(e=>{const t=[];let n=0;return{content:(e=(e=e.replace(/(\[\s*part~=\s*("[^"]*"|'[^']*')\s*\])/g,(e,o)=>{const r=`__part-${n}__`;return t.push(o),n++,r})).replace(/(\[[^\]]*\])/g,(e,o)=>{const r=`__ph-${n}__`;return t.push(o),n++,r})).replace(/(:nth-[-\w]+)(\([^)]+\))/g,(e,o,r)=>{const i=`__ph-${n}__`;return t.push(r),n++,o+i}),placeholders:t}})(e);let a,s="",l=0;const c=/( |>|\+|~(?!=))(?=(?:[^()]*\([^()]*\))*[^()]*$)\s*/g;let u=!((e=i.content).indexOf(xt)>-1);for(;null!==(a=c.exec(e));){const t=a[1],n=e.slice(l,a.index).trim();u=u||n.indexOf(xt)>-1;s+=`${u?r(n):n} ${t} `,l=c.lastIndex}const d=e.substring(l);return u=!d.match(Et)&&(u||d.indexOf(xt)>-1),s+=u?r(d):d,f=i.placeholders,s.replace(/__part-(\d+)__/g,(e,t)=>f[+t]).replace(/__ph-(\d+)__/g,(e,t)=>f[+t]);var f},Ut=(e,t,n,o,r)=>At(e,e=>{let i=e.selector,a=e.content;"@"!==e.selector[0]?i=((e,t,n,o)=>e.split(",").map(e=>o&&e.indexOf("."+o)>-1?e.trim():Ht(e,t)?Wt(e,t,n).trim():e.trim()).join(", "))(e.selector,t,n,o):(e.selector.startsWith("@media")||e.selector.startsWith("@supports")||e.selector.startsWith("@page")||e.selector.startsWith("@document"))&&(a=Ut(e.content,t,n,o,r));return{selector:i.replace(/\s{2,}/g," ").trim(),content:a}}),qt=(e,t,n,o,r)=>{const i=((e,t)=>{const n="."+t+" > ",o=[];return e=e.replace($t,function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(t[2]){const e=t[2].trim(),r=t[3],i=n+e+r;let a="";for(let n=t[4]-1;n>=0;n--){const e=t[5][n];if("}"===e||","===e)break;a=e+a}const s=(a+i).trim(),l=`${a.trimEnd()}${i.trim()}`.trim();if(s!==l){const e=`${l}, ${s}`;o.push({orgSelector:s,updatedSelector:e})}return i}return xt+t[3]}),{selectors:o,cssText:e}})(e=(e=>Bt(e,wt,jt))(e=(e=>Bt(e,yt,Vt))(e=(e=>{const t=[];return e=(e=e.replace(/@supports\s+selector\s*\(\s*([^)]*)\s*\)/g,(e,n)=>{const o=`__supports_${t.length}__`;return t.push(n),`@supports selector(${o})`})).replace(Lt,`$1${bt}`).replace(Pt,`$1${mt}`).replace(_t,`$1${gt}`),t.forEach((t,n)=>{e=e.replace(`__supports_${n}__`,t)}),e})(e))),o);return e=(e=>St.reduce((e,t)=>e.replace(t," "),e))(e=i.cssText),t&&(e=Ut(e,t,n,o,r)),{cssText:(e=(e=Qt(e,n)).replace(/>\s*\*\s+([^{, ]+)/gm," $1 ")).trim(),slottedSelectors:i.selectors.map(e=>({orgSelector:Qt(e.orgSelector,n),updatedSelector:Qt(e.updatedSelector,n)}))}},Qt=(e,t)=>e.replace(/-shadowcsshost-no-combinator/g,`.${t}`),Kt=e=>{const t=/([^\s,{][^,{]*?)::part\(\s*([^)]+?)\s*\)((?:[:.][^,{]*)*)/g;return At(e,e=>{if("@"===e.selector[0])return e;const n=e.selector.split(",").map(n=>{const o=[n.trim()];let r;for(;null!==(r=t.exec(n));){const t=r[1].trimEnd(),i=r[2].trim().split(/\s+/),a=r[3]||"",s=i.flatMap(t=>e.selector.includes(`[part~="${t}"]`)?[]:[`[part~="${t}"]`]).join(""),l=`${t} ${s}${a}`;s&&l!==n.trim()&&o.push(l)}return o.join(", ")});return e.selector=n.join(", "),e})},Xt=(e,t,n)=>{const o=t+"-h",r=t+"-s",i=e.match(Nt)||[];e=(e=>e.replace(Rt,""))(e);const a=[];if(n){const t=e=>{const t=`/*!@___${a.length}___*/`,n=`/*!@${e.selector}*/`;return a.push({placeholder:t,comment:n}),e.selector=t+e.selector,e};e=At(e,e=>"@"!==e.selector[0]?t(e):e.selector.startsWith("@media")||e.selector.startsWith("@supports")||e.selector.startsWith("@page")||e.selector.startsWith("@document")?(e.content=At(e.content,t),e):e)}const s=qt(e,t,o,r,n);return e=[s.cssText,...i].join("\n"),n&&a.forEach(t=>{let{placeholder:n,comment:o}=t;e=e.replace(n,o)}),s.slottedSelectors.forEach(t=>{const n=new RegExp(ee(t.orgSelector),"g");e=e.replace(n,t.updatedSelector)}),e=Kt(e)},Yt=e=>k.map(t=>t(e)).find(e=>!!e),Gt=e=>k.push(e),Zt=e=>{var t;return null==(t=p(e))?void 0:t.$modeName$},Jt=(e,t,n)=>{if((r.hydrateClientSide||r.hydrateServerSide)&&"string"===typeof e&&e.startsWith(f))return e="string"===typeof(o=e)&&o.startsWith(f)?te.fromLocalValue(JSON.parse(atob(o.slice(11)))):o;var o;if("string"===typeof e&&(16&t||8&t)&&(e.startsWith("{")&&e.endsWith("}")||e.startsWith("[")&&e.endsWith("]")))try{return JSON.parse(e)}catch(i){}return null==e||Z(e)?e:r.propBoolean&&4&t?(r.formAssociated&&n&&"string"===typeof e||"false"!==e)&&(""===e||!!e):r.propNumber&&2&t?"string"===typeof e?parseFloat(e):"number"===typeof e?e:NaN:r.propString&&1&t?String(e):e},en=(e,t,n)=>{const o=(e=>{var t;return r.lazyLoad?null==(t=p(e))?void 0:t.$hostElement$:e})(e);return{emit:e=>(r.isDev&&!o.isConnected&&w(`The "${t}" event was emitted, but the dispatcher node is no longer connected to the dom.`),tn(o,t,{bubbles:!!(4&n),composed:!!(2&n),cancelable:!!(1&n),detail:e}))}},tn=(e,t,n)=>{const o=O.ce(t,n);return e.dispatchEvent(o),o},nn=(e,t,n,o,i,a,s)=>{if(n===o)return;let l=g(e,t),c=t.toLowerCase();if(r.vdomClass&&"class"===t){const t=e.classList,i=rn(n);let a=rn(o);if(r.hydrateClientSide&&(e["s-si"]||e["s-sc"])&&s){const n=e["s-sc"]||e["s-si"];a.push(n),i.forEach(e=>{e.startsWith(n)&&a.push(e)}),a=[...new Set(a)].filter(e=>e),t.add(...a)}else t.remove(...i.filter(e=>e&&!a.includes(e))),t.add(...a.filter(e=>e&&!i.includes(e)))}else if(r.vdomStyle&&"style"===t){if(r.updatable)for(const t in n)o&&null!=o[t]||(!r.hydrateServerSide&&t.includes("-")?e.style.removeProperty(t):e.style[t]="");for(const t in o)n&&o[t]===n[t]||(!r.hydrateServerSide&&t.includes("-")?e.style.setProperty(t,o[t]):e.style[t]=o[t])}else if(r.vdomKey&&"key"===t);else if(r.vdomRef&&"ref"===t)o&&o(e);else if(!r.vdomListener||(r.lazyLoad?l:e.__lookupSetter__(t))||"o"!==t[0]||"n"!==t[1]){if(r.vdomPropOrAttr){const s=Z(o);if((l||s&&null!==o)&&!i)try{if(e.tagName.includes("-"))e[t]!==o&&(e[t]=o);else{const r=null==o?"":o;"list"===t?l=!1:null!=n&&e[t]==r||("function"===typeof e.__lookupSetter__(t)?e[t]=r:e.setAttribute(t,r))}}catch(u){}let d=!1;r.vdomXlink&&c!==(c=c.replace(/^xlink\:?/,""))&&(t=c,d=!0),null==o||!1===o?!1===o&&""!==e.getAttribute(t)||(r.vdomXlink&&d?e.removeAttributeNS(P,t):e.removeAttribute(t)):(!l||4&a||i)&&!s&&1===e.nodeType&&(o=!0===o?"":o,r.vdomXlink&&d?e.setAttributeNS(P,t,o):e.setAttribute(t,o))}}else if(t="-"===t[2]?t.slice(3):g(R,c)?c.slice(2):c[2]+t.slice(3),n||o){const r=t.endsWith(an);t=t.replace(sn,""),n&&O.rel(e,t,n,r),o&&O.ael(e,t,o,r)}},on=/\s/,rn=e=>("object"===typeof e&&e&&"baseVal"in e&&(e=e.baseVal),e&&"string"===typeof e?e.split(on):[]),an="Capture",sn=new RegExp(an+"$"),ln=(e,t,n,o)=>{const i=11===t.$elm$.nodeType&&t.$elm$.host?t.$elm$.host:t.$elm$,a=e&&e.$attrs$||{},s=t.$attrs$||{};if(r.updatable)for(const r of cn(Object.keys(a)))r in s||nn(i,r,a[r],void 0,n,t.$flags$,o);for(const r of cn(Object.keys(s)))nn(i,r,a[r],s[r],n,t.$flags$,o)};function cn(e){return e.includes("ref")?[...e.filter(e=>"ref"!==e),"ref"]:e}var un=!1,dn=!1,fn=!1,hn=!1,pn=(e,t,n)=>{var o;const i=t.$children$[n];let a,s,l,c=0;if(r.slotRelocation&&!un&&(fn=!0,"slot"===i.$tag$&&(i.$flags$|=i.$children$?2:1)),r.isDev&&i.$elm$&&y(`The JSX ${null!==i.$text$?`"${i.$text$}" text`:`"${i.$tag$}" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`),r.vdomText&&null!==i.$text$)a=i.$elm$=R.document.createTextNode(i.$text$);else if(r.slotRelocation&&1&i.$flags$)a=i.$elm$=r.isDebug||r.hydrateServerSide?_n(i):R.document.createTextNode(""),r.vdomAttribute&&ln(null,i,hn);else{if(r.svg&&!hn&&(hn="svg"===i.$tag$),!R.document)throw new Error("You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.");if(a=i.$elm$=r.svg?R.document.createElementNS(hn?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",!un&&r.slotRelocation&&2&i.$flags$?"slot-fb":i.$tag$):R.document.createElement(!un&&r.slotRelocation&&2&i.$flags$?"slot-fb":i.$tag$),r.svg&&hn&&"foreignObject"===i.$tag$&&(hn=!1),r.vdomAttribute&&ln(null,i,hn),(r.scoped||r.hydrateServerSide)&&(null!=(u=ut)&&void 0!==u)&&a["s-si"]!==ut&&a.classList.add(a["s-si"]=ut),i.$children$)for(c=0;c<i.$children$.length;++c)s=pn(e,i,c),s&&a.appendChild(s);r.svg&&("svg"===i.$tag$?hn=!1:"foreignObject"===a.tagName&&(hn=!0))}var u;return a["s-hn"]=ft,r.slotRelocation&&3&i.$flags$&&(a["s-sr"]=!0,a["s-cr"]=dt,a["s-sn"]=i.$name$||"",a["s-rf"]=null==(o=i.$attrs$)?void 0:o.ref,ve(a),l=e&&e.$children$&&e.$children$[n],l&&l.$tag$===i.$tag$&&e.$elm$&&(r.experimentalSlotFixes?mn(e.$elm$):gn(e.$elm$,!1)),(r.scoped||r.hydrateServerSide)&&Cn(dt,a,t.$elm$,null==e?void 0:e.$elm$)),a},mn=e=>{O.$flags$|=1;const t=e.closest(ft.toLowerCase());if(null!=t){const n=Array.from(t.__childNodes||t.childNodes).find(e=>e["s-cr"]),o=Array.from(e.__childNodes||e.childNodes);for(const e of n?o.reverse():o)null!=e["s-sh"]&&(En(t,e,null!=n?n:null),e["s-sh"]=void 0,fn=!0)}O.$flags$&=-2},gn=(e,t)=>{O.$flags$|=1;const n=Array.from(e.__childNodes||e.childNodes);if(e["s-sr"]&&r.experimentalSlotFixes){let t=e;for(;t=t.nextSibling;)t&&t["s-sn"]===e["s-sn"]&&t["s-sh"]===ft&&n.push(t)}for(let o=n.length-1;o>=0;o--){const e=n[o];e["s-hn"]!==ft&&e["s-ol"]&&(En(wn(e).parentNode,e,wn(e)),e["s-ol"].remove(),e["s-ol"]=void 0,e["s-sh"]=void 0,fn=!0),t&&gn(e,t)}O.$flags$&=-2},bn=(e,t,n,o,i,a)=>{let s,l=r.slotRelocation&&e["s-cr"]&&e["s-cr"].parentNode||e;for(r.shadowDom&&l.shadowRoot&&l.tagName===ft&&(l=l.shadowRoot);i<=a;++i)o[i]&&(s=pn(null,n,i),s&&(o[i].$elm$=s,En(l,s,r.slotRelocation?wn(t):t)))},vn=(e,t,n)=>{for(let o=t;o<=n;++o){const t=e[o];if(t){const e=t.$elm$;Sn(t),e&&(r.slotRelocation&&(dn=!0,e["s-ol"]?e["s-ol"].remove():gn(e,!0)),e.remove())}}},yn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return e.$tag$===t.$tag$&&(r.slotRelocation&&"slot"===e.$tag$?e.$name$===t.$name$:r.vdomKey&&!n?e.$key$===t.$key$:(n&&!e.$key$&&t.$key$&&(e.$key$=t.$key$),!0))},wn=e=>e&&e["s-ol"]||e,$n=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=t.$elm$=e.$elm$,i=e.$children$,a=t.$children$,s=t.$tag$,l=t.$text$;let c;r.vdomText&&null!==l?r.vdomText&&r.slotRelocation&&(c=o["s-cr"])?c.parentNode.textContent=l:r.vdomText&&e.$text$!==l&&(o.data=l):(r.svg&&(hn="svg"===s||"foreignObject"!==s&&hn),(r.vdomAttribute||r.reflect)&&(r.slot&&"slot"===s&&!un&&r.experimentalSlotFixes&&e.$name$!==t.$name$&&(t.$elm$["s-sn"]=t.$name$||"",mn(t.$elm$.parentElement)),ln(e,t,hn,n)),r.updatable&&null!==i&&null!==a?function(e,t,n,o){let i,a,s=arguments.length>4&&void 0!==arguments[4]&&arguments[4],l=0,c=0,u=0,d=0,f=t.length-1,h=t[0],p=t[f],m=o.length-1,g=o[0],b=o[m];for(;l<=f&&c<=m;)if(null==h)h=t[++l];else if(null==p)p=t[--f];else if(null==g)g=o[++c];else if(null==b)b=o[--m];else if(yn(h,g,s))$n(h,g,s),h=t[++l],g=o[++c];else if(yn(p,b,s))$n(p,b,s),p=t[--f],b=o[--m];else if(yn(h,b,s))!r.slotRelocation||"slot"!==h.$tag$&&"slot"!==b.$tag$||gn(h.$elm$.parentNode,!1),$n(h,b,s),En(e,h.$elm$,p.$elm$.nextSibling),h=t[++l],b=o[--m];else if(yn(p,g,s))!r.slotRelocation||"slot"!==h.$tag$&&"slot"!==b.$tag$||gn(p.$elm$.parentNode,!1),$n(p,g,s),En(e,p.$elm$,h.$elm$),p=t[--f],g=o[++c];else{if(u=-1,r.vdomKey)for(d=l;d<=f;++d)if(t[d]&&null!==t[d].$key$&&t[d].$key$===g.$key$){u=d;break}r.vdomKey&&u>=0?(a=t[u],a.$tag$!==g.$tag$?i=pn(t&&t[c],n,u):($n(a,g,s),t[u]=void 0,i=a.$elm$),g=o[++c]):(i=pn(t&&t[c],n,c),g=o[++c]),i&&(r.slotRelocation?En(wn(h.$elm$).parentNode,i,wn(h.$elm$)):En(h.$elm$.parentNode,i,h.$elm$))}l>f?bn(e,null==o[m+1]?null:o[m+1].$elm$,n,o,c,m):r.updatable&&c>m&&vn(t,l,f)}(o,i,t,a,n):null!==a?(r.updatable&&r.vdomText&&null!==e.$text$&&(o.textContent=""),bn(o,null,t,a,0,a.length-1)):!n&&r.updatable&&null!==i?vn(i,0,i.length-1):r.hydrateClientSide&&n&&r.updatable&&null!==i&&null===a&&(t.$children$=i),r.svg&&hn&&"svg"===s&&(hn=!1))},xn=[],kn=e=>{let t,n,o;const i=e.__childNodes||e.childNodes;for(const a of i){if(a["s-sr"]&&(t=a["s-cr"])&&t.parentNode){n=t.parentNode.__childNodes||t.parentNode.childNodes;const e=a["s-sn"];for(o=n.length-1;o>=0;o--)if(t=n[o],!t["s-cn"]&&!t["s-nr"]&&t["s-hn"]!==a["s-hn"]&&(!r.experimentalSlotFixes||!t["s-sh"]||t["s-sh"]!==a["s-hn"]))if(me(t,e)){let n=xn.find(e=>e.$nodeToRelocate$===t);dn=!0,t["s-sn"]=t["s-sn"]||e,n?(n.$nodeToRelocate$["s-sh"]=a["s-hn"],n.$slotRefNode$=a):(t["s-sh"]=a["s-hn"],xn.push({$slotRefNode$:a,$nodeToRelocate$:t})),t["s-sr"]&&xn.map(e=>{me(e.$nodeToRelocate$,t["s-sn"])&&(n=xn.find(e=>e.$nodeToRelocate$===t),n&&!e.$slotRefNode$&&(e.$slotRefNode$=n.$slotRefNode$))})}else xn.some(e=>e.$nodeToRelocate$===t)||xn.push({$nodeToRelocate$:t})}1===a.nodeType&&kn(a)}},Sn=e=>{r.vdomRef&&(e.$attrs$&&e.$attrs$.ref&&e.$attrs$.ref(null),e.$children$&&e.$children$.map(Sn))},En=(e,t,n)=>{if(r.scoped&&"string"===typeof t["s-sn"]&&t["s-sr"]&&t["s-cr"])Cn(t["s-cr"],t,e,t.parentElement);else if(r.experimentalSlotFixes&&"string"===typeof t["s-sn"]){11!==e.getRootNode().nodeType&&Me(t),e.insertBefore(t,n);const{slotNode:o}=we(t);return o&&ye(o),t}return r.experimentalSlotFixes&&e.__insertBefore?e.__insertBefore(t,n):null==e?void 0:e.insertBefore(t,n)};function Cn(e,t,n,o){var r,i;let a;if(e&&"string"===typeof t["s-sn"]&&t["s-sr"]&&e.parentNode&&e.parentNode["s-sc"]&&(a=t["s-si"]||e.parentNode["s-sc"])){const e=t["s-sn"],s=t["s-hn"];if(null==(r=n.classList)||r.add(a+"-s"),o&&(null==(i=o.classList)?void 0:i.contains(a+"-s"))){let t=(o.__childNodes||o.childNodes)[0],n=!1;for(;t;){if(t["s-sn"]!==e&&t["s-hn"]===s&&t["s-sr"]){n=!0;break}t=t.nextSibling}n||o.classList.remove(a+"-s")}}}var Tn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var o,i,a,s,l;const c=e.$hostElement$,u=e.$cmpMeta$,d=e.$vnode$||Je(null,null),f=tt(t)?t:Ze(null,null,t);if(ft=c.tagName,r.isDev&&Array.isArray(t)&&t.some(tt))throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of "${ft.toLowerCase()}" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);if(r.reflect&&u.$attrsToReflect$&&(f.$attrs$=f.$attrs$||{},u.$attrsToReflect$.map(e=>{let[t,n]=e;return f.$attrs$[n]=c[t]})),n&&f.$attrs$)for(const r of Object.keys(f.$attrs$))c.hasAttribute(r)&&!["key","ref","style","class"].includes(r)&&(f.$attrs$[r]=c[r]);if(f.$tag$=null,f.$flags$|=4,e.$vnode$=f,f.$elm$=d.$elm$=r.shadowDom&&c.shadowRoot||c,(r.scoped||r.shadowDom)&&(ut=c["s-sc"]),un=z&&!!(1&u.$flags$)&&!(128&u.$flags$),r.slotRelocation&&(dt=c["s-cr"],dn=!1),$n(d,f,n),r.slotRelocation){if(O.$flags$|=1,fn){kn(f.$elm$);for(const e of xn){const t=e.$nodeToRelocate$;if(!t["s-ol"]&&R.document){const e=r.isDebug||r.hydrateServerSide?Pn(t):R.document.createTextNode("");e["s-nr"]=t,En(t.parentNode,t["s-ol"]=e,t)}}for(const e of xn){const t=e.$nodeToRelocate$,l=e.$slotRefNode$;if(l){const e=l.parentNode;let n=l.nextSibling;if(!r.hydrateServerSide&&(!r.experimentalSlotFixes||n&&1===n.nodeType)){let r=null==(o=t["s-ol"])?void 0:o.previousSibling;for(;r;){let o=null!=(i=r["s-nr"])?i:null;if(o&&o["s-sn"]===t["s-sn"]&&e===(o.__parentNode||o.parentNode)){for(o=o.nextSibling;o===t||(null==o?void 0:o["s-sr"]);)o=null==o?void 0:o.nextSibling;if(!o||!o["s-nr"]){n=o;break}}r=r.previousSibling}}const s=t.__parentNode||t.parentNode,c=t.__nextSibling||t.nextSibling;(!n&&e!==s||c!==n)&&t!==n&&(r.experimentalSlotFixes||t["s-hn"]||!t["s-ol"]||(t["s-hn"]=t["s-ol"].parentNode.nodeName),En(e,t,n),1===t.nodeType&&"SLOT-FB"!==t.tagName&&(t.hidden=null!=(a=t["s-ih"])&&a)),t&&"function"===typeof l["s-rf"]&&l["s-rf"](l)}else 1===t.nodeType&&(n&&(t["s-ih"]=null!=(s=t.hidden)&&s),t.hidden=!0)}}dn&&de(f.$elm$),O.$flags$&=-2,xn.length=0}if(r.experimentalScopedSlotChanges&&2&u.$flags$){const e=f.$elm$.__childNodes||f.$elm$.childNodes;for(const t of e)t["s-hn"]===ft||t["s-sh"]||(n&&null==t["s-ih"]&&(t["s-ih"]=null!=(l=t.hidden)&&l),t.hidden=!0)}dt=void 0},_n=e=>{var t;return null==(t=R.document)?void 0:t.createComment(`<slot${e.$name$?' name="'+e.$name$+'"':""}> (host=${ft.toLowerCase()})`)},Pn=e=>{var t;return null==(t=R.document)?void 0:t.createComment("org-location for "+(e.localName?`<${e.localName}> (host=${e["s-hn"]})`:`[${e.textContent}]`))},Ln=(e,t)=>{if(r.asyncLoading&&t&&!e.$onRenderResolve$&&t["s-p"]){const n=t["s-p"].push(new Promise(o=>e.$onRenderResolve$=()=>{t["s-p"].splice(n-1,1),o()}))}},Rn=(e,t)=>{if(r.taskQueue&&r.updatable&&(e.$flags$|=16),r.asyncLoading&&4&e.$flags$)return void(e.$flags$|=512);Ln(e,e.$ancestorComponent$);const n=()=>Nn(e,t);if(!t)return r.taskQueue?Y(n):n();queueMicrotask(()=>{n()})},Nn=(e,t)=>{const n=e.$hostElement$,o=We("scheduleUpdate",e.$cmpMeta$.$tagName$),i=r.lazyLoad?e.$lazyInstance$:n;if(!i)throw new Error(`Can't render component <${n.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);let a;return t?(r.lazyLoad&&r.hostListener&&(e.$flags$|=256,e.$queuedListeners$&&(e.$queuedListeners$.map(e=>{let[t,o]=e;return Vn(i,t,o,n)}),e.$queuedListeners$=void 0)),jn(n,"componentWillLoad"),a=Vn(i,"componentWillLoad",void 0,n)):(jn(n,"componentWillUpdate"),a=Vn(i,"componentWillUpdate",void 0,n)),jn(n,"componentWillRender"),a=On(a,()=>Vn(i,"componentWillRender",void 0,n)),o(),On(a,()=>zn(e,i,t))},On=(e,t)=>In(e)?e.then(t).catch(e=>{console.error(e),t()}):t(),In=e=>e instanceof Promise||e&&e.then&&"function"===typeof e.then,zn=async(e,t,n)=>{var o;const i=e.$hostElement$,a=We("update",e.$cmpMeta$.$tagName$),s=i["s-rc"];r.style&&n&&Ke(e);const l=We("render",e.$cmpMeta$.$tagName$);if(r.isDev&&(e.$flags$|=1024),r.hydrateServerSide?await Dn(e,t,i,n):Dn(e,t,i,n),r.isDev&&(e.$renderCount$=void 0===e.$renderCount$?1:e.$renderCount$+1,e.$flags$&=-1025),r.hydrateServerSide)try{Fn(i),n&&(1&e.$cmpMeta$.$flags$?i["s-en"]="":2&e.$cmpMeta$.$flags$&&(i["s-en"]="c"))}catch(c){b(c,i)}if(r.asyncLoading&&s&&(s.map(e=>e()),i["s-rc"]=void 0),l(),a(),r.asyncLoading){const t=null!=(o=i["s-p"])?o:[],n=()=>An(e);0===t.length?n():(Promise.all(t).then(n),e.$flags$|=4,t.length=0)}else An(e)},Dn=(e,t,n,o)=>{const i=!!r.allRenderFn,a=!!r.lazyLoad,s=!!r.taskQueue,l=!!r.updatable;try{if(t=(i||t.render)&&t.render(),l&&s&&(e.$flags$&=-17),(l||a)&&(e.$flags$|=2),r.hasRenderFn||r.reflect)if(r.vdomRender||r.reflect){if(r.hydrateServerSide)return Promise.resolve(t).then(t=>Tn(e,t,o));Tn(e,t,o)}else{const o=n.shadowRoot;1&e.$cmpMeta$.$flags$?o.textContent=t:n.textContent=t}}catch(c){b(c,e.$hostElement$)}return null},An=e=>{const t=e.$cmpMeta$.$tagName$,n=e.$hostElement$,o=We("postUpdate",t),i=r.lazyLoad?e.$lazyInstance$:n,a=e.$ancestorComponent$;r.isDev&&(e.$flags$|=1024),Vn(i,"componentDidRender",void 0,n),r.isDev&&(e.$flags$&=-1025),jn(n,"componentDidRender"),64&e.$flags$?(r.isDev&&(e.$flags$|=1024),Vn(i,"componentDidUpdate",void 0,n),r.isDev&&(e.$flags$&=-1025),jn(n,"componentDidUpdate"),o()):(e.$flags$|=64,r.asyncLoading&&r.cssAnnotations&&Hn(n),r.isDev&&(e.$flags$|=2048),Vn(i,"componentDidLoad",void 0,n),r.isDev&&(e.$flags$&=-2049),jn(n,"componentDidLoad"),o(),r.asyncLoading&&(e.$onReadyResolve$(n),a||Bn(t))),r.method&&r.lazyLoad&&e.$onInstanceResolve$(n),r.asyncLoading&&(e.$onRenderResolve$&&(e.$onRenderResolve$(),e.$onRenderResolve$=void 0),512&e.$flags$&&K(()=>Rn(e,!1)),e.$flags$&=-517)},Mn=e=>{var t;if(r.updatable&&(s.isBrowser||s.isTesting)){const n=p(e),o=null==(t=null==n?void 0:n.$hostElement$)?void 0:t.isConnected;return o&&2===(18&n.$flags$)&&Rn(n,!1),o}return!1},Bn=e=>{var t;r.asyncQueue&&(O.$flags$|=2),K(()=>tn(R,"appload",{detail:{namespace:i}})),r.hydrateClientSide&&(null==(t=O.$orgLocNodes$)?void 0:t.size)&&O.$orgLocNodes$.clear(),r.profile&&performance.measure&&performance.measure(`[Stencil] ${i} initial load (by ${e})`,"st:app:start")},Vn=(e,t,n,o)=>{if(e&&e[t])try{return e[t](n)}catch(r){b(r,o)}},jn=(e,t)=>{r.lifecycleDOMEvents&&tn(e,"stencil_"+t,{bubbles:!0,composed:!0,detail:{namespace:i}})},Hn=e=>{var t,n;return r.hydratedClass?e.classList.add(null!=(t=r.hydratedSelectorName)?t:"hydrated"):r.hydratedAttribute?e.setAttribute(null!=(n=r.hydratedSelectorName)?n:"hydrated",""):void 0},Fn=e=>{const t=e.children;if(null!=t)for(let n=0,o=t.length;n<o;n++){const e=t[n];"function"===typeof e.connectedCallback&&e.connectedCallback(),Fn(e)}},Wn=(e,t)=>p(e).$instanceValues$.get(t),Un=(e,t,n,o)=>{const i=p(e);if(!i)return;if(r.lazyLoad&&!i)throw new Error(`Couldn't find host element for "${o.$tagName$}" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`);const a=r.lazyLoad?i.$hostElement$:e,s=i.$instanceValues$.get(t),l=i.$flags$,c=r.lazyLoad?i.$lazyInstance$:a;n=Jt(n,o.$members$[t][0],r.formAssociated&&!!(64&o.$flags$));const u=Number.isNaN(s)&&Number.isNaN(n);if((!r.lazyLoad||!(8&l)||void 0===s)&&(n!==s&&!u)&&(i.$instanceValues$.set(t,n),r.isDev&&(1024&i.$flags$?w(`The state/prop "${t}" changed during rendering. This can potentially lead to infinite-loops and other bugs.`,"\nElement",a,"\nNew value",n,"\nOld value",s):2048&i.$flags$&&w(`The state/prop "${t}" changed during "componentDidLoad()", this triggers extra re-renders, try to setup on "componentWillLoad()"`,"\nElement",a,"\nNew value",n,"\nOld value",s)),!r.lazyLoad||c)){if(r.watchCallback&&o.$watchers$&&128&l){const e=o.$watchers$[t];e&&e.map(e=>{try{c[e](n,s,t)}catch(o){b(o,a)}})}if(r.updatable&&2===(18&l)){if(c.componentShouldUpdate&&!1===c.componentShouldUpdate(n,s,t))return;Rn(i,!1)}}},qn=(e,t,n)=>{var o,i;const a=e.prototype;if(r.isTesting){if(a.__stencilAugmented)return;a.__stencilAugmented=!0}if(r.formAssociated&&64&t.$flags$&&1&n&&L.forEach(e=>{const t=a[e];Object.defineProperty(a,e,{value(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a;const s=p(this),l=r.lazyLoad?null==s?void 0:s.$lazyInstance$:this;if(l){const n=r.lazyLoad?l[e]:t;"function"===typeof n&&n.call(l,...o)}else null==(a=null==s?void 0:s.$onReadyPromise$)||a.then(t=>{const n=t[e];"function"===typeof n&&n.call(t,...o)})}})}),r.member&&t.$members$||r.watchCallback&&(t.$watchers$||e.watchers)){r.watchCallback&&e.watchers&&!t.$watchers$&&(t.$watchers$=e.watchers);const s=Object.entries(null!=(o=t.$members$)?o:{});if(s.map(e=>{let[o,[i]]=e;if((r.prop||r.state)&&(31&i||(!r.lazyLoad||2&n)&&32&i)){const{get:e,set:s}=Object.getOwnPropertyDescriptor(a,o)||{};e&&(t.$members$[o][0]|=2048),s&&(t.$members$[o][0]|=4096),(1&n||!e)&&Object.defineProperty(a,o,{get(){if(r.lazyLoad){if(0===(2048&t.$members$[o][0]))return Wn(this,o);const e=p(this),n=e?e.$lazyInstance$:a;if(!n)return;return n[o]}if(!r.lazyLoad)return e?e.apply(this):Wn(this,o)},configurable:!0,enumerable:!0}),Object.defineProperty(a,o,{set(e){const a=p(this);if(a){if(r.isDev&&0===(1&n)&&0===(4096&t.$members$[o][0])&&0===(a&&8&a.$flags$)&&0!==(31&i)&&0===(1024&i)&&w(`@Prop() "${o}" on <${t.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`),s){const n=32&i?this[o]:a.$hostElement$[o];return"undefined"===typeof n&&a.$instanceValues$.get(o)?e=a.$instanceValues$.get(o):!a.$instanceValues$.get(o)&&n&&a.$instanceValues$.set(o,n),s.apply(this,[Jt(e,i,r.formAssociated&&!!(64&t.$flags$))]),e=32&i?this[o]:a.$hostElement$[o],void Un(this,o,e,t)}if(r.lazyLoad){if(r.lazyLoad){if(0===(1&n)||0===(4096&t.$members$[o][0]))return Un(this,o,e,t),void(1&n&&!a.$lazyInstance$&&a.$onReadyPromise$.then(()=>{4096&t.$members$[o][0]&&a.$lazyInstance$[o]!==a.$instanceValues$.get(o)&&(a.$lazyInstance$[o]=e)}));const s=()=>{const n=a.$lazyInstance$[o];!a.$instanceValues$.get(o)&&n&&a.$instanceValues$.set(o,n),a.$lazyInstance$[o]=Jt(e,i,r.formAssociated&&!!(64&t.$flags$)),Un(this,o,a.$lazyInstance$[o],t)};a.$lazyInstance$?s():a.$onReadyPromise$.then(()=>s())}}else Un(this,o,e,t)}}})}else r.lazyLoad&&r.method&&1&n&&64&i&&Object.defineProperty(a,o,{value(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r;const i=p(this);return null==(r=null==i?void 0:i.$onInstancePromise$)?void 0:r.then(()=>{var e;return null==(e=i.$lazyInstance$)?void 0:e[o](...t)})}})}),r.observeAttribute&&(!r.lazyLoad||1&n)){const n=new Map;a.attributeChangedCallback=function(e,o,i){O.jmp(()=>{var s;const l=n.get(e);if(this.hasOwnProperty(l)&&r.lazyLoad)i=this[l],delete this[l];else{if(a.hasOwnProperty(l)&&"number"===typeof this[l]&&this[l]==i)return;if(null==l){const n=p(this),a=null==n?void 0:n.$flags$;if(n&&a&&!(8&a)&&128&a&&i!==o){const a=r.lazyLoad?n.$hostElement$:this,l=r.lazyLoad?n.$lazyInstance$:a,c=null==(s=t.$watchers$)?void 0:s[e];null==c||c.forEach(t=>{null!=l[t]&&l[t].call(l,i,o,e)})}return}}const c=Object.getOwnPropertyDescriptor(a,l);(i=(null!==i||"boolean"!==typeof this[l])&&i)===this[l]||c.get&&!c.set||(this[l]=i)})},e.observedAttributes=Array.from(new Set([...Object.keys(null!=(i=t.$watchers$)?i:{}),...s.filter(e=>{let[t,n]=e;return 15&n[0]}).map(e=>{let[o,i]=e;var a;const s=i[1]||o;return n.set(s,o),r.reflect&&512&i[0]&&(null==(a=t.$attrsToReflect$)||a.push([o,s])),s})]))}}return e},Qn=async(e,t,o,i)=>{let a;if(0===(32&t.$flags$)){t.$flags$|=32;const c=o.$lazyBundleId$;if(r.lazyLoad&&c){const c=((e,t,o)=>{const i=e.$tagName$.replace(/-/g,"_"),a=e.$lazyBundleId$;if(r.isDev&&"string"!==typeof a)return void y(`Trying to lazily load component <${e.$tagName$}> with style mode "${t.$modeName$}", but it does not exist.`);if(!a)return;const s=!r.hotModuleReplacement&&$.get(a);return s?s[i]:n(140)(`./${a}.entry.js${r.hotModuleReplacement&&o?"?s-hmr="+o:""}`).then(e=>(r.hotModuleReplacement||$.set(a,e),e[i]),e=>{b(e,t.$hostElement$)})})(o,t,i);if(c&&"then"in c){const e=(s=`st:load:${o.$tagName$}:${t.$modeName$}`,l=`[Stencil] Load module for <${o.$tagName$}>`,r.profile&&performance.mark?(0===performance.getEntriesByName(s,"mark").length&&performance.mark(s),()=>{0===performance.getEntriesByName(l,"measure").length&&performance.measure(l,s)}):()=>{});a=await c,e()}else a=c;if(!a)throw new Error(`Constructor for "${o.$tagName$}#${t.$modeName$}" was not found`);r.member&&!a.isProxied&&(r.watchCallback&&(o.$watchers$=a.watchers),qn(a,o,2),a.isProxied=!0);const u=We("createInstance",o.$tagName$);r.member&&(t.$flags$|=8);try{new a(t)}catch(d){b(d,e)}r.member&&(t.$flags$&=-9),r.watchCallback&&(t.$flags$|=128),u(),Kn(t.$lazyInstance$,e)}else{a=e.constructor;const n=e.localName;customElements.whenDefined(n).then(()=>t.$flags$|=128)}if(r.style&&a&&a.style){let n;"string"===typeof a.style?n=a.style:r.mode&&"string"!==typeof a.style&&(t.$modeName$=Yt(e),t.$modeName$&&(n=a.style[t.$modeName$]),r.hydrateServerSide&&t.$modeName$&&e.setAttribute("s-mode",t.$modeName$));const i=Xe(o,t.$modeName$);if(!x.has(i)){const e=We("registerStyles",o.$tagName$);r.hydrateServerSide&&r.shadowDom&&128&o.$flags$&&(n=Xt(n,i,!0)),qe(i,n,!!(1&o.$flags$)),e()}}}var s,l;const c=t.$ancestorComponent$,u=()=>Rn(t,!0);r.asyncLoading&&c&&c["s-rc"]?c["s-rc"].push(u):u()},Kn=(e,t)=>{r.lazyLoad&&Vn(e,"connectedCallback",void 0,t)},Xn=e=>{if(0===(1&O.$flags$)){const t=p(e);if(!t)return;const n=t.$cmpMeta$,o=We("connectedCallback",n.$tagName$);if(r.hostListenerTargetParent&&eo(e,t,n.$listeners$,!0),1&t.$flags$)eo(e,t,n.$listeners$,!1),(null==t?void 0:t.$lazyInstance$)?Kn(t.$lazyInstance$,e):(null==t?void 0:t.$onReadyPromise$)&&t.$onReadyPromise$.then(()=>Kn(t.$lazyInstance$,e));else{let o;if(t.$flags$|=1,r.hydrateClientSide&&(o=e.getAttribute(E),o)){if(r.shadowDom&&z&&1&n.$flags$){const t=r.mode?Qe(e.shadowRoot,n,e.getAttribute("s-mode")):Qe(e.shadowRoot,n);e.classList.remove(t+"-h",t+"-s")}else if(r.scoped&&2&n.$flags$){const t=Xe(n,r.mode?e.getAttribute("s-mode"):void 0);e["s-sc"]=t}((e,t,n,o)=>{var i,a,s;const l=We("hydrateClient",t),c=e.shadowRoot,u=[],d=[],f=[],h=r.shadowDom&&c?[]:null,m=Je(t,null);let g;if(m.$elm$=e,Object.entries((null==(i=o.$cmpMeta$)?void 0:i.$members$)||{}).forEach(t=>{let[n,[i,a]]=t;var s,l;if(!(31&i))return;const c=a||n,u=e.getAttribute(c);if(null!==u){const e=Jt(u,i,r.formAssociated&&!!(64&(null==(s=o.$cmpMeta$)?void 0:s.$flags$)));null==(l=null==o?void 0:o.$instanceValues$)||l.set(n,e)}}),r.scoped){const t=o.$cmpMeta$;t&&10&t.$flags$&&e["s-sc"]?(g=e["s-sc"],e.classList.add(g+"-h")):e["s-sc"]&&delete e["s-sc"]}!R.document||O.$orgLocNodes$&&O.$orgLocNodes$.size||st(R.document.body,O.$orgLocNodes$=new Map),e[E]=n,e.removeAttribute(E),o.$vnode$=at(m,u,d,h,e,e,n,f);let b=0;const v=u.length;let y;for(;b<v;b++){y=u[b];const n=y.$hostId$+"."+y.$nodeId$,o=O.$orgLocNodes$.get(n),i=y.$elm$;if(c){if((null==(a=y.$tag$)?void 0:a.toString().includes("-"))&&"slot-fb"!==y.$tag$&&!y.$elm$.shadowRoot){const t=p(y.$elm$);if(t){const n=Xe(t.$cmpMeta$,r.mode?y.$elm$.getAttribute("s-mode"):void 0),o=R.document.querySelector(`style[sty-id="${n}"]`);o&&e.shadowRoot.append(o.cloneNode(!0))}}}else i["s-hn"]=t.toUpperCase(),"slot"===y.$tag$&&(i["s-cr"]=e["s-cr"]);"slot"===y.$tag$&&(y.$name$=y.$elm$["s-sn"]||y.$elm$.name||null,y.$children$?(y.$flags$|=2,y.$elm$.childNodes.length||y.$children$.forEach(e=>{y.$elm$.appendChild(e.$elm$)})):y.$flags$|=1),o&&o.isConnected&&(o.parentElement.shadowRoot&&""===o["s-en"]&&o.parentNode.insertBefore(i,o.nextSibling),o.parentNode.removeChild(o),c||(i["s-oo"]=parseInt(y.$nodeId$))),o&&!o["s-id"]&&O.$orgLocNodes$.delete(n)}const w=[],$=f.length;let x,k,S,C,T=0;for(;T<$;T++)if(x=f[T],x&&x.length)for(S=x.length,k=0;k<S;k++){if(C=x[k],w[C.hostId]||(w[C.hostId]=O.$orgLocNodes$.get(C.hostId)),!w[C.hostId])continue;const e=w[C.hostId];e.shadowRoot&&C.node.parentElement!==e&&e.appendChild(C.node),e.shadowRoot&&c||(C.slot["s-cr"]||(C.slot["s-cr"]=e["s-cr"],!C.slot["s-cr"]&&e.shadowRoot?C.slot["s-cr"]=e:C.slot["s-cr"]=(e.__childNodes||e.childNodes)[0]),ge(C.node,C.slot,!1,C.node["s-oo"]),(null==(s=C.node.parentElement)?void 0:s.shadowRoot)&&C.node.getAttribute&&C.node.getAttribute("slot")&&C.node.removeAttribute("slot"),r.experimentalSlotFixes&&Oe(C.node))}if(r.scoped&&g&&d.length&&d.forEach(e=>{e.$elm$.parentElement.classList.add(g+"-s")}),r.shadowDom&&c){let t=0;const n=h.length;if(n){for(;t<n;t++){const e=h[t];e&&c.appendChild(e)}Array.from(e.childNodes).forEach(e=>{"string"!==typeof e["s-en"]&&"string"!==typeof e["s-sn"]&&(1===e.nodeType&&e.slot&&e.hidden?e.removeAttribute("hidden"):(8===e.nodeType&&!e.nodeValue||3===e.nodeType&&!e.wholeText.trim())&&e.parentNode.removeChild(e))})}}o.$hostElement$=e,l()})(e,n.$tagName$,o,t)}if(r.slotRelocation&&!o&&(r.hydrateServerSide||(r.slot||r.shadowDom)&&12&n.$flags$)&&Yn(e),r.asyncLoading){let n=e;for(;n=n.parentNode||n.host;)if(r.hydrateClientSide&&1===n.nodeType&&n.hasAttribute("s-id")&&n["s-p"]||n["s-p"]){Ln(t,t.$ancestorComponent$=n);break}}r.prop&&!r.hydrateServerSide&&n.$members$&&Object.entries(n.$members$).map(t=>{let[n,[o]]=t;if(31&o&&e.hasOwnProperty(n)){const t=e[n];delete e[n],e[n]=t}}),r.initializeNextTick?K(()=>Qn(e,t,n)):Qn(e,t,n)}o()}},Yn=e=>{if(!R.document)return;const t=e["s-cr"]=R.document.createComment(r.isDebug?`content-ref (host=${e.localName})`:"");t["s-cn"]=!0,En(e,t,e.firstChild)},Gn=(e,t)=>{r.lazyLoad&&Vn(e,"disconnectedCallback",void 0,t||e)},Zn=async e=>{if(0===(1&O.$flags$)){const t=p(e);r.hostListener&&(null==t?void 0:t.$rmListeners$)&&(t.$rmListeners$.map(e=>e()),t.$rmListeners$=void 0),r.lazyLoad?(null==t?void 0:t.$lazyInstance$)?Gn(t.$lazyInstance$,e):(null==t?void 0:t.$onReadyPromise$)&&t.$onReadyPromise$.then(()=>Gn(t.$lazyInstance$,e)):Gn(e)}Ue.has(e)&&Ue.delete(e),e.shadowRoot&&Ue.has(e.shadowRoot)&&Ue.delete(e.shadowRoot)},Jn=(e,t)=>{const n={$flags$:t[0],$tagName$:t[1]};r.member&&(n.$members$=t[2]),r.hostListener&&(n.$listeners$=t[3]),r.watchCallback&&(n.$watchers$=e.$watchers$),r.reflect&&(n.$attrsToReflect$=[]),r.shadowDom&&!z&&1&n.$flags$&&(n.$flags$|=8),!(1&n.$flags$)&&256&n.$flags$&&(r.experimentalSlotFixes?$e(e.prototype):(r.slotChildNodesFix&&Ne(e.prototype),r.cloneNodeFix&&xe(e.prototype),r.appendChildSlotFix&&ke(e.prototype),r.scopedSlotTextContentFix&&2&n.$flags$&&Re(e.prototype))),r.hydrateClientSide&&r.shadowDom&&Ge();const o=e.prototype.connectedCallback,i=e.prototype.disconnectedCallback;return Object.assign(e.prototype,{__hasHostListenerAttached:!1,__registerHost(){m(this,n)},connectedCallback(){if(!this.__hasHostListenerAttached){const e=p(this);if(!e)return;eo(this,e,n.$listeners$,!1),this.__hasHostListenerAttached=!0}Xn(this),o&&o.call(this)},disconnectedCallback(){Zn(this),i&&i.call(this)},__attachShadow(){if(z)if(this.shadowRoot){if("open"!==this.shadowRoot.mode)throw new Error(`Unable to re-use existing shadow root for ${n.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`)}else le.call(this,n);else this.shadowRoot=this}}),e.is=n.$tagName$,qn(e,n,3)},eo=(e,t,n,o)=>{r.hostListener&&n&&R.document&&(r.hostListenerTargetParent&&(n=o?n.filter(e=>{let[t]=e;return 32&t}):n.filter(e=>{let[t]=e;return!(32&t)})),n.map(n=>{let[o,i,a]=n;const s=r.hostListenerTarget?no(R.document,e,o):e,l=to(t,a),c=oo(o);O.ael(s,i,l,c),(t.$rmListeners$=t.$rmListeners$||[]).push(()=>O.rel(s,i,l,c))}))},to=(e,t)=>n=>{var o;try{r.lazyLoad?256&e.$flags$?null==(o=e.$lazyInstance$)||o[t](n):(e.$queuedListeners$=e.$queuedListeners$||[]).push([t,n]):e.$hostElement$[t](n)}catch(i){b(i,e.$hostElement$)}},no=(e,t,n)=>r.hostListenerTargetDocument&&4&n?e:r.hostListenerTargetWindow&&8&n?R:r.hostListenerTargetBody&&16&n?e.body:r.hostListenerTargetParent&&32&n&&t.parentElement?t.parentElement:t,oo=e=>D?{passive:0!==(1&e),capture:0!==(2&e)}:0!==(2&e)},497:(e,t,n)=>{"use strict";var o=n(218);function r(){}function i(){}i.resetWarningCache=r,e.exports=function(){function e(e,t,n,r,i,a){if(a!==o){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:r};return n.PropTypes=n,n}},507:(e,t,n)=>{"use strict";n.d(t,{c:()=>f});var o=n(286),r=n(384);let i;const a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>{if(void 0===i){const t=void 0!==e.style.animationName,n=void 0!==e.style.webkitAnimationName;i=!t&&n?"-webkit-":""}return i},l=(e,t,n)=>{const o=t.startsWith("animation")?s(e):"";e.style.setProperty(o+t,n)},c=(e,t)=>{const n=t.startsWith("animation")?s(e):"";e.style.removeProperty(n+t)},u=[],d=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;if(void 0!==t){const n=Array.isArray(t)?t:[t];return[...e,...n]}return e},f=e=>{let t,n,i,f,h,p,m,g,b,v,y,w,$,x=[],k=[],S=[],E=!1,C={},T=[],_=[],P={},L=0,R=!1,N=!1,O=!0,I=!1,z=!0,D=!1;const A=e,M=[],B=[],V=[],j=[],H=[],F=[],W=[],U=[],q=[],Q=[],K=[],X="function"===typeof AnimationEffect||void 0!==o.w&&"function"===typeof o.w.AnimationEffect,Y="function"===typeof Element&&"function"===typeof Element.prototype.animate&&X,G=()=>K,Z=e=>{ne(),e&&oe()},J=(e,t)=>{const n=t.findIndex(t=>t.c===e);n>-1&&t.splice(n,1)},ee=(e,t)=>(((null===t||void 0===t?void 0:t.oneTimeCallback)?B:M).push({c:e,o:t}),$),te=()=>(M.length=0,B.length=0,$),ne=()=>{if(Y)K.forEach(e=>{e.cancel()}),K.length=0;else{const e=j.slice();(0,r.r)(()=>{e.forEach(e=>{c(e,"animation-name"),c(e,"animation-duration"),c(e,"animation-timing-function"),c(e,"animation-iteration-count"),c(e,"animation-delay"),c(e,"animation-play-state"),c(e,"animation-fill-mode"),c(e,"animation-direction")})})}},oe=()=>{F.forEach(e=>{(null===e||void 0===e?void 0:e.parentNode)&&e.parentNode.removeChild(e)}),F.length=0},re=()=>void 0!==h?h:m?m.getFill():"both",ie=()=>void 0!==b?b:void 0!==p?p:m?m.getDirection():"normal",ae=()=>R?"linear":void 0!==i?i:m?m.getEasing():"linear",se=()=>N?0:void 0!==v?v:void 0!==n?n:m?m.getDuration():0,le=()=>void 0!==f?f:m?m.getIterations():1,ce=()=>void 0!==y?y:void 0!==t?t:m?m.getDelay():0,ue=e=>{Y?G().forEach(t=>{const n=t.effect;if(n.setKeyframes)n.setKeyframes(e);else{const o=new KeyframeEffect(n.target,e,n.getTiming());t.effect=o}}):fe()},de=()=>{0!==L&&(L--,0===L&&((()=>{we(),q.forEach(e=>e()),Q.forEach(e=>e());const e=O?1:0,t=T,n=_,o=P;j.forEach(e=>{const r=e.classList;t.forEach(e=>r.add(e)),n.forEach(e=>r.remove(e));for(const t in o)o.hasOwnProperty(t)&&l(e,t,o[t])}),v=void 0,b=void 0,y=void 0,M.forEach(t=>t.c(e,$)),B.forEach(t=>t.c(e,$)),B.length=0,z=!0,O&&(I=!0),O=!0})(),m&&m.animationFinish()))},fe=function(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];oe();const n=(e=>(e.forEach(e=>{for(const t in e)if(e.hasOwnProperty(t)){const n=e[t];if("easing"===t)e["animation-timing-function"]=n,delete e[t];else{const o=a(t);o!==t&&(e[o]=n,delete e[t])}}}),e))(x);j.forEach(o=>{if(n.length>0){const i=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map(e=>{const t=e.offset,n=[];for(const o in e)e.hasOwnProperty(o)&&"offset"!==o&&n.push(`${o}: ${e[o]};`);return`${100*t}% { ${n.join(" ")} }`}).join(" ")}(n);w=void 0!==e?e:(e=>{let t=u.indexOf(e);return t<0&&(t=u.push(e)-1),`ion-animation-${t}`})(i);const a=((e,t,n)=>{var o;const r=(e=>{const t=void 0!==e.getRootNode?e.getRootNode():e;return t.head||t})(n),i=s(n),a=r.querySelector("#"+e);if(a)return a;const l=(null!==(o=n.ownerDocument)&&void 0!==o?o:document).createElement("style");return l.id=e,l.textContent=`@${i}keyframes ${e} { ${t} } @${i}keyframes ${e}-alt { ${t} }`,r.appendChild(l),l})(w,i,o);F.push(a),l(o,"animation-duration",`${se()}ms`),l(o,"animation-timing-function",ae()),l(o,"animation-delay",`${ce()}ms`),l(o,"animation-fill-mode",re()),l(o,"animation-direction",ie());const c=le()===1/0?"infinite":le().toString();l(o,"animation-iteration-count",c),l(o,"animation-play-state","paused"),t&&l(o,"animation-name",`${a.id}-alt`),(0,r.r)(()=>{l(o,"animation-name",a.id||null)})}})},he=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];(()=>{W.forEach(e=>e()),U.forEach(e=>e());const e=k,t=S,n=C;j.forEach(o=>{const r=o.classList;e.forEach(e=>r.add(e)),t.forEach(e=>r.remove(e));for(const e in n)n.hasOwnProperty(e)&&l(o,e,n[e])})})(),x.length>0&&(Y?(j.forEach(e=>{const t=e.animate(x,{id:A,delay:ce(),duration:se(),easing:ae(),iterations:le(),fill:re(),direction:ie()});t.pause(),K.push(t)}),K.length>0&&(K[0].onfinish=()=>{de()})):fe(e)),E=!0},pe=e=>{if(e=Math.min(Math.max(e,0),.9999),Y)K.forEach(t=>{t.currentTime=t.effect.getComputedTiming().delay+se()*e,t.pause()});else{const t=`-${se()*e}ms`;j.forEach(e=>{x.length>0&&(l(e,"animation-delay",t),l(e,"animation-play-state","paused"))})}},me=e=>{K.forEach(e=>{e.effect.updateTiming({delay:ce(),duration:se(),easing:ae(),iterations:le(),fill:re(),direction:ie()})}),void 0!==e&&pe(e)},ge=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1?arguments[1]:void 0;(0,r.r)(()=>{j.forEach(n=>{l(n,"animation-name",w||null),l(n,"animation-duration",`${se()}ms`),l(n,"animation-timing-function",ae()),l(n,"animation-delay",void 0!==t?`-${t*se()}ms`:`${ce()}ms`),l(n,"animation-fill-mode",re()||null),l(n,"animation-direction",ie()||null);const o=le()===1/0?"infinite":le().toString();l(n,"animation-iteration-count",o),e&&l(n,"animation-name",`${w}-alt`),(0,r.r)(()=>{l(n,"animation-name",w||null)})})})},be=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2?arguments[2]:void 0;return e&&H.forEach(o=>{o.update(e,t,n)}),Y?me(n):ge(t,n),$},ve=()=>{E&&(Y?K.forEach(e=>{e.pause()}):j.forEach(e=>{l(e,"animation-play-state","paused")}),D=!0)},ye=()=>{g=void 0,de()},we=()=>{g&&clearTimeout(g)},$e=()=>{j.forEach(e=>{c(e,"animation-duration"),c(e,"animation-delay"),c(e,"animation-play-state")})},xe=e=>new Promise(t=>{(null===e||void 0===e?void 0:e.sync)&&(N=!0,ee(()=>N=!1,{oneTimeCallback:!0})),E||he(),I&&(Y?(pe(0),me()):ge(),I=!1),z&&(L=H.length+1,z=!1);const n=()=>{J(o,B),t()},o=()=>{J(n,V),t()};ee(o,{oneTimeCallback:!0}),((e,t)=>{V.push({c:e,o:t})})(n,{oneTimeCallback:!0}),H.forEach(e=>{e.play()}),Y?(K.forEach(e=>{e.play()}),0!==x.length&&0!==j.length||de()):(()=>{if(we(),(0,r.r)(()=>{j.forEach(e=>{x.length>0&&l(e,"animation-play-state","running")})}),0===x.length||0===j.length)de();else{const e=ce()||0,t=se()||0,n=le()||1;isFinite(n)&&(g=setTimeout(ye,e+t*n+100)),((e,t)=>{let n;const o={passive:!0},r=()=>{n&&n()},i=n=>{e===n.target&&(r(),t(n))};e&&(e.addEventListener("webkitAnimationEnd",i,o),e.addEventListener("animationend",i,o),n=()=>{e.removeEventListener("webkitAnimationEnd",i,o),e.removeEventListener("animationend",i,o)})})(j[0],()=>{we(),(0,r.r)(()=>{$e(),(0,r.r)(de)})})}})(),D=!1}),ke=(e,t)=>{const n=x[0];return void 0===n||void 0!==n.offset&&0!==n.offset?x=[{offset:0,[e]:t},...x]:n[e]=t,$};return $={parentAnimation:m,elements:j,childAnimations:H,id:A,animationFinish:de,from:ke,to:(e,t)=>{const n=x[x.length-1];return void 0===n||void 0!==n.offset&&1!==n.offset?x=[...x,{offset:1,[e]:t}]:n[e]=t,$},fromTo:(e,t,n)=>ke(e,t).to(e,n),parent:e=>(m=e,$),play:xe,pause:()=>(H.forEach(e=>{e.pause()}),ve(),$),stop:()=>{H.forEach(e=>{e.stop()}),E&&(ne(),E=!1),R=!1,N=!1,z=!0,b=void 0,v=void 0,y=void 0,L=0,I=!1,O=!0,D=!1,V.forEach(e=>e.c(0,$)),V.length=0},destroy:e=>(H.forEach(t=>{t.destroy(e)}),Z(e),j.length=0,H.length=0,x.length=0,te(),E=!1,z=!0,$),keyframes:e=>{const t=x!==e;return x=e,t&&ue(x),$},addAnimation:e=>{if(null!=e)if(Array.isArray(e))for(const t of e)t.parent($),H.push(t);else e.parent($),H.push(e);return $},addElement:e=>{if(null!=e)if(1===e.nodeType)j.push(e);else if(e.length>=0)for(let t=0;t<e.length;t++)j.push(e[t]);else console.error("Invalid addElement value");return $},update:be,fill:e=>(h=e,be(!0),$),direction:e=>(p=e,be(!0),$),iterations:e=>(f=e,be(!0),$),duration:e=>(Y||0!==e||(e=1),n=e,be(!0),$),easing:e=>(i=e,be(!0),$),delay:e=>(t=e,be(!0),$),getWebAnimations:G,getKeyframes:()=>x,getFill:re,getDirection:ie,getDelay:ce,getIterations:le,getEasing:ae,getDuration:se,afterAddRead:e=>(q.push(e),$),afterAddWrite:e=>(Q.push(e),$),afterClearStyles:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];for(const t of e)P[t]="";return $},afterStyles:function(){return P=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},$},afterRemoveClass:e=>(_=d(_,e),$),afterAddClass:e=>(T=d(T,e),$),beforeAddRead:e=>(W.push(e),$),beforeAddWrite:e=>(U.push(e),$),beforeClearStyles:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];for(const t of e)C[t]="";return $},beforeStyles:function(){return C=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},$},beforeRemoveClass:e=>(S=d(S,e),$),beforeAddClass:e=>(k=d(k,e),$),onFinish:ee,isRunning:()=>0!==L&&!D,progressStart:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;return H.forEach(n=>{n.progressStart(e,t)}),ve(),R=e,E||he(),be(!1,!0,t),$},progressStep:e=>(H.forEach(t=>{t.progressStep(e)}),pe(e),$),progressEnd:(e,t,n)=>(R=!1,H.forEach(o=>{o.progressEnd(e,t,n)}),void 0!==n&&(v=n),I=!1,O=!0,0===e?(b="reverse"===ie()?"normal":"reverse","reverse"===b&&(O=!1),Y?(be(),pe(1-t)):(y=(1-t)*se()*-1,be(!1,!1))):1===e&&(Y?(be(),pe(t)):(y=t*se()*-1,be(!1,!1))),void 0===e||m||xe(),$)}}},579:(e,t,n)=>{"use strict";e.exports=n(153)},619:(e,t,n)=>{"use strict";n.d(t,{a:()=>c,b:()=>x,c:()=>i,i:()=>k});var o=n(441);class r{constructor(){this.m=new Map}reset(e){this.m=new Map(Object.entries(e))}get(e,t){const n=this.m.get(e);return void 0!==n?n:t}getBoolean(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.m.get(e);return void 0===n?t:"string"===typeof n?"true"===n:!!n}getNumber(e,t){const n=parseFloat(this.m.get(e));return isNaN(n)?void 0!==t?t:NaN:n}set(e,t){this.m.set(e,t)}}const i=new r,a=(e,t)=>e.substr(0,t.length)===t,s="ionic:",l="ionic-persist-config",c=(e,t)=>{return"string"===typeof e&&(t=e,e=void 0),(n=e,u(n)).includes(t);var n},u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;if("undefined"===typeof e)return[];e.Ionic=e.Ionic||{};let t=e.Ionic.platforms;return null==t&&(t=e.Ionic.platforms=d(e),t.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),t},d=e=>{const t=i.get("platform");return Object.keys(w).filter(n=>{const o=null===t||void 0===t?void 0:t[n];return"function"===typeof o?o(e):w[n](e)})},f=e=>!!v(e,/iPad/i)||!(!v(e,/Macintosh/i)||!p(e)),h=e=>v(e,/android|sink/i),p=e=>y(e,"(any-pointer:coarse)"),m=e=>g(e)||b(e),g=e=>!!(e.cordova||e.phonegap||e.PhoneGap),b=e=>{const t=e.Capacitor;return!!(null===t||void 0===t?void 0:t.isNative)},v=(e,t)=>t.test(e.navigator.userAgent),y=(e,t)=>{var n;return null===(n=e.matchMedia)||void 0===n?void 0:n.call(e,t).matches},w={ipad:f,iphone:e=>v(e,/iPhone/i),ios:e=>v(e,/iPhone|iPod/i)||f(e),android:h,phablet:e=>{const t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),r=Math.max(t,n);return o>390&&o<520&&r>620&&r<800},tablet:e=>{const t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),r=Math.max(t,n);return f(e)||(e=>h(e)&&!v(e,/mobile/i))(e)||o>460&&o<820&&r>780&&r<1400},cordova:g,capacitor:b,electron:e=>v(e,/electron/i),pwa:e=>{var t;return!(!(null===(t=e.matchMedia)||void 0===t?void 0:t.call(e,"(display-mode: standalone)").matches)&&!e.navigator.standalone)},mobile:p,mobileweb:e=>p(e)&&!m(e),desktop:e=>!p(e),hybrid:m};let $;const x=e=>e&&(0,o.Wi)(e)||$,k=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("undefined"===typeof window)return;const t=window.document,n=window,r=n.Ionic=n.Ionic||{},d={};e._ael&&(d.ael=e._ael),e._rel&&(d.rel=e._rel),e._ce&&(d.ce=e._ce),(0,o.zb)(d);const f=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(e=>{try{const t=e.sessionStorage.getItem(l);return null!==t?JSON.parse(t):{}}catch(t){return{}}})(n)),{persistConfig:!1}),r.config),(e=>{const t={};return e.location.search.slice(1).split("&").map(e=>e.split("=")).map(e=>{let[t,n]=e;return[decodeURIComponent(t),decodeURIComponent(n)]}).filter(e=>{let[t]=e;return a(t,s)}).map(e=>{let[t,n]=e;return[t.slice(s.length),n]}).forEach(e=>{let[n,o]=e;t[n]=o}),t})(n)),e);i.reset(f),i.getBoolean("persistConfig")&&((e,t)=>{try{e.sessionStorage.setItem(l,JSON.stringify(t))}catch(n){return}})(n,f),u(n),r.config=i,r.mode=$=i.get("mode",t.documentElement.getAttribute("mode")||(c(n,"ios")?"ios":"md")),i.set("mode",$),t.documentElement.setAttribute("mode",$),t.documentElement.classList.add($),i.getBoolean("_testing")&&i.set("animated",!1);const h=e=>{var t;return null===(t=e.tagName)||void 0===t?void 0:t.startsWith("ION-")},p=e=>["ios","md"].includes(e);(0,o.iY)(e=>{for(;e;){const t=e.mode||e.getAttribute("mode");if(t){if(p(t))return t;h(e)&&console.warn('Invalid ionic mode: "'+t+'", expected: "ios" or "md"')}e=e.parentElement}return $})}},681:(e,t,n)=>{"use strict";n(5)},721:(e,t,n)=>{"use strict";n.d(t,{a:()=>d,c:()=>h,f:()=>u,g:()=>c,p:()=>p,s:()=>f});var o=n(384),r=n(435);const i="ion-content",a=".ion-content-scroll-host",s=`${i}, ${a}`,l=e=>"ION-CONTENT"===e.tagName,c=async e=>l(e)?(await new Promise(t=>(0,o.c)(e,t)),e.getScrollElement()):e,u=e=>{const t=e.querySelector(a);return t||e.querySelector(s)},d=e=>e.closest(s),f=(e,t)=>{if(l(e)){return e.scrollToTop(t)}return Promise.resolve(e.scrollTo({top:0,left:0,behavior:t>0?"smooth":"auto"}))},h=(e,t,n,o)=>{if(l(e)){return e.scrollByPoint(t,n,o)}return Promise.resolve(e.scrollBy({top:n,left:t,behavior:o>0?"smooth":"auto"}))},p=e=>(0,r.b)(e,i)},730:(e,t,n)=>{"use strict";var o=n(43),r=n(853);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,s={};function l(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(s[e]=t,e=0;e<t.length;e++)a.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,o,r,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=o,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var b=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function y(e,t,n,o){var r=g.hasOwnProperty(t)?g[t]:null;(null!==r?0!==r.type:o||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,o){if(null===t||"undefined"===typeof t||function(e,t,n,o){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!o&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,o))return!0;if(o)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,r,o)&&(n=null),o||null===r?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):r.mustUseProperty?e[r.propertyName]=null===n?3!==r.type&&"":n:(t=r.attributeName,o=r.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(r=r.type)||4===r&&!0===n?"":""+n,o?e.setAttributeNS(o,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(b,v);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(b,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(b,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var w=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$=Symbol.for("react.element"),x=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),S=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),T=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),L=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),N=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var O=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var I=Symbol.iterator;function z(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=I&&e[I]||e["@@iterator"])?e:null}var D,A=Object.assign;function M(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var B=!1;function V(e,t){if(!e||B)return"";B=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var o=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){o=c}e.call(t.prototype)}else{try{throw Error()}catch(c){o=c}e()}}catch(c){if(c&&o&&"string"===typeof c.stack){for(var r=c.stack.split("\n"),i=o.stack.split("\n"),a=r.length-1,s=i.length-1;1<=a&&0<=s&&r[a]!==i[s];)s--;for(;1<=a&&0<=s;a--,s--)if(r[a]!==i[s]){if(1!==a||1!==s)do{if(a--,0>--s||r[a]!==i[s]){var l="\n"+r[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=a&&0<=s);break}}}finally{B=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?M(e):""}function j(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return e=V(e.type,!1);case 11:return e=V(e.type.render,!1);case 1:return e=V(e.type,!0);default:return""}}function H(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case x:return"Portal";case E:return"Profiler";case S:return"StrictMode";case P:return"Suspense";case L:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case _:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case R:return null!==(t=e.displayName||null)?t:H(e.type)||"Memo";case N:t=e._payload,e=e._init;try{return H(e(t))}catch(n){}}return null}function F(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return H(t);case 8:return t===S?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function U(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=U(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var r=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(e){o=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return o},setValue:function(e){o=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),o="";return e&&(o=U(e)?e.checked?"true":"false":e.value),(e=o)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function X(e,t){var n=t.checked;return A({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,o=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:o,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function G(e,t){null!=(t=t.checked)&&y(e,"checked",t,!1)}function Z(e,t){G(e,t);var n=W(t.value),o=t.type;if(null!=n)"number"===o?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===o||"reset"===o)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(!("submit"!==o&&"reset"!==o||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,o){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&o&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,r=0;r<e.length;r++){if(e[r].value===n)return e[r].selected=!0,void(o&&(e[r].defaultSelected=!0));null!==t||e[r].disabled||(t=e[r])}null!==t&&(t.selected=!0)}}function oe(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return A({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function re(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(i(92));if(te(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function ie(e,t){var n=W(t.value),o=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=o&&(e.defaultValue=""+o)}function ae(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,o){MSApp.execUnsafeLocalFunction(function(){return ue(e,t)})}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var o=0===n.indexOf("--"),r=me(n,t[n],o);"float"===n&&(n="cssFloat"),o?e.setProperty(n,r):e[n]=r}}Object.keys(he).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]})});var be=A({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(be[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(i(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function $e(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var xe=null,ke=null,Se=null;function Ee(e){if(e=wr(e)){if("function"!==typeof xe)throw Error(i(280));var t=e.stateNode;t&&(t=xr(t),xe(e.stateNode,e.type,t))}}function Ce(e){ke?Se?Se.push(e):Se=[e]:ke=e}function Te(){if(ke){var e=ke,t=Se;if(Se=ke=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function _e(e,t){return e(t)}function Pe(){}var Le=!1;function Re(e,t,n){if(Le)return e(t,n);Le=!0;try{return _e(e,t,n)}finally{Le=!1,(null!==ke||null!==Se)&&(Pe(),Te())}}function Ne(e,t){var n=e.stateNode;if(null===n)return null;var o=xr(n);if(null===o)return null;n=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(o=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!o;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Oe=!1;if(u)try{var Ie={};Object.defineProperty(Ie,"passive",{get:function(){Oe=!0}}),window.addEventListener("test",Ie,Ie),window.removeEventListener("test",Ie,Ie)}catch(ue){Oe=!1}function ze(e,t,n,o,r,i,a,s,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var De=!1,Ae=null,Me=!1,Be=null,Ve={onError:function(e){De=!0,Ae=e}};function je(e,t,n,o,r,i,a,s,l){De=!1,Ae=null,ze.apply(Ve,arguments)}function He(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Fe(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function We(e){if(He(e)!==e)throw Error(i(188))}function Ue(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=He(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,o=t;;){var r=n.return;if(null===r)break;var a=r.alternate;if(null===a){if(null!==(o=r.return)){n=o;continue}break}if(r.child===a.child){for(a=r.child;a;){if(a===n)return We(r),e;if(a===o)return We(r),t;a=a.sibling}throw Error(i(188))}if(n.return!==o.return)n=r,o=a;else{for(var s=!1,l=r.child;l;){if(l===n){s=!0,n=r,o=a;break}if(l===o){s=!0,o=r,n=a;break}l=l.sibling}if(!s){for(l=a.child;l;){if(l===n){s=!0,n=a,o=r;break}if(l===o){s=!0,o=a,n=r;break}l=l.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==o)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var Qe=r.unstable_scheduleCallback,Ke=r.unstable_cancelCallback,Xe=r.unstable_shouldYield,Ye=r.unstable_requestPaint,Ge=r.unstable_now,Ze=r.unstable_getCurrentPriorityLevel,Je=r.unstable_ImmediatePriority,et=r.unstable_UserBlockingPriority,tt=r.unstable_NormalPriority,nt=r.unstable_LowPriority,ot=r.unstable_IdlePriority,rt=null,it=null;var at=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var o=0,r=e.suspendedLanes,i=e.pingedLanes,a=268435455&n;if(0!==a){var s=a&~r;0!==s?o=dt(s):0!==(i&=a)&&(o=dt(i))}else 0!==(a=n&~r)?o=dt(a):0!==i&&(o=dt(i));if(0===o)return 0;if(0!==t&&t!==o&&0===(t&r)&&((r=o&-o)>=(i=t&-t)||16===r&&0!==(4194240&i)))return t;if(0!==(4&o)&&(o|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=o;0<t;)r=1<<(n=31-at(t)),o|=e[n],t&=~r;return o}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function bt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-at(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var o=31-at(n),r=1<<o;r&t|e[o]&t&&(e[o]|=t),n&=~r}}var yt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var $t,xt,kt,St,Et,Ct=!1,Tt=[],_t=null,Pt=null,Lt=null,Rt=new Map,Nt=new Map,Ot=[],It="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Lt=null;break;case"pointerover":case"pointerout":Rt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Nt.delete(t.pointerId)}}function Dt(e,t,n,o,r,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:o,nativeEvent:i,targetContainers:[r]},null!==t&&(null!==(t=wr(t))&&xt(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,null!==r&&-1===t.indexOf(r)&&t.push(r),e)}function At(e){var t=yr(e.target);if(null!==t){var n=He(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Fe(n)))return e.blockedOn=t,void Et(e.priority,function(){kt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Xt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=wr(n))&&xt(t),e.blockedOn=n,!1;var o=new(n=e.nativeEvent).constructor(n.type,n);we=o,n.target.dispatchEvent(o),we=null,t.shift()}return!0}function Bt(e,t,n){Mt(e)&&n.delete(t)}function Vt(){Ct=!1,null!==_t&&Mt(_t)&&(_t=null),null!==Pt&&Mt(Pt)&&(Pt=null),null!==Lt&&Mt(Lt)&&(Lt=null),Rt.forEach(Bt),Nt.forEach(Bt)}function jt(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Vt)))}function Ht(e){function t(t){return jt(t,e)}if(0<Tt.length){jt(Tt[0],e);for(var n=1;n<Tt.length;n++){var o=Tt[n];o.blockedOn===e&&(o.blockedOn=null)}}for(null!==_t&&jt(_t,e),null!==Pt&&jt(Pt,e),null!==Lt&&jt(Lt,e),Rt.forEach(t),Nt.forEach(t),n=0;n<Ot.length;n++)(o=Ot[n]).blockedOn===e&&(o.blockedOn=null);for(;0<Ot.length&&null===(n=Ot[0]).blockedOn;)At(n),null===n.blockedOn&&Ot.shift()}var Ft=w.ReactCurrentBatchConfig,Wt=!0;function Ut(e,t,n,o){var r=yt,i=Ft.transition;Ft.transition=null;try{yt=1,Qt(e,t,n,o)}finally{yt=r,Ft.transition=i}}function qt(e,t,n,o){var r=yt,i=Ft.transition;Ft.transition=null;try{yt=4,Qt(e,t,n,o)}finally{yt=r,Ft.transition=i}}function Qt(e,t,n,o){if(Wt){var r=Xt(e,t,n,o);if(null===r)Uo(e,t,o,Kt,n),zt(e,o);else if(function(e,t,n,o,r){switch(t){case"focusin":return _t=Dt(_t,e,t,n,o,r),!0;case"dragenter":return Pt=Dt(Pt,e,t,n,o,r),!0;case"mouseover":return Lt=Dt(Lt,e,t,n,o,r),!0;case"pointerover":var i=r.pointerId;return Rt.set(i,Dt(Rt.get(i)||null,e,t,n,o,r)),!0;case"gotpointercapture":return i=r.pointerId,Nt.set(i,Dt(Nt.get(i)||null,e,t,n,o,r)),!0}return!1}(r,e,t,n,o))o.stopPropagation();else if(zt(e,o),4&t&&-1<It.indexOf(e)){for(;null!==r;){var i=wr(r);if(null!==i&&$t(i),null===(i=Xt(e,t,n,o))&&Uo(e,t,o,Kt,n),i===r)break;r=i}null!==r&&o.stopPropagation()}else Uo(e,t,o,null,n)}}var Kt=null;function Xt(e,t,n,o){if(Kt=null,null!==(e=yr(e=$e(o))))if(null===(t=He(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Fe(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Yt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case ot:return 536870912;default:return 16}default:return 16}}var Gt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,o=n.length,r="value"in Gt?Gt.value:Gt.textContent,i=r.length;for(e=0;e<o&&n[e]===r[e];e++);var a=o-e;for(t=1;t<=a&&n[o-t]===r[i-t];t++);return Jt=r.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function on(){return!1}function rn(e){function t(t,n,o,r,i){for(var a in this._reactName=t,this._targetInst=o,this.type=n,this.nativeEvent=r,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(r):r[a]);return this.isDefaultPrevented=(null!=r.defaultPrevented?r.defaultPrevented:!1===r.returnValue)?nn:on,this.isPropagationStopped=on,this}return A(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,sn,ln,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=rn(cn),dn=A({},cn,{view:0,detail:0}),fn=rn(dn),hn=A({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=rn(hn),mn=rn(A({},hn,{dataTransfer:0})),gn=rn(A({},dn,{relatedTarget:0})),bn=rn(A({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=A({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),yn=rn(vn),wn=rn(A({},cn,{data:0})),$n={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},xn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function En(){return Sn}var Cn=A({},dn,{key:function(e){if(e.key){var t=$n[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?xn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Tn=rn(Cn),_n=rn(A({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=rn(A({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Ln=rn(A({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Rn=A({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Nn=rn(Rn),On=[9,13,27,32],In=u&&"CompositionEvent"in window,zn=null;u&&"documentMode"in document&&(zn=document.documentMode);var Dn=u&&"TextEvent"in window&&!zn,An=u&&(!In||zn&&8<zn&&11>=zn),Mn=String.fromCharCode(32),Bn=!1;function Vn(e,t){switch(e){case"keyup":return-1!==On.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function jn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Hn=!1;var Fn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Fn[e.type]:"textarea"===t}function Un(e,t,n,o){Ce(o),0<(t=Qo(t,"onChange")).length&&(n=new un("onChange","change",null,n,o),e.push({event:n,listeners:t}))}var qn=null,Qn=null;function Kn(e){Bo(e,0)}function Xn(e){if(Q($r(e)))return e}function Yn(e,t){if("change"===e)return t}var Gn=!1;if(u){var Zn;if(u){var Jn="oninput"in document;if(!Jn){var eo=document.createElement("div");eo.setAttribute("oninput","return;"),Jn="function"===typeof eo.oninput}Zn=Jn}else Zn=!1;Gn=Zn&&(!document.documentMode||9<document.documentMode)}function to(){qn&&(qn.detachEvent("onpropertychange",no),Qn=qn=null)}function no(e){if("value"===e.propertyName&&Xn(Qn)){var t=[];Un(t,Qn,e,$e(e)),Re(Kn,t)}}function oo(e,t,n){"focusin"===e?(to(),Qn=n,(qn=t).attachEvent("onpropertychange",no)):"focusout"===e&&to()}function ro(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Xn(Qn)}function io(e,t){if("click"===e)return Xn(t)}function ao(e,t){if("input"===e||"change"===e)return Xn(t)}var so="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lo(e,t){if(so(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(o=0;o<n.length;o++){var r=n[o];if(!d.call(t,r)||!so(e[r],t[r]))return!1}return!0}function co(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function uo(e,t){var n,o=co(e);for(e=0;o;){if(3===o.nodeType){if(n=e+o.textContent.length,e<=t&&n>=t)return{node:o,offset:t-e};e=n}e:{for(;o;){if(o.nextSibling){o=o.nextSibling;break e}o=o.parentNode}o=void 0}o=co(o)}}function fo(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fo(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function ho(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(o){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function po(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mo(e){var t=ho(),n=e.focusedElem,o=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fo(n.ownerDocument.documentElement,n)){if(null!==o&&po(n))if(t=o.start,void 0===(e=o.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var r=n.textContent.length,i=Math.min(o.start,r);o=void 0===o.end?i:Math.min(o.end,r),!e.extend&&i>o&&(r=o,o=i,i=r),r=uo(n,i);var a=uo(n,o);r&&a&&(1!==e.rangeCount||e.anchorNode!==r.node||e.anchorOffset!==r.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(r.node,r.offset),e.removeAllRanges(),i>o?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var go=u&&"documentMode"in document&&11>=document.documentMode,bo=null,vo=null,yo=null,wo=!1;function $o(e,t,n){var o=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;wo||null==bo||bo!==K(o)||("selectionStart"in(o=bo)&&po(o)?o={start:o.selectionStart,end:o.selectionEnd}:o={anchorNode:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset},yo&&lo(yo,o)||(yo=o,0<(o=Qo(vo,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:o}),t.target=bo)))}function xo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ko={animationend:xo("Animation","AnimationEnd"),animationiteration:xo("Animation","AnimationIteration"),animationstart:xo("Animation","AnimationStart"),transitionend:xo("Transition","TransitionEnd")},So={},Eo={};function Co(e){if(So[e])return So[e];if(!ko[e])return e;var t,n=ko[e];for(t in n)if(n.hasOwnProperty(t)&&t in Eo)return So[e]=n[t];return e}u&&(Eo=document.createElement("div").style,"AnimationEvent"in window||(delete ko.animationend.animation,delete ko.animationiteration.animation,delete ko.animationstart.animation),"TransitionEvent"in window||delete ko.transitionend.transition);var To=Co("animationend"),_o=Co("animationiteration"),Po=Co("animationstart"),Lo=Co("transitionend"),Ro=new Map,No="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Oo(e,t){Ro.set(e,t),l(t,[e])}for(var Io=0;Io<No.length;Io++){var zo=No[Io];Oo(zo.toLowerCase(),"on"+(zo[0].toUpperCase()+zo.slice(1)))}Oo(To,"onAnimationEnd"),Oo(_o,"onAnimationIteration"),Oo(Po,"onAnimationStart"),Oo("dblclick","onDoubleClick"),Oo("focusin","onFocus"),Oo("focusout","onBlur"),Oo(Lo,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Do="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ao=new Set("cancel close invalid load scroll toggle".split(" ").concat(Do));function Mo(e,t,n){var o=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,o,r,a,s,l,c){if(je.apply(this,arguments),De){if(!De)throw Error(i(198));var u=Ae;De=!1,Ae=null,Me||(Me=!0,Be=u)}}(o,t,void 0,e),e.currentTarget=null}function Bo(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var o=e[n],r=o.event;o=o.listeners;e:{var i=void 0;if(t)for(var a=o.length-1;0<=a;a--){var s=o[a],l=s.instance,c=s.currentTarget;if(s=s.listener,l!==i&&r.isPropagationStopped())break e;Mo(r,s,c),i=l}else for(a=0;a<o.length;a++){if(l=(s=o[a]).instance,c=s.currentTarget,s=s.listener,l!==i&&r.isPropagationStopped())break e;Mo(r,s,c),i=l}}}if(Me)throw e=Be,Me=!1,Be=null,e}function Vo(e,t){var n=t[gr];void 0===n&&(n=t[gr]=new Set);var o=e+"__bubble";n.has(o)||(Wo(t,e,2,!1),n.add(o))}function jo(e,t,n){var o=0;t&&(o|=4),Wo(n,e,o,t)}var Ho="_reactListening"+Math.random().toString(36).slice(2);function Fo(e){if(!e[Ho]){e[Ho]=!0,a.forEach(function(t){"selectionchange"!==t&&(Ao.has(t)||jo(t,!1,e),jo(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ho]||(t[Ho]=!0,jo("selectionchange",!1,t))}}function Wo(e,t,n,o){switch(Yt(t)){case 1:var r=Ut;break;case 4:r=qt;break;default:r=Qt}n=r.bind(null,t,n,e),r=void 0,!Oe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(r=!0),o?void 0!==r?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):void 0!==r?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function Uo(e,t,n,o,r){var i=o;if(0===(1&t)&&0===(2&t)&&null!==o)e:for(;;){if(null===o)return;var a=o.tag;if(3===a||4===a){var s=o.stateNode.containerInfo;if(s===r||8===s.nodeType&&s.parentNode===r)break;if(4===a)for(a=o.return;null!==a;){var l=a.tag;if((3===l||4===l)&&((l=a.stateNode.containerInfo)===r||8===l.nodeType&&l.parentNode===r))return;a=a.return}for(;null!==s;){if(null===(a=yr(s)))return;if(5===(l=a.tag)||6===l){o=i=a;continue e}s=s.parentNode}}o=o.return}Re(function(){var o=i,r=$e(n),a=[];e:{var s=Ro.get(e);if(void 0!==s){var l=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=Tn;break;case"focusin":c="focus",l=gn;break;case"focusout":c="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Pn;break;case To:case _o:case Po:l=bn;break;case Lo:l=Ln;break;case"scroll":l=fn;break;case"wheel":l=Nn;break;case"copy":case"cut":case"paste":l=yn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=_n}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==s?s+"Capture":null:s;u=[];for(var h,p=o;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Ne(p,f))&&u.push(qo(p,m,h)))),d)break;p=p.return}0<u.length&&(s=new l(s,c,null,n,r),a.push({event:s,listeners:u}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===we||!(c=n.relatedTarget||n.fromElement)||!yr(c)&&!c[mr])&&(l||s)&&(s=r.window===r?r:(s=r.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=o,null!==(c=(c=n.relatedTarget||n.toElement)?yr(c):null)&&(c!==(d=He(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(l=null,c=o),l!==c)){if(u=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(u=_n,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:$r(l),h=null==c?s:$r(c),(s=new u(m,p+"leave",l,n,r)).target=d,s.relatedTarget=h,m=null,yr(r)===o&&((u=new u(f,p+"enter",c,n,r)).target=h,u.relatedTarget=d,m=u),d=m,l&&c)e:{for(f=c,p=0,h=u=l;h;h=Ko(h))p++;for(h=0,m=f;m;m=Ko(m))h++;for(;0<p-h;)u=Ko(u),p--;for(;0<h-p;)f=Ko(f),h--;for(;p--;){if(u===f||null!==f&&u===f.alternate)break e;u=Ko(u),f=Ko(f)}u=null}else u=null;null!==l&&Xo(a,s,l,u,!1),null!==c&&null!==d&&Xo(a,d,c,u,!0)}if("select"===(l=(s=o?$r(o):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Yn;else if(Wn(s))if(Gn)g=ao;else{g=ro;var b=oo}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=io);switch(g&&(g=g(e,o))?Un(a,g,n,r):(b&&b(e,s,o),"focusout"===e&&(b=s._wrapperState)&&b.controlled&&"number"===s.type&&ee(s,"number",s.value)),b=o?$r(o):window,e){case"focusin":(Wn(b)||"true"===b.contentEditable)&&(bo=b,vo=o,yo=null);break;case"focusout":yo=vo=bo=null;break;case"mousedown":wo=!0;break;case"contextmenu":case"mouseup":case"dragend":wo=!1,$o(a,n,r);break;case"selectionchange":if(go)break;case"keydown":case"keyup":$o(a,n,r)}var v;if(In)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else Hn?Vn(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(An&&"ko"!==n.locale&&(Hn||"onCompositionStart"!==y?"onCompositionEnd"===y&&Hn&&(v=en()):(Zt="value"in(Gt=r)?Gt.value:Gt.textContent,Hn=!0)),0<(b=Qo(o,y)).length&&(y=new wn(y,e,null,n,r),a.push({event:y,listeners:b}),v?y.data=v:null!==(v=jn(n))&&(y.data=v))),(v=Dn?function(e,t){switch(e){case"compositionend":return jn(t);case"keypress":return 32!==t.which?null:(Bn=!0,Mn);case"textInput":return(e=t.data)===Mn&&Bn?null:e;default:return null}}(e,n):function(e,t){if(Hn)return"compositionend"===e||!In&&Vn(e,t)?(e=en(),Jt=Zt=Gt=null,Hn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return An&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(o=Qo(o,"onBeforeInput")).length&&(r=new wn("onBeforeInput","beforeinput",null,n,r),a.push({event:r,listeners:o}),r.data=v))}Bo(a,t)})}function qo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qo(e,t){for(var n=t+"Capture",o=[];null!==e;){var r=e,i=r.stateNode;5===r.tag&&null!==i&&(r=i,null!=(i=Ne(e,n))&&o.unshift(qo(e,i,r)),null!=(i=Ne(e,t))&&o.push(qo(e,i,r))),e=e.return}return o}function Ko(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Xo(e,t,n,o,r){for(var i=t._reactName,a=[];null!==n&&n!==o;){var s=n,l=s.alternate,c=s.stateNode;if(null!==l&&l===o)break;5===s.tag&&null!==c&&(s=c,r?null!=(l=Ne(n,i))&&a.unshift(qo(n,l,s)):r||null!=(l=Ne(n,i))&&a.push(qo(n,l,s))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var Yo=/\r\n?/g,Go=/\u0000|\uFFFD/g;function Zo(e){return("string"===typeof e?e:""+e).replace(Yo,"\n").replace(Go,"")}function Jo(e,t,n){if(t=Zo(t),Zo(e)!==t&&n)throw Error(i(425))}function er(){}var tr=null,nr=null;function or(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var rr="function"===typeof setTimeout?setTimeout:void 0,ir="function"===typeof clearTimeout?clearTimeout:void 0,ar="function"===typeof Promise?Promise:void 0,sr="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ar?function(e){return ar.resolve(null).then(e).catch(lr)}:rr;function lr(e){setTimeout(function(){throw e})}function cr(e,t){var n=t,o=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&8===r.nodeType)if("/$"===(n=r.data)){if(0===o)return e.removeChild(r),void Ht(t);o--}else"$"!==n&&"$?"!==n&&"$!"!==n||o++;n=r}while(n);Ht(t)}function ur(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function dr(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fr=Math.random().toString(36).slice(2),hr="__reactFiber$"+fr,pr="__reactProps$"+fr,mr="__reactContainer$"+fr,gr="__reactEvents$"+fr,br="__reactListeners$"+fr,vr="__reactHandles$"+fr;function yr(e){var t=e[hr];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mr]||n[hr]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=dr(e);null!==e;){if(n=e[hr])return n;e=dr(e)}return t}n=(e=n).parentNode}return null}function wr(e){return!(e=e[hr]||e[mr])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function $r(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function xr(e){return e[pr]||null}var kr=[],Sr=-1;function Er(e){return{current:e}}function Cr(e){0>Sr||(e.current=kr[Sr],kr[Sr]=null,Sr--)}function Tr(e,t){Sr++,kr[Sr]=e.current,e.current=t}var _r={},Pr=Er(_r),Lr=Er(!1),Rr=_r;function Nr(e,t){var n=e.type.contextTypes;if(!n)return _r;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var r,i={};for(r in n)i[r]=t[r];return o&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Or(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ir(){Cr(Lr),Cr(Pr)}function zr(e,t,n){if(Pr.current!==_r)throw Error(i(168));Tr(Pr,t),Tr(Lr,n)}function Dr(e,t,n){var o=e.stateNode;if(t=t.childContextTypes,"function"!==typeof o.getChildContext)return n;for(var r in o=o.getChildContext())if(!(r in t))throw Error(i(108,F(e)||"Unknown",r));return A({},n,o)}function Ar(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_r,Rr=Pr.current,Tr(Pr,e),Tr(Lr,Lr.current),!0}function Mr(e,t,n){var o=e.stateNode;if(!o)throw Error(i(169));n?(e=Dr(e,t,Rr),o.__reactInternalMemoizedMergedChildContext=e,Cr(Lr),Cr(Pr),Tr(Pr,e)):Cr(Lr),Tr(Lr,n)}var Br=null,Vr=!1,jr=!1;function Hr(e){null===Br?Br=[e]:Br.push(e)}function Fr(){if(!jr&&null!==Br){jr=!0;var e=0,t=yt;try{var n=Br;for(yt=1;e<n.length;e++){var o=n[e];do{o=o(!0)}while(null!==o)}Br=null,Vr=!1}catch(r){throw null!==Br&&(Br=Br.slice(e+1)),Qe(Je,Fr),r}finally{yt=t,jr=!1}}return null}var Wr=[],Ur=0,qr=null,Qr=0,Kr=[],Xr=0,Yr=null,Gr=1,Zr="";function Jr(e,t){Wr[Ur++]=Qr,Wr[Ur++]=qr,qr=e,Qr=t}function ei(e,t,n){Kr[Xr++]=Gr,Kr[Xr++]=Zr,Kr[Xr++]=Yr,Yr=e;var o=Gr;e=Zr;var r=32-at(o)-1;o&=~(1<<r),n+=1;var i=32-at(t)+r;if(30<i){var a=r-r%5;i=(o&(1<<a)-1).toString(32),o>>=a,r-=a,Gr=1<<32-at(t)+r|n<<r|o,Zr=i+e}else Gr=1<<i|n<<r|o,Zr=e}function ti(e){null!==e.return&&(Jr(e,1),ei(e,1,0))}function ni(e){for(;e===qr;)qr=Wr[--Ur],Wr[Ur]=null,Qr=Wr[--Ur],Wr[Ur]=null;for(;e===Yr;)Yr=Kr[--Xr],Kr[Xr]=null,Zr=Kr[--Xr],Kr[Xr]=null,Gr=Kr[--Xr],Kr[Xr]=null}var oi=null,ri=null,ii=!1,ai=null;function si(e,t){var n=Rc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function li(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,oi=e,ri=ur(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,oi=e,ri=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Yr?{id:Gr,overflow:Zr}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Rc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,oi=e,ri=null,!0);default:return!1}}function ci(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ui(e){if(ii){var t=ri;if(t){var n=t;if(!li(e,t)){if(ci(e))throw Error(i(418));t=ur(n.nextSibling);var o=oi;t&&li(e,t)?si(o,n):(e.flags=-4097&e.flags|2,ii=!1,oi=e)}}else{if(ci(e))throw Error(i(418));e.flags=-4097&e.flags|2,ii=!1,oi=e}}}function di(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;oi=e}function fi(e){if(e!==oi)return!1;if(!ii)return di(e),ii=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!or(e.type,e.memoizedProps)),t&&(t=ri)){if(ci(e))throw hi(),Error(i(418));for(;t;)si(e,t),t=ur(t.nextSibling)}if(di(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ri=ur(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ri=null}}else ri=oi?ur(e.stateNode.nextSibling):null;return!0}function hi(){for(var e=ri;e;)e=ur(e.nextSibling)}function pi(){ri=oi=null,ii=!1}function mi(e){null===ai?ai=[e]:ai.push(e)}var gi=w.ReactCurrentBatchConfig;function bi(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(i(309));var o=n.stateNode}if(!o)throw Error(i(147,e));var r=o,a=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===a?t.ref:(t=function(e){var t=r.refs;null===e?delete t[a]:t[a]=e},t._stringRef=a,t)}if("string"!==typeof e)throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function vi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yi(e){return(0,e._init)(e._payload)}function wi(e){function t(t,n){if(e){var o=t.deletions;null===o?(t.deletions=[n],t.flags|=16):o.push(n)}}function n(n,o){if(!e)return null;for(;null!==o;)t(n,o),o=o.sibling;return null}function o(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function r(e,t){return(e=Oc(e,t)).index=0,e.sibling=null,e}function a(t,n,o){return t.index=o,e?null!==(o=t.alternate)?(o=o.index)<n?(t.flags|=2,n):o:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,o){return null===t||6!==t.tag?((t=Ac(n,e.mode,o)).return=e,t):((t=r(t,n)).return=e,t)}function c(e,t,n,o){var i=n.type;return i===k?d(e,t,n.props.children,o,n.key):null!==t&&(t.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===N&&yi(i)===t.type)?((o=r(t,n.props)).ref=bi(e,t,n),o.return=e,o):((o=Ic(n.type,n.key,n.props,null,e.mode,o)).ref=bi(e,t,n),o.return=e,o)}function u(e,t,n,o){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Mc(n,e.mode,o)).return=e,t):((t=r(t,n.children||[])).return=e,t)}function d(e,t,n,o,i){return null===t||7!==t.tag?((t=zc(n,e.mode,o,i)).return=e,t):((t=r(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Ac(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case $:return(n=Ic(t.type,t.key,t.props,null,e.mode,n)).ref=bi(e,null,t),n.return=e,n;case x:return(t=Mc(t,e.mode,n)).return=e,t;case N:return f(e,(0,t._init)(t._payload),n)}if(te(t)||z(t))return(t=zc(t,e.mode,n,null)).return=e,t;vi(e,t)}return null}function h(e,t,n,o){var r=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==r?null:l(e,t,""+n,o);if("object"===typeof n&&null!==n){switch(n.$$typeof){case $:return n.key===r?c(e,t,n,o):null;case x:return n.key===r?u(e,t,n,o):null;case N:return h(e,t,(r=n._init)(n._payload),o)}if(te(n)||z(n))return null!==r?null:d(e,t,n,o,null);vi(e,n)}return null}function p(e,t,n,o,r){if("string"===typeof o&&""!==o||"number"===typeof o)return l(t,e=e.get(n)||null,""+o,r);if("object"===typeof o&&null!==o){switch(o.$$typeof){case $:return c(t,e=e.get(null===o.key?n:o.key)||null,o,r);case x:return u(t,e=e.get(null===o.key?n:o.key)||null,o,r);case N:return p(e,t,n,(0,o._init)(o._payload),r)}if(te(o)||z(o))return d(t,e=e.get(n)||null,o,r,null);vi(t,o)}return null}function m(r,i,s,l){for(var c=null,u=null,d=i,m=i=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var b=h(r,d,s[m],l);if(null===b){null===d&&(d=g);break}e&&d&&null===b.alternate&&t(r,d),i=a(b,i,m),null===u?c=b:u.sibling=b,u=b,d=g}if(m===s.length)return n(r,d),ii&&Jr(r,m),c;if(null===d){for(;m<s.length;m++)null!==(d=f(r,s[m],l))&&(i=a(d,i,m),null===u?c=d:u.sibling=d,u=d);return ii&&Jr(r,m),c}for(d=o(r,d);m<s.length;m++)null!==(g=p(d,r,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),i=a(g,i,m),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach(function(e){return t(r,e)}),ii&&Jr(r,m),c}function g(r,s,l,c){var u=z(l);if("function"!==typeof u)throw Error(i(150));if(null==(l=u.call(l)))throw Error(i(151));for(var d=u=null,m=s,g=s=0,b=null,v=l.next();null!==m&&!v.done;g++,v=l.next()){m.index>g?(b=m,m=null):b=m.sibling;var y=h(r,m,v.value,c);if(null===y){null===m&&(m=b);break}e&&m&&null===y.alternate&&t(r,m),s=a(y,s,g),null===d?u=y:d.sibling=y,d=y,m=b}if(v.done)return n(r,m),ii&&Jr(r,g),u;if(null===m){for(;!v.done;g++,v=l.next())null!==(v=f(r,v.value,c))&&(s=a(v,s,g),null===d?u=v:d.sibling=v,d=v);return ii&&Jr(r,g),u}for(m=o(r,m);!v.done;g++,v=l.next())null!==(v=p(m,r,g,v.value,c))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),s=a(v,s,g),null===d?u=v:d.sibling=v,d=v);return e&&m.forEach(function(e){return t(r,e)}),ii&&Jr(r,g),u}return function e(o,i,a,l){if("object"===typeof a&&null!==a&&a.type===k&&null===a.key&&(a=a.props.children),"object"===typeof a&&null!==a){switch(a.$$typeof){case $:e:{for(var c=a.key,u=i;null!==u;){if(u.key===c){if((c=a.type)===k){if(7===u.tag){n(o,u.sibling),(i=r(u,a.props.children)).return=o,o=i;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===N&&yi(c)===u.type){n(o,u.sibling),(i=r(u,a.props)).ref=bi(o,u,a),i.return=o,o=i;break e}n(o,u);break}t(o,u),u=u.sibling}a.type===k?((i=zc(a.props.children,o.mode,l,a.key)).return=o,o=i):((l=Ic(a.type,a.key,a.props,null,o.mode,l)).ref=bi(o,i,a),l.return=o,o=l)}return s(o);case x:e:{for(u=a.key;null!==i;){if(i.key===u){if(4===i.tag&&i.stateNode.containerInfo===a.containerInfo&&i.stateNode.implementation===a.implementation){n(o,i.sibling),(i=r(i,a.children||[])).return=o,o=i;break e}n(o,i);break}t(o,i),i=i.sibling}(i=Mc(a,o.mode,l)).return=o,o=i}return s(o);case N:return e(o,i,(u=a._init)(a._payload),l)}if(te(a))return m(o,i,a,l);if(z(a))return g(o,i,a,l);vi(o,a)}return"string"===typeof a&&""!==a||"number"===typeof a?(a=""+a,null!==i&&6===i.tag?(n(o,i.sibling),(i=r(i,a)).return=o,o=i):(n(o,i),(i=Ac(a,o.mode,l)).return=o,o=i),s(o)):n(o,i)}}var $i=wi(!0),xi=wi(!1),ki=Er(null),Si=null,Ei=null,Ci=null;function Ti(){Ci=Ei=Si=null}function _i(e){var t=ki.current;Cr(ki),e._currentValue=t}function Pi(e,t,n){for(;null!==e;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==o&&(o.childLanes|=t)):null!==o&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===n)break;e=e.return}}function Li(e,t){Si=e,Ci=Ei=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(ys=!0),e.firstContext=null)}function Ri(e){var t=e._currentValue;if(Ci!==e)if(e={context:e,memoizedValue:t,next:null},null===Ei){if(null===Si)throw Error(i(308));Ei=e,Si.dependencies={lanes:0,firstContext:e}}else Ei=Ei.next=e;return t}var Ni=null;function Oi(e){null===Ni?Ni=[e]:Ni.push(e)}function Ii(e,t,n,o){var r=t.interleaved;return null===r?(n.next=n,Oi(t)):(n.next=r.next,r.next=n),t.interleaved=n,zi(e,o)}function zi(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Di=!1;function Ai(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Mi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Bi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vi(e,t,n){var o=e.updateQueue;if(null===o)return null;if(o=o.shared,0!==(2&_l)){var r=o.pending;return null===r?t.next=t:(t.next=r.next,r.next=t),o.pending=t,zi(e,n)}return null===(r=o.interleaved)?(t.next=t,Oi(o)):(t.next=r.next,r.next=t),o.interleaved=t,zi(e,n)}function ji(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var o=t.lanes;n|=o&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Hi(e,t){var n=e.updateQueue,o=e.alternate;if(null!==o&&n===(o=o.updateQueue)){var r=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?r=i=a:i=i.next=a,n=n.next}while(null!==n);null===i?r=i=t:i=i.next=t}else r=i=t;return n={baseState:o.baseState,firstBaseUpdate:r,lastBaseUpdate:i,shared:o.shared,effects:o.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Fi(e,t,n,o){var r=e.updateQueue;Di=!1;var i=r.firstBaseUpdate,a=r.lastBaseUpdate,s=r.shared.pending;if(null!==s){r.shared.pending=null;var l=s,c=l.next;l.next=null,null===a?i=c:a.next=c,a=l;var u=e.alternate;null!==u&&((s=(u=u.updateQueue).lastBaseUpdate)!==a&&(null===s?u.firstBaseUpdate=c:s.next=c,u.lastBaseUpdate=l))}if(null!==i){var d=r.baseState;for(a=0,u=c=l=null,s=i;;){var f=s.lane,h=s.eventTime;if((o&f)===f){null!==u&&(u=u.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=A({},d,f);break e;case 2:Di=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=r.effects)?r.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===u?(c=u=h,l=d):u=u.next=h,a|=f;if(null===(s=s.next)){if(null===(s=r.shared.pending))break;s=(f=s).next,f.next=null,r.lastBaseUpdate=f,r.shared.pending=null}}if(null===u&&(l=d),r.baseState=l,r.firstBaseUpdate=c,r.lastBaseUpdate=u,null!==(t=r.shared.interleaved)){r=t;do{a|=r.lane,r=r.next}while(r!==t)}else null===i&&(r.shared.lanes=0);Dl|=a,e.lanes=a,e.memoizedState=d}}function Wi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var o=e[t],r=o.callback;if(null!==r){if(o.callback=null,o=n,"function"!==typeof r)throw Error(i(191,r));r.call(o)}}}var Ui={},qi=Er(Ui),Qi=Er(Ui),Ki=Er(Ui);function Xi(e){if(e===Ui)throw Error(i(174));return e}function Yi(e,t){switch(Tr(Ki,t),Tr(Qi,e),Tr(qi,Ui),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Cr(qi),Tr(qi,t)}function Gi(){Cr(qi),Cr(Qi),Cr(Ki)}function Zi(e){Xi(Ki.current);var t=Xi(qi.current),n=le(t,e.type);t!==n&&(Tr(Qi,e),Tr(qi,n))}function Ji(e){Qi.current===e&&(Cr(qi),Cr(Qi))}var ea=Er(0);function ta(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var na=[];function oa(){for(var e=0;e<na.length;e++)na[e]._workInProgressVersionPrimary=null;na.length=0}var ra=w.ReactCurrentDispatcher,ia=w.ReactCurrentBatchConfig,aa=0,sa=null,la=null,ca=null,ua=!1,da=!1,fa=0,ha=0;function pa(){throw Error(i(321))}function ma(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!so(e[n],t[n]))return!1;return!0}function ga(e,t,n,o,r,a){if(aa=a,sa=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ra.current=null===e||null===e.memoizedState?Ja:es,e=n(o,r),da){a=0;do{if(da=!1,fa=0,25<=a)throw Error(i(301));a+=1,ca=la=null,t.updateQueue=null,ra.current=ts,e=n(o,r)}while(da)}if(ra.current=Za,t=null!==la&&null!==la.next,aa=0,ca=la=sa=null,ua=!1,t)throw Error(i(300));return e}function ba(){var e=0!==fa;return fa=0,e}function va(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ca?sa.memoizedState=ca=e:ca=ca.next=e,ca}function ya(){if(null===la){var e=sa.alternate;e=null!==e?e.memoizedState:null}else e=la.next;var t=null===ca?sa.memoizedState:ca.next;if(null!==t)ca=t,la=e;else{if(null===e)throw Error(i(310));e={memoizedState:(la=e).memoizedState,baseState:la.baseState,baseQueue:la.baseQueue,queue:la.queue,next:null},null===ca?sa.memoizedState=ca=e:ca=ca.next=e}return ca}function wa(e,t){return"function"===typeof t?t(e):t}function $a(e){var t=ya(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var o=la,r=o.baseQueue,a=n.pending;if(null!==a){if(null!==r){var s=r.next;r.next=a.next,a.next=s}o.baseQueue=r=a,n.pending=null}if(null!==r){a=r.next,o=o.baseState;var l=s=null,c=null,u=a;do{var d=u.lane;if((aa&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),o=u.hasEagerState?u.eagerState:e(o,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(l=c=f,s=o):c=c.next=f,sa.lanes|=d,Dl|=d}u=u.next}while(null!==u&&u!==a);null===c?s=o:c.next=l,so(o,t.memoizedState)||(ys=!0),t.memoizedState=o,t.baseState=s,t.baseQueue=c,n.lastRenderedState=o}if(null!==(e=n.interleaved)){r=e;do{a=r.lane,sa.lanes|=a,Dl|=a,r=r.next}while(r!==e)}else null===r&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function xa(e){var t=ya(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var o=n.dispatch,r=n.pending,a=t.memoizedState;if(null!==r){n.pending=null;var s=r=r.next;do{a=e(a,s.action),s=s.next}while(s!==r);so(a,t.memoizedState)||(ys=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,o]}function ka(){}function Sa(e,t){var n=sa,o=ya(),r=t(),a=!so(o.memoizedState,r);if(a&&(o.memoizedState=r,ys=!0),o=o.queue,Da(Ta.bind(null,n,o,e),[e]),o.getSnapshot!==t||a||null!==ca&&1&ca.memoizedState.tag){if(n.flags|=2048,Ra(9,Ca.bind(null,n,o,r,t),void 0,null),null===Pl)throw Error(i(349));0!==(30&aa)||Ea(n,t,r)}return r}function Ea(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=sa.updateQueue)?(t={lastEffect:null,stores:null},sa.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ca(e,t,n,o){t.value=n,t.getSnapshot=o,_a(t)&&Pa(e)}function Ta(e,t,n){return n(function(){_a(t)&&Pa(e)})}function _a(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!so(e,n)}catch(o){return!0}}function Pa(e){var t=zi(e,1);null!==t&&nc(t,e,1,-1)}function La(e){var t=va();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wa,lastRenderedState:e},t.queue=e,e=e.dispatch=Ka.bind(null,sa,e),[t.memoizedState,e]}function Ra(e,t,n,o){return e={tag:e,create:t,destroy:n,deps:o,next:null},null===(t=sa.updateQueue)?(t={lastEffect:null,stores:null},sa.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(o=n.next,n.next=e,e.next=o,t.lastEffect=e),e}function Na(){return ya().memoizedState}function Oa(e,t,n,o){var r=va();sa.flags|=e,r.memoizedState=Ra(1|t,n,void 0,void 0===o?null:o)}function Ia(e,t,n,o){var r=ya();o=void 0===o?null:o;var i=void 0;if(null!==la){var a=la.memoizedState;if(i=a.destroy,null!==o&&ma(o,a.deps))return void(r.memoizedState=Ra(t,n,i,o))}sa.flags|=e,r.memoizedState=Ra(1|t,n,i,o)}function za(e,t){return Oa(8390656,8,e,t)}function Da(e,t){return Ia(2048,8,e,t)}function Aa(e,t){return Ia(4,2,e,t)}function Ma(e,t){return Ia(4,4,e,t)}function Ba(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Va(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ia(4,4,Ba.bind(null,t,e),n)}function ja(){}function Ha(e,t){var n=ya();t=void 0===t?null:t;var o=n.memoizedState;return null!==o&&null!==t&&ma(t,o[1])?o[0]:(n.memoizedState=[e,t],e)}function Fa(e,t){var n=ya();t=void 0===t?null:t;var o=n.memoizedState;return null!==o&&null!==t&&ma(t,o[1])?o[0]:(e=e(),n.memoizedState=[e,t],e)}function Wa(e,t,n){return 0===(21&aa)?(e.baseState&&(e.baseState=!1,ys=!0),e.memoizedState=n):(so(n,t)||(n=mt(),sa.lanes|=n,Dl|=n,e.baseState=!0),t)}function Ua(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var o=ia.transition;ia.transition={};try{e(!1),t()}finally{yt=n,ia.transition=o}}function qa(){return ya().memoizedState}function Qa(e,t,n){var o=tc(e);if(n={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null},Xa(e))Ya(t,n);else if(null!==(n=Ii(e,t,n,o))){nc(n,e,o,ec()),Ga(n,t,o)}}function Ka(e,t,n){var o=tc(e),r={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null};if(Xa(e))Ya(t,r);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var a=t.lastRenderedState,s=i(a,n);if(r.hasEagerState=!0,r.eagerState=s,so(s,a)){var l=t.interleaved;return null===l?(r.next=r,Oi(t)):(r.next=l.next,l.next=r),void(t.interleaved=r)}}catch(c){}null!==(n=Ii(e,t,r,o))&&(nc(n,e,o,r=ec()),Ga(n,t,o))}}function Xa(e){var t=e.alternate;return e===sa||null!==t&&t===sa}function Ya(e,t){da=ua=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ga(e,t,n){if(0!==(4194240&n)){var o=t.lanes;n|=o&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Za={readContext:Ri,useCallback:pa,useContext:pa,useEffect:pa,useImperativeHandle:pa,useInsertionEffect:pa,useLayoutEffect:pa,useMemo:pa,useReducer:pa,useRef:pa,useState:pa,useDebugValue:pa,useDeferredValue:pa,useTransition:pa,useMutableSource:pa,useSyncExternalStore:pa,useId:pa,unstable_isNewReconciler:!1},Ja={readContext:Ri,useCallback:function(e,t){return va().memoizedState=[e,void 0===t?null:t],e},useContext:Ri,useEffect:za,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Oa(4194308,4,Ba.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Oa(4194308,4,e,t)},useInsertionEffect:function(e,t){return Oa(4,2,e,t)},useMemo:function(e,t){var n=va();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var o=va();return t=void 0!==n?n(t):t,o.memoizedState=o.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},o.queue=e,e=e.dispatch=Qa.bind(null,sa,e),[o.memoizedState,e]},useRef:function(e){return e={current:e},va().memoizedState=e},useState:La,useDebugValue:ja,useDeferredValue:function(e){return va().memoizedState=e},useTransition:function(){var e=La(!1),t=e[0];return e=Ua.bind(null,e[1]),va().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var o=sa,r=va();if(ii){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===Pl)throw Error(i(349));0!==(30&aa)||Ea(o,t,n)}r.memoizedState=n;var a={value:n,getSnapshot:t};return r.queue=a,za(Ta.bind(null,o,a,e),[e]),o.flags|=2048,Ra(9,Ca.bind(null,o,a,n,t),void 0,null),n},useId:function(){var e=va(),t=Pl.identifierPrefix;if(ii){var n=Zr;t=":"+t+"R"+(n=(Gr&~(1<<32-at(Gr)-1)).toString(32)+n),0<(n=fa++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ha++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Ri,useCallback:Ha,useContext:Ri,useEffect:Da,useImperativeHandle:Va,useInsertionEffect:Aa,useLayoutEffect:Ma,useMemo:Fa,useReducer:$a,useRef:Na,useState:function(){return $a(wa)},useDebugValue:ja,useDeferredValue:function(e){return Wa(ya(),la.memoizedState,e)},useTransition:function(){return[$a(wa)[0],ya().memoizedState]},useMutableSource:ka,useSyncExternalStore:Sa,useId:qa,unstable_isNewReconciler:!1},ts={readContext:Ri,useCallback:Ha,useContext:Ri,useEffect:Da,useImperativeHandle:Va,useInsertionEffect:Aa,useLayoutEffect:Ma,useMemo:Fa,useReducer:xa,useRef:Na,useState:function(){return xa(wa)},useDebugValue:ja,useDeferredValue:function(e){var t=ya();return null===la?t.memoizedState=e:Wa(t,la.memoizedState,e)},useTransition:function(){return[xa(wa)[0],ya().memoizedState]},useMutableSource:ka,useSyncExternalStore:Sa,useId:qa,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=A({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function os(e,t,n,o){n=null===(n=n(o,t=e.memoizedState))||void 0===n?t:A({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var rs={isMounted:function(e){return!!(e=e._reactInternals)&&He(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var o=ec(),r=tc(e),i=Bi(o,r);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Vi(e,i,r))&&(nc(t,e,r,o),ji(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var o=ec(),r=tc(e),i=Bi(o,r);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Vi(e,i,r))&&(nc(t,e,r,o),ji(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),o=tc(e),r=Bi(n,o);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=Vi(e,r,o))&&(nc(t,e,o,n),ji(t,e,o))}};function is(e,t,n,o,r,i,a){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(o,i,a):!t.prototype||!t.prototype.isPureReactComponent||(!lo(n,o)||!lo(r,i))}function as(e,t,n){var o=!1,r=_r,i=t.contextType;return"object"===typeof i&&null!==i?i=Ri(i):(r=Or(t)?Rr:Pr.current,i=(o=null!==(o=t.contextTypes)&&void 0!==o)?Nr(e,r):_r),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=rs,e.stateNode=t,t._reactInternals=e,o&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=r,e.__reactInternalMemoizedMaskedChildContext=i),t}function ss(e,t,n,o){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,o),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,o),t.state!==e&&rs.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,o){var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs={},Ai(e);var i=t.contextType;"object"===typeof i&&null!==i?r.context=Ri(i):(i=Or(t)?Rr:Pr.current,r.context=Nr(e,i)),r.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(os(e,t,i,n),r.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof r.getSnapshotBeforeUpdate||"function"!==typeof r.UNSAFE_componentWillMount&&"function"!==typeof r.componentWillMount||(t=r.state,"function"===typeof r.componentWillMount&&r.componentWillMount(),"function"===typeof r.UNSAFE_componentWillMount&&r.UNSAFE_componentWillMount(),t!==r.state&&rs.enqueueReplaceState(r,r.state,null),Fi(e,n,r,o),r.state=e.memoizedState),"function"===typeof r.componentDidMount&&(e.flags|=4194308)}function cs(e,t){try{var n="",o=t;do{n+=j(o),o=o.return}while(o);var r=n}catch(i){r="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:r,digest:null}}function us(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fs="function"===typeof WeakMap?WeakMap:Map;function hs(e,t,n){(n=Bi(-1,n)).tag=3,n.payload={element:null};var o=t.value;return n.callback=function(){Wl||(Wl=!0,Ul=o),ds(0,t)},n}function ps(e,t,n){(n=Bi(-1,n)).tag=3;var o=e.type.getDerivedStateFromError;if("function"===typeof o){var r=t.value;n.payload=function(){return o(r)},n.callback=function(){ds(0,t)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof o&&(null===ql?ql=new Set([this]):ql.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var o=e.pingCache;if(null===o){o=e.pingCache=new fs;var r=new Set;o.set(t,r)}else void 0===(r=o.get(t))&&(r=new Set,o.set(t,r));r.has(n)||(r.add(n),e=Ec.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function bs(e,t,n,o,r){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Bi(-1,1)).tag=2,Vi(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=r,e)}var vs=w.ReactCurrentOwner,ys=!1;function ws(e,t,n,o){t.child=null===e?xi(t,null,n,o):$i(t,e.child,n,o)}function $s(e,t,n,o,r){n=n.render;var i=t.ref;return Li(t,r),o=ga(e,t,n,o,i,r),n=ba(),null===e||ys?(ii&&n&&ti(t),t.flags|=1,ws(e,t,o,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Ws(e,t,r))}function xs(e,t,n,o,r){if(null===e){var i=n.type;return"function"!==typeof i||Nc(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Ic(n.type,null,o,t,t.mode,r)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,ks(e,t,i,o,r))}if(i=e.child,0===(e.lanes&r)){var a=i.memoizedProps;if((n=null!==(n=n.compare)?n:lo)(a,o)&&e.ref===t.ref)return Ws(e,t,r)}return t.flags|=1,(e=Oc(i,o)).ref=t.ref,e.return=t,t.child=e}function ks(e,t,n,o,r){if(null!==e){var i=e.memoizedProps;if(lo(i,o)&&e.ref===t.ref){if(ys=!1,t.pendingProps=o=i,0===(e.lanes&r))return t.lanes=e.lanes,Ws(e,t,r);0!==(131072&e.flags)&&(ys=!0)}}return Cs(e,t,n,o,r)}function Ss(e,t,n){var o=t.pendingProps,r=o.children,i=null!==e?e.memoizedState:null;if("hidden"===o.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Tr(Ol,Nl),Nl|=n;else{if(0===(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Tr(Ol,Nl),Nl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},o=null!==i?i.baseLanes:n,Tr(Ol,Nl),Nl|=o}else null!==i?(o=i.baseLanes|n,t.memoizedState=null):o=n,Tr(Ol,Nl),Nl|=o;return ws(e,t,r,n),t.child}function Es(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Cs(e,t,n,o,r){var i=Or(n)?Rr:Pr.current;return i=Nr(t,i),Li(t,r),n=ga(e,t,n,o,i,r),o=ba(),null===e||ys?(ii&&o&&ti(t),t.flags|=1,ws(e,t,n,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Ws(e,t,r))}function Ts(e,t,n,o,r){if(Or(n)){var i=!0;Ar(t)}else i=!1;if(Li(t,r),null===t.stateNode)Fs(e,t),as(t,n,o),ls(t,n,o,r),o=!0;else if(null===e){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,c=n.contextType;"object"===typeof c&&null!==c?c=Ri(c):c=Nr(t,c=Or(n)?Rr:Pr.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof a.getSnapshotBeforeUpdate;d||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s!==o||l!==c)&&ss(t,a,o,c),Di=!1;var f=t.memoizedState;a.state=f,Fi(t,o,a,r),l=t.memoizedState,s!==o||f!==l||Lr.current||Di?("function"===typeof u&&(os(t,n,u,o),l=t.memoizedState),(s=Di||is(t,n,s,o,f,l,c))?(d||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=l),a.props=o,a.state=l,a.context=c,o=s):("function"===typeof a.componentDidMount&&(t.flags|=4194308),o=!1)}else{a=t.stateNode,Mi(e,t),s=t.memoizedProps,c=t.type===t.elementType?s:ns(t.type,s),a.props=c,d=t.pendingProps,f=a.context,"object"===typeof(l=n.contextType)&&null!==l?l=Ri(l):l=Nr(t,l=Or(n)?Rr:Pr.current);var h=n.getDerivedStateFromProps;(u="function"===typeof h||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,a,o,l),Di=!1,f=t.memoizedState,a.state=f,Fi(t,o,a,r);var p=t.memoizedState;s!==d||f!==p||Lr.current||Di?("function"===typeof h&&(os(t,n,h,o),p=t.memoizedState),(c=Di||is(t,n,c,o,f,p,l)||!1)?(u||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(o,p,l),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(o,p,l)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=p),a.props=o,a.state=p,a.context=l,o=c):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),o=!1)}return _s(e,t,n,o,i,r)}function _s(e,t,n,o,r,i){Es(e,t);var a=0!==(128&t.flags);if(!o&&!a)return r&&Mr(t,n,!1),Ws(e,t,i);o=t.stateNode,vs.current=t;var s=a&&"function"!==typeof n.getDerivedStateFromError?null:o.render();return t.flags|=1,null!==e&&a?(t.child=$i(t,e.child,null,i),t.child=$i(t,null,s,i)):ws(e,t,s,i),t.memoizedState=o.state,r&&Mr(t,n,!0),t.child}function Ps(e){var t=e.stateNode;t.pendingContext?zr(0,t.pendingContext,t.pendingContext!==t.context):t.context&&zr(0,t.context,!1),Yi(e,t.containerInfo)}function Ls(e,t,n,o,r){return pi(),mi(r),t.flags|=256,ws(e,t,n,o),t.child}var Rs,Ns,Os,Is,zs={dehydrated:null,treeContext:null,retryLane:0};function Ds(e){return{baseLanes:e,cachePool:null,transitions:null}}function As(e,t,n){var o,r=t.pendingProps,a=ea.current,s=!1,l=0!==(128&t.flags);if((o=l)||(o=(null===e||null!==e.memoizedState)&&0!==(2&a)),o?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(a|=1),Tr(ea,1&a),null===e)return ui(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=r.children,e=r.fallback,s?(r=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&r)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Dc(l,r,0,null),e=zc(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ds(n),t.memoizedState=zs,e):Ms(t,l));if(null!==(a=e.memoizedState)&&null!==(o=a.dehydrated))return function(e,t,n,o,r,a,s){if(n)return 256&t.flags?(t.flags&=-257,Bs(e,t,s,o=us(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(a=o.fallback,r=t.mode,o=Dc({mode:"visible",children:o.children},r,0,null),(a=zc(a,r,s,null)).flags|=2,o.return=t,a.return=t,o.sibling=a,t.child=o,0!==(1&t.mode)&&$i(t,e.child,null,s),t.child.memoizedState=Ds(s),t.memoizedState=zs,a);if(0===(1&t.mode))return Bs(e,t,s,null);if("$!"===r.data){if(o=r.nextSibling&&r.nextSibling.dataset)var l=o.dgst;return o=l,Bs(e,t,s,o=us(a=Error(i(419)),o,void 0))}if(l=0!==(s&e.childLanes),ys||l){if(null!==(o=Pl)){switch(s&-s){case 4:r=2;break;case 16:r=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:r=32;break;case 536870912:r=268435456;break;default:r=0}0!==(r=0!==(r&(o.suspendedLanes|s))?0:r)&&r!==a.retryLane&&(a.retryLane=r,zi(e,r),nc(o,e,r,-1))}return mc(),Bs(e,t,s,o=us(Error(i(421))))}return"$?"===r.data?(t.flags|=128,t.child=e.child,t=Tc.bind(null,e),r._reactRetry=t,null):(e=a.treeContext,ri=ur(r.nextSibling),oi=t,ii=!0,ai=null,null!==e&&(Kr[Xr++]=Gr,Kr[Xr++]=Zr,Kr[Xr++]=Yr,Gr=e.id,Zr=e.overflow,Yr=t),t=Ms(t,o.children),t.flags|=4096,t)}(e,t,l,r,o,a,n);if(s){s=r.fallback,l=t.mode,o=(a=e.child).sibling;var c={mode:"hidden",children:r.children};return 0===(1&l)&&t.child!==a?((r=t.child).childLanes=0,r.pendingProps=c,t.deletions=null):(r=Oc(a,c)).subtreeFlags=14680064&a.subtreeFlags,null!==o?s=Oc(o,s):(s=zc(s,l,n,null)).flags|=2,s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,l=null===(l=e.child.memoizedState)?Ds(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=zs,r}return e=(s=e.child).sibling,r=Oc(s,{mode:"visible",children:r.children}),0===(1&t.mode)&&(r.lanes=n),r.return=t,r.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ms(e,t){return(t=Dc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Bs(e,t,n,o){return null!==o&&mi(o),$i(t,e.child,null,n),(e=Ms(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vs(e,t,n){e.lanes|=t;var o=e.alternate;null!==o&&(o.lanes|=t),Pi(e.return,t,n)}function js(e,t,n,o,r){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:n,tailMode:r}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=o,i.tail=n,i.tailMode=r)}function Hs(e,t,n){var o=t.pendingProps,r=o.revealOrder,i=o.tail;if(ws(e,t,o.children,n),0!==(2&(o=ea.current)))o=1&o|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vs(e,n,t);else if(19===e.tag)Vs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}if(Tr(ea,o),0===(1&t.mode))t.memoizedState=null;else switch(r){case"forwards":for(n=t.child,r=null;null!==n;)null!==(e=n.alternate)&&null===ta(e)&&(r=n),n=n.sibling;null===(n=r)?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),js(t,!1,r,n,i);break;case"backwards":for(n=null,r=t.child,t.child=null;null!==r;){if(null!==(e=r.alternate)&&null===ta(e)){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}js(t,!0,n,null,i);break;case"together":js(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fs(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ws(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Dl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Oc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Oc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Us(e,t){if(!ii)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var o=null;null!==n;)null!==n.alternate&&(o=n),n=n.sibling;null===o?t||null===e.tail?e.tail=null:e.tail.sibling=null:o.sibling=null}}function qs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,o=0;if(t)for(var r=e.child;null!==r;)n|=r.lanes|r.childLanes,o|=14680064&r.subtreeFlags,o|=14680064&r.flags,r.return=e,r=r.sibling;else for(r=e.child;null!==r;)n|=r.lanes|r.childLanes,o|=r.subtreeFlags,o|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=o,e.childLanes=n,t}function Qs(e,t,n){var o=t.pendingProps;switch(ni(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qs(t),null;case 1:case 17:return Or(t.type)&&Ir(),qs(t),null;case 3:return o=t.stateNode,Gi(),Cr(Lr),Cr(Pr),oa(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),null!==e&&null!==e.child||(fi(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ai&&(ac(ai),ai=null))),Ns(e,t),qs(t),null;case 5:Ji(t);var r=Xi(Ki.current);if(n=t.type,null!==e&&null!=t.stateNode)Os(e,t,n,o,r),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(null===t.stateNode)throw Error(i(166));return qs(t),null}if(e=Xi(qi.current),fi(t)){o=t.stateNode,n=t.type;var a=t.memoizedProps;switch(o[hr]=t,o[pr]=a,e=0!==(1&t.mode),n){case"dialog":Vo("cancel",o),Vo("close",o);break;case"iframe":case"object":case"embed":Vo("load",o);break;case"video":case"audio":for(r=0;r<Do.length;r++)Vo(Do[r],o);break;case"source":Vo("error",o);break;case"img":case"image":case"link":Vo("error",o),Vo("load",o);break;case"details":Vo("toggle",o);break;case"input":Y(o,a),Vo("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!a.multiple},Vo("invalid",o);break;case"textarea":re(o,a),Vo("invalid",o)}for(var l in ve(n,a),r=null,a)if(a.hasOwnProperty(l)){var c=a[l];"children"===l?"string"===typeof c?o.textContent!==c&&(!0!==a.suppressHydrationWarning&&Jo(o.textContent,c,e),r=["children",c]):"number"===typeof c&&o.textContent!==""+c&&(!0!==a.suppressHydrationWarning&&Jo(o.textContent,c,e),r=["children",""+c]):s.hasOwnProperty(l)&&null!=c&&"onScroll"===l&&Vo("scroll",o)}switch(n){case"input":q(o),J(o,a,!0);break;case"textarea":q(o),ae(o);break;case"select":case"option":break;default:"function"===typeof a.onClick&&(o.onclick=er)}o=r,t.updateQueue=o,null!==o&&(t.flags|=4)}else{l=9===r.nodeType?r:r.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof o.is?e=l.createElement(n,{is:o.is}):(e=l.createElement(n),"select"===n&&(l=e,o.multiple?l.multiple=!0:o.size&&(l.size=o.size))):e=l.createElementNS(e,n),e[hr]=t,e[pr]=o,Rs(e,t,!1,!1),t.stateNode=e;e:{switch(l=ye(n,o),n){case"dialog":Vo("cancel",e),Vo("close",e),r=o;break;case"iframe":case"object":case"embed":Vo("load",e),r=o;break;case"video":case"audio":for(r=0;r<Do.length;r++)Vo(Do[r],e);r=o;break;case"source":Vo("error",e),r=o;break;case"img":case"image":case"link":Vo("error",e),Vo("load",e),r=o;break;case"details":Vo("toggle",e),r=o;break;case"input":Y(e,o),r=X(e,o),Vo("invalid",e);break;case"option":default:r=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},r=A({},o,{value:void 0}),Vo("invalid",e);break;case"textarea":re(e,o),r=oe(e,o),Vo("invalid",e)}for(a in ve(n,r),c=r)if(c.hasOwnProperty(a)){var u=c[a];"style"===a?ge(e,u):"dangerouslySetInnerHTML"===a?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===a?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(s.hasOwnProperty(a)?null!=u&&"onScroll"===a&&Vo("scroll",e):null!=u&&y(e,a,u,l))}switch(n){case"input":q(e),J(e,o,!1);break;case"textarea":q(e),ae(e);break;case"option":null!=o.value&&e.setAttribute("value",""+W(o.value));break;case"select":e.multiple=!!o.multiple,null!=(a=o.value)?ne(e,!!o.multiple,a,!1):null!=o.defaultValue&&ne(e,!!o.multiple,o.defaultValue,!0);break;default:"function"===typeof r.onClick&&(e.onclick=er)}switch(n){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qs(t),null;case 6:if(e&&null!=t.stateNode)Is(e,t,e.memoizedProps,o);else{if("string"!==typeof o&&null===t.stateNode)throw Error(i(166));if(n=Xi(Ki.current),Xi(qi.current),fi(t)){if(o=t.stateNode,n=t.memoizedProps,o[hr]=t,(a=o.nodeValue!==n)&&null!==(e=oi))switch(e.tag){case 3:Jo(o.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jo(o.nodeValue,n,0!==(1&e.mode))}a&&(t.flags|=4)}else(o=(9===n.nodeType?n:n.ownerDocument).createTextNode(o))[hr]=t,t.stateNode=o}return qs(t),null;case 13:if(Cr(ea),o=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ii&&null!==ri&&0!==(1&t.mode)&&0===(128&t.flags))hi(),pi(),t.flags|=98560,a=!1;else if(a=fi(t),null!==o&&null!==o.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[hr]=t}else pi(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qs(t),a=!1}else null!==ai&&(ac(ai),ai=null),a=!0;if(!a)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((o=null!==o)!==(null!==e&&null!==e.memoizedState)&&o&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ea.current)?0===Il&&(Il=3):mc())),null!==t.updateQueue&&(t.flags|=4),qs(t),null);case 4:return Gi(),Ns(e,t),null===e&&Fo(t.stateNode.containerInfo),qs(t),null;case 10:return _i(t.type._context),qs(t),null;case 19:if(Cr(ea),null===(a=t.memoizedState))return qs(t),null;if(o=0!==(128&t.flags),null===(l=a.rendering))if(o)Us(a,!1);else{if(0!==Il||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ta(e))){for(t.flags|=128,Us(a,!1),null!==(o=l.updateQueue)&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=n,n=t.child;null!==n;)e=o,(a=n).flags&=14680066,null===(l=a.alternate)?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=l.childLanes,a.lanes=l.lanes,a.child=l.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=l.memoizedProps,a.memoizedState=l.memoizedState,a.updateQueue=l.updateQueue,a.type=l.type,e=l.dependencies,a.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Tr(ea,1&ea.current|2),t.child}e=e.sibling}null!==a.tail&&Ge()>Hl&&(t.flags|=128,o=!0,Us(a,!1),t.lanes=4194304)}else{if(!o)if(null!==(e=ta(l))){if(t.flags|=128,o=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Us(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!ii)return qs(t),null}else 2*Ge()-a.renderingStartTime>Hl&&1073741824!==n&&(t.flags|=128,o=!0,Us(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=a.last)?n.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=Ge(),t.sibling=null,n=ea.current,Tr(ea,o?1&n|2:1&n),t):(qs(t),null);case 22:case 23:return dc(),o=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==o&&(t.flags|=8192),o&&0!==(1&t.mode)?0!==(1073741824&Nl)&&(qs(t),6&t.subtreeFlags&&(t.flags|=8192)):qs(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function Ks(e,t){switch(ni(t),t.tag){case 1:return Or(t.type)&&Ir(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Gi(),Cr(Lr),Cr(Pr),oa(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Ji(t),null;case 13:if(Cr(ea),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));pi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Cr(ea),null;case 4:return Gi(),null;case 10:return _i(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Rs=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ns=function(){},Os=function(e,t,n,o){var r=e.memoizedProps;if(r!==o){e=t.stateNode,Xi(qi.current);var i,a=null;switch(n){case"input":r=X(e,r),o=X(e,o),a=[];break;case"select":r=A({},r,{value:void 0}),o=A({},o,{value:void 0}),a=[];break;case"textarea":r=oe(e,r),o=oe(e,o),a=[];break;default:"function"!==typeof r.onClick&&"function"===typeof o.onClick&&(e.onclick=er)}for(u in ve(n,o),n=null,r)if(!o.hasOwnProperty(u)&&r.hasOwnProperty(u)&&null!=r[u])if("style"===u){var l=r[u];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(s.hasOwnProperty(u)?a||(a=[]):(a=a||[]).push(u,null));for(u in o){var c=o[u];if(l=null!=r?r[u]:void 0,o.hasOwnProperty(u)&&c!==l&&(null!=c||null!=l))if("style"===u)if(l){for(i in l)!l.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&l[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(a||(a=[]),a.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,l=l?l.__html:void 0,null!=c&&l!==c&&(a=a||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(a=a||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(s.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Vo("scroll",e),a||l===c||(a=[])):(a=a||[]).push(u,c))}n&&(a=a||[]).push("style",n);var u=a;(t.updateQueue=u)&&(t.flags|=4)}},Is=function(e,t,n,o){n!==o&&(t.flags|=4)};var Xs=!1,Ys=!1,Gs="function"===typeof WeakSet?WeakSet:Set,Zs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(o){Sc(e,t,o)}else n.current=null}function el(e,t,n){try{n()}catch(o){Sc(e,t,o)}}var tl=!1;function nl(e,t,n){var o=t.updateQueue;if(null!==(o=null!==o?o.lastEffect:null)){var r=o=o.next;do{if((r.tag&e)===e){var i=r.destroy;r.destroy=void 0,void 0!==i&&el(t,n,i)}r=r.next}while(r!==o)}}function ol(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var o=n.create;n.destroy=o()}n=n.next}while(n!==t)}}function rl(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function il(e){var t=e.alternate;null!==t&&(e.alternate=null,il(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[hr],delete t[pr],delete t[gr],delete t[br],delete t[vr])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function al(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||al(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var o=e.tag;if(5===o||6===o)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=er));else if(4!==o&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function cl(e,t,n){var o=e.tag;if(5===o||6===o)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==o&&null!==(e=e.child))for(cl(e,t,n),e=e.sibling;null!==e;)cl(e,t,n),e=e.sibling}var ul=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(it&&"function"===typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(rt,n)}catch(s){}switch(n.tag){case 5:Ys||Js(n,t);case 6:var o=ul,r=dl;ul=null,fl(e,t,n),dl=r,null!==(ul=o)&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ul.removeChild(n.stateNode));break;case 18:null!==ul&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?cr(e.parentNode,n):1===e.nodeType&&cr(e,n),Ht(e)):cr(ul,n.stateNode));break;case 4:o=ul,r=dl,ul=n.stateNode.containerInfo,dl=!0,fl(e,t,n),ul=o,dl=r;break;case 0:case 11:case 14:case 15:if(!Ys&&(null!==(o=n.updateQueue)&&null!==(o=o.lastEffect))){r=o=o.next;do{var i=r,a=i.destroy;i=i.tag,void 0!==a&&(0!==(2&i)||0!==(4&i))&&el(n,t,a),r=r.next}while(r!==o)}fl(e,t,n);break;case 1:if(!Ys&&(Js(n,t),"function"===typeof(o=n.stateNode).componentWillUnmount))try{o.props=n.memoizedProps,o.state=n.memoizedState,o.componentWillUnmount()}catch(s){Sc(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Ys=(o=Ys)||null!==n.memoizedState,fl(e,t,n),Ys=o):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gs),t.forEach(function(t){var o=_c.bind(null,e,t);n.has(t)||(n.add(t),t.then(o,o))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var o=0;o<n.length;o++){var r=n[o];try{var a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:ul=l.stateNode,dl=!1;break e;case 3:case 4:ul=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===ul)throw Error(i(160));hl(a,s,r),ul=null,dl=!1;var c=r.alternate;null!==c&&(c.return=null),r.return=null}catch(u){Sc(r,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),bl(e),4&o){try{nl(3,e,e.return),ol(3,e)}catch(g){Sc(e,e.return,g)}try{nl(5,e,e.return)}catch(g){Sc(e,e.return,g)}}break;case 1:ml(t,e),bl(e),512&o&&null!==n&&Js(n,n.return);break;case 5:if(ml(t,e),bl(e),512&o&&null!==n&&Js(n,n.return),32&e.flags){var r=e.stateNode;try{fe(r,"")}catch(g){Sc(e,e.return,g)}}if(4&o&&null!=(r=e.stateNode)){var a=e.memoizedProps,s=null!==n?n.memoizedProps:a,l=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===l&&"radio"===a.type&&null!=a.name&&G(r,a),ye(l,s);var u=ye(l,a);for(s=0;s<c.length;s+=2){var d=c[s],f=c[s+1];"style"===d?ge(r,f):"dangerouslySetInnerHTML"===d?de(r,f):"children"===d?fe(r,f):y(r,d,f,u)}switch(l){case"input":Z(r,a);break;case"textarea":ie(r,a);break;case"select":var h=r._wrapperState.wasMultiple;r._wrapperState.wasMultiple=!!a.multiple;var p=a.value;null!=p?ne(r,!!a.multiple,p,!1):h!==!!a.multiple&&(null!=a.defaultValue?ne(r,!!a.multiple,a.defaultValue,!0):ne(r,!!a.multiple,a.multiple?[]:"",!1))}r[pr]=a}catch(g){Sc(e,e.return,g)}}break;case 6:if(ml(t,e),bl(e),4&o){if(null===e.stateNode)throw Error(i(162));r=e.stateNode,a=e.memoizedProps;try{r.nodeValue=a}catch(g){Sc(e,e.return,g)}}break;case 3:if(ml(t,e),bl(e),4&o&&null!==n&&n.memoizedState.isDehydrated)try{Ht(t.containerInfo)}catch(g){Sc(e,e.return,g)}break;case 4:default:ml(t,e),bl(e);break;case 13:ml(t,e),bl(e),8192&(r=e.child).flags&&(a=null!==r.memoizedState,r.stateNode.isHidden=a,!a||null!==r.alternate&&null!==r.alternate.memoizedState||(jl=Ge())),4&o&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Ys=(u=Ys)||d,ml(t,e),Ys=u):ml(t,e),bl(e),8192&o){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Zs=e,d=e.child;null!==d;){for(f=Zs=d;null!==Zs;){switch(p=(h=Zs).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Js(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){o=h,n=h.return;try{t=o,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Sc(o,n,g)}}break;case 5:Js(h,h.return);break;case 22:if(null!==h.memoizedState){$l(f);continue}}null!==p?(p.return=h,Zs=p):$l(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{r=f.stateNode,u?"function"===typeof(a=r.style).setProperty?a.setProperty("display","none","important"):a.display="none":(l=f.stateNode,s=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,l.style.display=me("display",s))}catch(g){Sc(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){Sc(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),bl(e),4&o&&pl(e);case 21:}}function bl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(al(n)){var o=n;break e}n=n.return}throw Error(i(160))}switch(o.tag){case 5:var r=o.stateNode;32&o.flags&&(fe(r,""),o.flags&=-33),cl(e,sl(e),r);break;case 3:case 4:var a=o.stateNode.containerInfo;ll(e,sl(e),a);break;default:throw Error(i(161))}}catch(s){Sc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Zs=e,yl(e,t,n)}function yl(e,t,n){for(var o=0!==(1&e.mode);null!==Zs;){var r=Zs,i=r.child;if(22===r.tag&&o){var a=null!==r.memoizedState||Xs;if(!a){var s=r.alternate,l=null!==s&&null!==s.memoizedState||Ys;s=Xs;var c=Ys;if(Xs=a,(Ys=l)&&!c)for(Zs=r;null!==Zs;)l=(a=Zs).child,22===a.tag&&null!==a.memoizedState?xl(r):null!==l?(l.return=a,Zs=l):xl(r);for(;null!==i;)Zs=i,yl(i,t,n),i=i.sibling;Zs=r,Xs=s,Ys=c}wl(e)}else 0!==(8772&r.subtreeFlags)&&null!==i?(i.return=r,Zs=i):wl(e)}}function wl(e){for(;null!==Zs;){var t=Zs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ys||ol(5,t);break;case 1:var o=t.stateNode;if(4&t.flags&&!Ys)if(null===n)o.componentDidMount();else{var r=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);o.componentDidUpdate(r,n.memoizedState,o.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&Wi(t,a,o);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wi(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ht(f)}}}break;default:throw Error(i(163))}Ys||512&t.flags&&rl(t)}catch(h){Sc(t,t.return,h)}}if(t===e){Zs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zs=n;break}Zs=t.return}}function $l(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function xl(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ol(4,t)}catch(l){Sc(t,n,l)}break;case 1:var o=t.stateNode;if("function"===typeof o.componentDidMount){var r=t.return;try{o.componentDidMount()}catch(l){Sc(t,r,l)}}var i=t.return;try{rl(t)}catch(l){Sc(t,i,l)}break;case 5:var a=t.return;try{rl(t)}catch(l){Sc(t,a,l)}}}catch(l){Sc(t,t.return,l)}if(t===e){Zs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Zs=s;break}Zs=t.return}}var kl,Sl=Math.ceil,El=w.ReactCurrentDispatcher,Cl=w.ReactCurrentOwner,Tl=w.ReactCurrentBatchConfig,_l=0,Pl=null,Ll=null,Rl=0,Nl=0,Ol=Er(0),Il=0,zl=null,Dl=0,Al=0,Ml=0,Bl=null,Vl=null,jl=0,Hl=1/0,Fl=null,Wl=!1,Ul=null,ql=null,Ql=!1,Kl=null,Xl=0,Yl=0,Gl=null,Zl=-1,Jl=0;function ec(){return 0!==(6&_l)?Ge():-1!==Zl?Zl:Zl=Ge()}function tc(e){return 0===(1&e.mode)?1:0!==(2&_l)&&0!==Rl?Rl&-Rl:null!==gi.transition?(0===Jl&&(Jl=mt()),Jl):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Yt(e.type)}function nc(e,t,n,o){if(50<Yl)throw Yl=0,Gl=null,Error(i(185));bt(e,n,o),0!==(2&_l)&&e===Pl||(e===Pl&&(0===(2&_l)&&(Al|=n),4===Il&&sc(e,Rl)),oc(e,o),1===n&&0===_l&&0===(1&t.mode)&&(Hl=Ge()+500,Vr&&Fr()))}function oc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,o=e.pingedLanes,r=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-at(i),s=1<<a,l=r[a];-1===l?0!==(s&n)&&0===(s&o)||(r[a]=ht(s,t)):l<=t&&(e.expiredLanes|=s),i&=~s}}(e,t);var o=ft(e,e===Pl?Rl:0);if(0===o)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=o&-o,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Vr=!0,Hr(e)}(lc.bind(null,e)):Hr(lc.bind(null,e)),sr(function(){0===(6&_l)&&Fr()}),n=null;else{switch(wt(o)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=ot}n=Pc(n,rc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function rc(e,t){if(Zl=-1,Jl=0,0!==(6&_l))throw Error(i(327));var n=e.callbackNode;if(xc()&&e.callbackNode!==n)return null;var o=ft(e,e===Pl?Rl:0);if(0===o)return null;if(0!==(30&o)||0!==(o&e.expiredLanes)||t)t=gc(e,o);else{t=o;var r=_l;_l|=2;var a=pc();for(Pl===e&&Rl===t||(Fl=null,Hl=Ge()+500,fc(e,t));;)try{vc();break}catch(l){hc(e,l)}Ti(),El.current=a,_l=r,null!==Ll?t=0:(Pl=null,Rl=0,t=Il)}if(0!==t){if(2===t&&(0!==(r=pt(e))&&(o=r,t=ic(e,r))),1===t)throw n=zl,fc(e,0),sc(e,o),oc(e,Ge()),n;if(6===t)sc(e,o);else{if(r=e.current.alternate,0===(30&o)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var o=0;o<n.length;o++){var r=n[o],i=r.getSnapshot;r=r.value;try{if(!so(i(),r))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(r)&&(2===(t=gc(e,o))&&(0!==(a=pt(e))&&(o=a,t=ic(e,a))),1===t))throw n=zl,fc(e,0),sc(e,o),oc(e,Ge()),n;switch(e.finishedWork=r,e.finishedLanes=o,t){case 0:case 1:throw Error(i(345));case 2:case 5:$c(e,Vl,Fl);break;case 3:if(sc(e,o),(130023424&o)===o&&10<(t=jl+500-Ge())){if(0!==ft(e,0))break;if(((r=e.suspendedLanes)&o)!==o){ec(),e.pingedLanes|=e.suspendedLanes&r;break}e.timeoutHandle=rr($c.bind(null,e,Vl,Fl),t);break}$c(e,Vl,Fl);break;case 4:if(sc(e,o),(4194240&o)===o)break;for(t=e.eventTimes,r=-1;0<o;){var s=31-at(o);a=1<<s,(s=t[s])>r&&(r=s),o&=~a}if(o=r,10<(o=(120>(o=Ge()-o)?120:480>o?480:1080>o?1080:1920>o?1920:3e3>o?3e3:4320>o?4320:1960*Sl(o/1960))-o)){e.timeoutHandle=rr($c.bind(null,e,Vl,Fl),o);break}$c(e,Vl,Fl);break;default:throw Error(i(329))}}}return oc(e,Ge()),e.callbackNode===n?rc.bind(null,e):null}function ic(e,t){var n=Bl;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Vl,Vl=n,null!==t&&ac(t)),e}function ac(e){null===Vl?Vl=e:Vl.push.apply(Vl,e)}function sc(e,t){for(t&=~Ml,t&=~Al,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-at(t),o=1<<n;e[n]=-1,t&=~o}}function lc(e){if(0!==(6&_l))throw Error(i(327));xc();var t=ft(e,0);if(0===(1&t))return oc(e,Ge()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var o=pt(e);0!==o&&(t=o,n=ic(e,o))}if(1===n)throw n=zl,fc(e,0),sc(e,t),oc(e,Ge()),n;if(6===n)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$c(e,Vl,Fl),oc(e,Ge()),null}function cc(e,t){var n=_l;_l|=1;try{return e(t)}finally{0===(_l=n)&&(Hl=Ge()+500,Vr&&Fr())}}function uc(e){null!==Kl&&0===Kl.tag&&0===(6&_l)&&xc();var t=_l;_l|=1;var n=Tl.transition,o=yt;try{if(Tl.transition=null,yt=1,e)return e()}finally{yt=o,Tl.transition=n,0===(6&(_l=t))&&Fr()}}function dc(){Nl=Ol.current,Cr(Ol)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ir(n)),null!==Ll)for(n=Ll.return;null!==n;){var o=n;switch(ni(o),o.tag){case 1:null!==(o=o.type.childContextTypes)&&void 0!==o&&Ir();break;case 3:Gi(),Cr(Lr),Cr(Pr),oa();break;case 5:Ji(o);break;case 4:Gi();break;case 13:case 19:Cr(ea);break;case 10:_i(o.type._context);break;case 22:case 23:dc()}n=n.return}if(Pl=e,Ll=e=Oc(e.current,null),Rl=Nl=t,Il=0,zl=null,Ml=Al=Dl=0,Vl=Bl=null,null!==Ni){for(t=0;t<Ni.length;t++)if(null!==(o=(n=Ni[t]).interleaved)){n.interleaved=null;var r=o.next,i=n.pending;if(null!==i){var a=i.next;i.next=r,o.next=a}n.pending=o}Ni=null}return e}function hc(e,t){for(;;){var n=Ll;try{if(Ti(),ra.current=Za,ua){for(var o=sa.memoizedState;null!==o;){var r=o.queue;null!==r&&(r.pending=null),o=o.next}ua=!1}if(aa=0,ca=la=sa=null,da=!1,fa=0,Cl.current=null,null===n||null===n.return){Il=1,zl=t,Ll=null;break}e:{var a=e,s=n.return,l=n,c=t;if(t=Rl,l.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=gs(s);if(null!==p){p.flags&=-257,bs(p,s,l,0,t),1&p.mode&&ms(a,u,t),c=u;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(c),t.updateQueue=g}else m.add(c);break e}if(0===(1&t)){ms(a,u,t),mc();break e}c=Error(i(426))}else if(ii&&1&l.mode){var b=gs(s);if(null!==b){0===(65536&b.flags)&&(b.flags|=256),bs(b,s,l,0,t),mi(cs(c,l));break e}}a=c=cs(c,l),4!==Il&&(Il=2),null===Bl?Bl=[a]:Bl.push(a),a=s;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t,Hi(a,hs(0,c,t));break e;case 1:l=c;var v=a.type,y=a.stateNode;if(0===(128&a.flags)&&("function"===typeof v.getDerivedStateFromError||null!==y&&"function"===typeof y.componentDidCatch&&(null===ql||!ql.has(y)))){a.flags|=65536,t&=-t,a.lanes|=t,Hi(a,ps(a,l,t));break e}}a=a.return}while(null!==a)}wc(n)}catch(w){t=w,Ll===n&&null!==n&&(Ll=n=n.return);continue}break}}function pc(){var e=El.current;return El.current=Za,null===e?Za:e}function mc(){0!==Il&&3!==Il&&2!==Il||(Il=4),null===Pl||0===(268435455&Dl)&&0===(268435455&Al)||sc(Pl,Rl)}function gc(e,t){var n=_l;_l|=2;var o=pc();for(Pl===e&&Rl===t||(Fl=null,fc(e,t));;)try{bc();break}catch(r){hc(e,r)}if(Ti(),_l=n,El.current=o,null!==Ll)throw Error(i(261));return Pl=null,Rl=0,Il}function bc(){for(;null!==Ll;)yc(Ll)}function vc(){for(;null!==Ll&&!Xe();)yc(Ll)}function yc(e){var t=kl(e.alternate,e,Nl);e.memoizedProps=e.pendingProps,null===t?wc(e):Ll=t,Cl.current=null}function wc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qs(n,t,Nl)))return void(Ll=n)}else{if(null!==(n=Ks(n,t)))return n.flags&=32767,void(Ll=n);if(null===e)return Il=6,void(Ll=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ll=t);Ll=t=e}while(null!==t);0===Il&&(Il=5)}function $c(e,t,n){var o=yt,r=Tl.transition;try{Tl.transition=null,yt=1,function(e,t,n,o){do{xc()}while(null!==Kl);if(0!==(6&_l))throw Error(i(327));n=e.finishedWork;var r=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var o=e.eventTimes;for(e=e.expirationTimes;0<n;){var r=31-at(n),i=1<<r;t[r]=0,o[r]=-1,e[r]=-1,n&=~i}}(e,a),e===Pl&&(Ll=Pl=null,Rl=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ql||(Ql=!0,Pc(tt,function(){return xc(),null})),a=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||a){a=Tl.transition,Tl.transition=null;var s=yt;yt=1;var l=_l;_l|=4,Cl.current=null,function(e,t){if(tr=Wt,po(e=ho())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var o=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(o&&0!==o.rangeCount){n=o.anchorNode;var r=o.anchorOffset,a=o.focusNode;o=o.focusOffset;try{n.nodeType,a.nodeType}catch($){n=null;break e}var s=0,l=-1,c=-1,u=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==r&&3!==f.nodeType||(l=s+r),f!==a||0!==o&&3!==f.nodeType||(c=s+o),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++u===r&&(l=s),h===a&&++d===o&&(c=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(nr={focusedElem:e,selectionRange:n},Wt=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,b=m.memoizedState,v=t.stateNode,y=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:ns(t.type,g),b);v.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(i(163))}}catch($){Sc(t,t.return,$)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}m=tl,tl=!1}(e,n),gl(n,e),mo(nr),Wt=!!tr,nr=tr=null,e.current=n,vl(n,e,r),Ye(),_l=l,yt=s,Tl.transition=a}else e.current=n;if(Ql&&(Ql=!1,Kl=e,Xl=r),a=e.pendingLanes,0===a&&(ql=null),function(e){if(it&&"function"===typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(rt,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),oc(e,Ge()),null!==t)for(o=e.onRecoverableError,n=0;n<t.length;n++)r=t[n],o(r.value,{componentStack:r.stack,digest:r.digest});if(Wl)throw Wl=!1,e=Ul,Ul=null,e;0!==(1&Xl)&&0!==e.tag&&xc(),a=e.pendingLanes,0!==(1&a)?e===Gl?Yl++:(Yl=0,Gl=e):Yl=0,Fr()}(e,t,n,o)}finally{Tl.transition=r,yt=o}return null}function xc(){if(null!==Kl){var e=wt(Xl),t=Tl.transition,n=yt;try{if(Tl.transition=null,yt=16>e?16:e,null===Kl)var o=!1;else{if(e=Kl,Kl=null,Xl=0,0!==(6&_l))throw Error(i(331));var r=_l;for(_l|=4,Zs=e.current;null!==Zs;){var a=Zs,s=a.child;if(0!==(16&Zs.flags)){var l=a.deletions;if(null!==l){for(var c=0;c<l.length;c++){var u=l[c];for(Zs=u;null!==Zs;){var d=Zs;switch(d.tag){case 0:case 11:case 15:nl(8,d,a)}var f=d.child;if(null!==f)f.return=d,Zs=f;else for(;null!==Zs;){var h=(d=Zs).sibling,p=d.return;if(il(d),d===u){Zs=null;break}if(null!==h){h.return=p,Zs=h;break}Zs=p}}}var m=a.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var b=g.sibling;g.sibling=null,g=b}while(null!==g)}}Zs=a}}if(0!==(2064&a.subtreeFlags)&&null!==s)s.return=a,Zs=s;else e:for(;null!==Zs;){if(0!==(2048&(a=Zs).flags))switch(a.tag){case 0:case 11:case 15:nl(9,a,a.return)}var v=a.sibling;if(null!==v){v.return=a.return,Zs=v;break e}Zs=a.return}}var y=e.current;for(Zs=y;null!==Zs;){var w=(s=Zs).child;if(0!==(2064&s.subtreeFlags)&&null!==w)w.return=s,Zs=w;else e:for(s=y;null!==Zs;){if(0!==(2048&(l=Zs).flags))try{switch(l.tag){case 0:case 11:case 15:ol(9,l)}}catch(x){Sc(l,l.return,x)}if(l===s){Zs=null;break e}var $=l.sibling;if(null!==$){$.return=l.return,Zs=$;break e}Zs=l.return}}if(_l=r,Fr(),it&&"function"===typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(rt,e)}catch(x){}o=!0}return o}finally{yt=n,Tl.transition=t}}return!1}function kc(e,t,n){e=Vi(e,t=hs(0,t=cs(n,t),1),1),t=ec(),null!==e&&(bt(e,1,t),oc(e,t))}function Sc(e,t,n){if(3===e.tag)kc(e,e,n);else for(;null!==t;){if(3===t.tag){kc(t,e,n);break}if(1===t.tag){var o=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof o.componentDidCatch&&(null===ql||!ql.has(o))){t=Vi(t,e=ps(t,e=cs(n,e),1),1),e=ec(),null!==t&&(bt(t,1,e),oc(t,e));break}}t=t.return}}function Ec(e,t,n){var o=e.pingCache;null!==o&&o.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Pl===e&&(Rl&n)===n&&(4===Il||3===Il&&(130023424&Rl)===Rl&&500>Ge()-jl?fc(e,0):Ml|=n),oc(e,t)}function Cc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=zi(e,t))&&(bt(e,t,n),oc(e,n))}function Tc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Cc(e,n)}function _c(e,t){var n=0;switch(e.tag){case 13:var o=e.stateNode,r=e.memoizedState;null!==r&&(n=r.retryLane);break;case 19:o=e.stateNode;break;default:throw Error(i(314))}null!==o&&o.delete(t),Cc(e,n)}function Pc(e,t){return Qe(e,t)}function Lc(e,t,n,o){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Rc(e,t,n,o){return new Lc(e,t,n,o)}function Nc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Oc(e,t){var n=e.alternate;return null===n?((n=Rc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ic(e,t,n,o,r,a){var s=2;if(o=e,"function"===typeof e)Nc(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case k:return zc(n.children,r,a,t);case S:s=8,r|=8;break;case E:return(e=Rc(12,n,t,2|r)).elementType=E,e.lanes=a,e;case P:return(e=Rc(13,n,t,r)).elementType=P,e.lanes=a,e;case L:return(e=Rc(19,n,t,r)).elementType=L,e.lanes=a,e;case O:return Dc(n,r,a,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case C:s=10;break e;case T:s=9;break e;case _:s=11;break e;case R:s=14;break e;case N:s=16,o=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Rc(s,n,t,r)).elementType=e,t.type=o,t.lanes=a,t}function zc(e,t,n,o){return(e=Rc(7,e,o,t)).lanes=n,e}function Dc(e,t,n,o){return(e=Rc(22,e,o,t)).elementType=O,e.lanes=n,e.stateNode={isHidden:!1},e}function Ac(e,t,n){return(e=Rc(6,e,null,t)).lanes=n,e}function Mc(e,t,n){return(t=Rc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bc(e,t,n,o,r){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=o,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null}function Vc(e,t,n,o,r,i,a,s,l){return e=new Bc(e,t,n,s,l),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Rc(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:o,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ai(i),e}function jc(e){if(!e)return _r;e:{if(He(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Or(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var n=e.type;if(Or(n))return Dr(e,n,t)}return t}function Hc(e,t,n,o,r,i,a,s,l){return(e=Vc(n,o,!0,e,0,i,0,s,l)).context=jc(null),n=e.current,(i=Bi(o=ec(),r=tc(n))).callback=void 0!==t&&null!==t?t:null,Vi(n,i,r),e.current.lanes=r,bt(e,r,o),oc(e,o),e}function Fc(e,t,n,o){var r=t.current,i=ec(),a=tc(r);return n=jc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Bi(i,a)).payload={element:e},null!==(o=void 0===o?null:o)&&(t.callback=o),null!==(e=Vi(r,t,a))&&(nc(e,r,a,i),ji(e,r,a)),a}function Wc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Uc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qc(e,t){Uc(e,t),(e=e.alternate)&&Uc(e,t)}kl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Lr.current)ys=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return ys=!1,function(e,t,n){switch(t.tag){case 3:Ps(t),pi();break;case 5:Zi(t);break;case 1:Or(t.type)&&Ar(t);break;case 4:Yi(t,t.stateNode.containerInfo);break;case 10:var o=t.type._context,r=t.memoizedProps.value;Tr(ki,o._currentValue),o._currentValue=r;break;case 13:if(null!==(o=t.memoizedState))return null!==o.dehydrated?(Tr(ea,1&ea.current),t.flags|=128,null):0!==(n&t.child.childLanes)?As(e,t,n):(Tr(ea,1&ea.current),null!==(e=Ws(e,t,n))?e.sibling:null);Tr(ea,1&ea.current);break;case 19:if(o=0!==(n&t.childLanes),0!==(128&e.flags)){if(o)return Hs(e,t,n);t.flags|=128}if(null!==(r=t.memoizedState)&&(r.rendering=null,r.tail=null,r.lastEffect=null),Tr(ea,ea.current),o)break;return null;case 22:case 23:return t.lanes=0,Ss(e,t,n)}return Ws(e,t,n)}(e,t,n);ys=0!==(131072&e.flags)}else ys=!1,ii&&0!==(1048576&t.flags)&&ei(t,Qr,t.index);switch(t.lanes=0,t.tag){case 2:var o=t.type;Fs(e,t),e=t.pendingProps;var r=Nr(t,Pr.current);Li(t,n),r=ga(null,t,o,e,r,n);var a=ba();return t.flags|=1,"object"===typeof r&&null!==r&&"function"===typeof r.render&&void 0===r.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Or(o)?(a=!0,Ar(t)):a=!1,t.memoizedState=null!==r.state&&void 0!==r.state?r.state:null,Ai(t),r.updater=rs,t.stateNode=r,r._reactInternals=t,ls(t,o,e,n),t=_s(null,t,o,!0,a,n)):(t.tag=0,ii&&a&&ti(t),ws(null,t,r,n),t=t.child),t;case 16:o=t.elementType;e:{switch(Fs(e,t),e=t.pendingProps,o=(r=o._init)(o._payload),t.type=o,r=t.tag=function(e){if("function"===typeof e)return Nc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===_)return 11;if(e===R)return 14}return 2}(o),e=ns(o,e),r){case 0:t=Cs(null,t,o,e,n);break e;case 1:t=Ts(null,t,o,e,n);break e;case 11:t=$s(null,t,o,e,n);break e;case 14:t=xs(null,t,o,ns(o.type,e),n);break e}throw Error(i(306,o,""))}return t;case 0:return o=t.type,r=t.pendingProps,Cs(e,t,o,r=t.elementType===o?r:ns(o,r),n);case 1:return o=t.type,r=t.pendingProps,Ts(e,t,o,r=t.elementType===o?r:ns(o,r),n);case 3:e:{if(Ps(t),null===e)throw Error(i(387));o=t.pendingProps,r=(a=t.memoizedState).element,Mi(e,t),Fi(t,o,null,n);var s=t.memoizedState;if(o=s.element,a.isDehydrated){if(a={element:o,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Ls(e,t,o,n,r=cs(Error(i(423)),t));break e}if(o!==r){t=Ls(e,t,o,n,r=cs(Error(i(424)),t));break e}for(ri=ur(t.stateNode.containerInfo.firstChild),oi=t,ii=!0,ai=null,n=xi(t,null,o,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pi(),o===r){t=Ws(e,t,n);break e}ws(e,t,o,n)}t=t.child}return t;case 5:return Zi(t),null===e&&ui(t),o=t.type,r=t.pendingProps,a=null!==e?e.memoizedProps:null,s=r.children,or(o,r)?s=null:null!==a&&or(o,a)&&(t.flags|=32),Es(e,t),ws(e,t,s,n),t.child;case 6:return null===e&&ui(t),null;case 13:return As(e,t,n);case 4:return Yi(t,t.stateNode.containerInfo),o=t.pendingProps,null===e?t.child=$i(t,null,o,n):ws(e,t,o,n),t.child;case 11:return o=t.type,r=t.pendingProps,$s(e,t,o,r=t.elementType===o?r:ns(o,r),n);case 7:return ws(e,t,t.pendingProps,n),t.child;case 8:case 12:return ws(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(o=t.type._context,r=t.pendingProps,a=t.memoizedProps,s=r.value,Tr(ki,o._currentValue),o._currentValue=s,null!==a)if(so(a.value,s)){if(a.children===r.children&&!Lr.current){t=Ws(e,t,n);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var l=a.dependencies;if(null!==l){s=a.child;for(var c=l.firstContext;null!==c;){if(c.context===o){if(1===a.tag){(c=Bi(-1,n&-n)).tag=2;var u=a.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}a.lanes|=n,null!==(c=a.alternate)&&(c.lanes|=n),Pi(a.return,n,t),l.lanes|=n;break}c=c.next}}else if(10===a.tag)s=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(s=a.return))throw Error(i(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Pi(s,n,t),s=a.sibling}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}ws(e,t,r.children,n),t=t.child}return t;case 9:return r=t.type,o=t.pendingProps.children,Li(t,n),o=o(r=Ri(r)),t.flags|=1,ws(e,t,o,n),t.child;case 14:return r=ns(o=t.type,t.pendingProps),xs(e,t,o,r=ns(o.type,r),n);case 15:return ks(e,t,t.type,t.pendingProps,n);case 17:return o=t.type,r=t.pendingProps,r=t.elementType===o?r:ns(o,r),Fs(e,t),t.tag=1,Or(o)?(e=!0,Ar(t)):e=!1,Li(t,n),as(t,o,r),ls(t,o,r,n),_s(null,t,o,!0,e,n);case 19:return Hs(e,t,n);case 22:return Ss(e,t,n)}throw Error(i(156,t.tag))};var Qc="function"===typeof reportError?reportError:function(e){console.error(e)};function Kc(e){this._internalRoot=e}function Xc(e){this._internalRoot=e}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zc(){}function Jc(e,t,n,o,r){var i=n._reactRootContainer;if(i){var a=i;if("function"===typeof r){var s=r;r=function(){var e=Wc(a);s.call(e)}}Fc(t,a,e,r)}else a=function(e,t,n,o,r){if(r){if("function"===typeof o){var i=o;o=function(){var e=Wc(a);i.call(e)}}var a=Hc(t,o,e,0,null,!1,0,"",Zc);return e._reactRootContainer=a,e[mr]=a.current,Fo(8===e.nodeType?e.parentNode:e),uc(),a}for(;r=e.lastChild;)e.removeChild(r);if("function"===typeof o){var s=o;o=function(){var e=Wc(l);s.call(e)}}var l=Vc(e,0,!1,null,0,!1,0,"",Zc);return e._reactRootContainer=l,e[mr]=l.current,Fo(8===e.nodeType?e.parentNode:e),uc(function(){Fc(t,l,n,o)}),l}(n,t,e,r,o);return Wc(a)}Xc.prototype.render=Kc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Fc(e,t,null,null)},Xc.prototype.unmount=Kc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc(function(){Fc(null,e,null,null)}),t[mr]=null}},Xc.prototype.unstable_scheduleHydration=function(e){if(e){var t=St();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ot.length&&0!==t&&t<Ot[n].priority;n++);Ot.splice(n,0,e),0===n&&At(e)}},$t=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),oc(t,Ge()),0===(6&_l)&&(Hl=Ge()+500,Fr()))}break;case 13:uc(function(){var t=zi(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}}),qc(e,1)}},xt=function(e){if(13===e.tag){var t=zi(e,134217728);if(null!==t)nc(t,e,134217728,ec());qc(e,134217728)}},kt=function(e){if(13===e.tag){var t=tc(e),n=zi(e,t);if(null!==n)nc(n,e,t,ec());qc(e,t)}},St=function(){return yt},Et=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},xe=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var o=n[t];if(o!==e&&o.form===e.form){var r=xr(o);if(!r)throw Error(i(90));Q(o),Z(o,r)}}}break;case"textarea":ie(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},_e=cc,Pe=uc;var eu={usingClientEntryPoint:!1,Events:[wr,$r,xr,Ce,Te,cc]},tu={findFiberByHostInstance:yr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ue(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ou=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ou.isDisabled&&ou.supportsFiber)try{rt=ou.inject(nu),it=ou}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Yc(t))throw Error(i(200));return function(e,t,n){var o=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:x,key:null==o?null:""+o,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Yc(e))throw Error(i(299));var n=!1,o="",r=Qc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(o=t.identifierPrefix),void 0!==t.onRecoverableError&&(r=t.onRecoverableError)),t=Vc(e,1,!1,null,0,n,0,o,r),e[mr]=t.current,Fo(8===e.nodeType?e.parentNode:e),new Kc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=Ue(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Gc(t))throw Error(i(200));return Jc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Yc(e))throw Error(i(405));var o=null!=n&&n.hydratedSources||null,r=!1,a="",s=Qc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Hc(t,null,e,1,null!=n?n:null,r,0,a,s),e[mr]=t.current,Fo(e),o)for(e=0;e<o.length;e++)r=(r=(n=o[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,r]:t.mutableSourceEagerHydrationData.push(n,r);return new Xc(t)},t.render=function(e,t,n){if(!Gc(t))throw Error(i(200));return Jc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Gc(e))throw Error(i(40));return!!e._reactRootContainer&&(uc(function(){Jc(null,null,e,!1,function(){e._reactRootContainer=null,e[mr]=null})}),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,o){if(!Gc(n))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return Jc(e,t,n,!1,o)},t.version="18.3.1-next-f1338f8080-20240426"},763:(e,t,n)=>{"use strict";e.exports=n(983)},793:(e,t,n)=>{"use strict";n.d(t,{K:()=>a,a:()=>i});var o=n(286);var r,i;!function(e){e.Unimplemented="UNIMPLEMENTED",e.Unavailable="UNAVAILABLE"}(r||(r={})),function(e){e.Body="body",e.Ionic="ionic",e.Native="native",e.None="none"}(i||(i={}));const a={getEngine(){const e=(()=>{if(void 0!==o.w)return o.w.Capacitor})();if(null===e||void 0===e?void 0:e.isPluginAvailable("Keyboard"))return e.Plugins.Keyboard},getResizeMode(){const e=this.getEngine();return(null===e||void 0===e?void 0:e.getResizeMode)?e.getResizeMode().catch(e=>{if(e.code!==r.Unimplemented)throw e}):Promise.resolve(void 0)}}},853:(e,t,n)=>{"use strict";e.exports=n(234)},903:(e,t,n)=>{"use strict";n.d(t,{b:()=>i,c:()=>a,d:()=>s,g:()=>S,l:()=>w,s:()=>x,t:()=>l});var o=n(441),r=n(384);const i="ionViewWillLeave",a="ionViewDidLeave",s="ionViewWillUnload",l=e=>new Promise((t,n)=>{(0,o.bN)(()=>{c(e),u(e).then(n=>{n.animation&&n.animation.destroy(),d(e),t(n)},t=>{d(e),n(t)})})}),c=e=>{const t=e.enteringEl,n=e.leavingEl;k(t,n,e.direction),e.showGoBack?t.classList.add("can-go-back"):t.classList.remove("can-go-back"),x(t,!1),t.style.setProperty("pointer-events","none"),n&&(x(n,!1),n.style.setProperty("pointer-events","none"))},u=async e=>{const t=await f(e);return t&&o.L2.isBrowser?h(t,e):p(e)},d=e=>{const t=e.enteringEl,n=e.leavingEl;t.classList.remove("ion-page-invisible"),t.style.removeProperty("pointer-events"),void 0!==n&&(n.classList.remove("ion-page-invisible"),n.style.removeProperty("pointer-events"))},f=async e=>{if(!e.leavingEl||!e.animated||0===e.duration)return;if(e.animationBuilder)return e.animationBuilder;return"ios"===e.mode?(await n.e(923).then(n.bind(n,923))).iosTransitionAnimation:(await n.e(627).then(n.bind(n,627))).mdTransitionAnimation},h=async(e,t)=>{await m(t,!0);const n=e(t.baseEl,t);v(t.enteringEl,t.leavingEl);const o=await b(n,t);return t.progressCallback&&t.progressCallback(void 0),o&&y(t.enteringEl,t.leavingEl),{hasCompleted:o,animation:n}},p=async e=>{const t=e.enteringEl,n=e.leavingEl;return await m(e,!1),v(t,n),y(t,n),{hasCompleted:!0}},m=async(e,t)=>{(void 0!==e.deepWait?e.deepWait:t)&&await Promise.all([$(e.enteringEl),$(e.leavingEl)]),await g(e.viewIsReady,e.enteringEl)},g=async(e,t)=>{e&&await e(t)},b=(e,t)=>{const n=t.progressCallback,o=new Promise(t=>{e.onFinish(e=>t(1===e))});return n?(e.progressStart(!0),n(e)):e.play(),o},v=(e,t)=>{w(t,i),w(e,"ionViewWillEnter")},y=(e,t)=>{w(e,"ionViewDidEnter"),w(t,a)},w=(e,t)=>{if(e){const n=new CustomEvent(t,{bubbles:!1,cancelable:!1});e.dispatchEvent(n)}},$=async e=>{const t=e;if(t){if(null!=t.componentOnReady){if(null!=await t.componentOnReady())return}else if(null!=t.__registerHost){const e=new Promise(e=>(0,r.r)(e));return void await e}await Promise.all(Array.from(t.children).map($))}},x=(e,t)=>{t?(e.setAttribute("aria-hidden","true"),e.classList.add("ion-page-hidden")):(e.hidden=!1,e.removeAttribute("aria-hidden"),e.classList.remove("ion-page-hidden"))},k=(e,t,n)=>{void 0!==e&&(e.style.zIndex="back"===n?"99":"101"),void 0!==t&&(t.style.zIndex="100")},S=e=>{if(e.classList.contains("ion-page"))return e;const t=e.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs");return t||e}},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},983:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,o=n?Symbol.for("react.element"):60103,r=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,p=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,b=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,y=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function $(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case o:switch(e=e.type){case u:case d:case i:case s:case a:case h:return e;default:switch(e=e&&e.$$typeof){case c:case f:case g:case m:case l:return e;default:return t}}case r:return t}}}function x(e){return $(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=l,t.Element=o,t.ForwardRef=f,t.Fragment=i,t.Lazy=g,t.Memo=m,t.Portal=r,t.Profiler=s,t.StrictMode=a,t.Suspense=h,t.isAsyncMode=function(e){return x(e)||$(e)===u},t.isConcurrentMode=x,t.isContextConsumer=function(e){return $(e)===c},t.isContextProvider=function(e){return $(e)===l},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===o},t.isForwardRef=function(e){return $(e)===f},t.isFragment=function(e){return $(e)===i},t.isLazy=function(e){return $(e)===g},t.isMemo=function(e){return $(e)===m},t.isPortal=function(e){return $(e)===r},t.isProfiler=function(e){return $(e)===s},t.isStrictMode=function(e){return $(e)===a},t.isSuspense=function(e){return $(e)===h},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===i||e===d||e===s||e===a||e===h||e===p||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===l||e.$$typeof===c||e.$$typeof===f||e.$$typeof===v||e.$$typeof===y||e.$$typeof===w||e.$$typeof===b)},t.typeOf=$}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,n),i.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,o)=>(n.f[o](e,t),t),[])),n.u=e=>"static/js/"+e+"."+{82:"aac29f79",247:"729ae9df",422:"0eda4b26",627:"3dfd551a",768:"bef5843c",806:"6211f4d0",834:"9037cda7",923:"eef5654c",938:"0fe6d811"}[e]+".chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="alphabet-game:";n.l=(o,r,i,a)=>{if(e[o])e[o].push(r);else{var s,l;if(void 0!==i)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==o||d.getAttribute("data-webpack")==t+i){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+i),s.src=o),e[o]=[r];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(h);var r=e[o];if(delete e[o],s.parentNode&&s.parentNode.removeChild(s),r&&r.forEach(e=>e(n)),t)return t(n)},h=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,o)=>{var r=n.o(e,t)?e[t]:void 0;if(0!==r)if(r)o.push(r[2]);else{var i=new Promise((n,o)=>r=e[t]=[n,o]);o.push(r[2]=i);var a=n.p+n.u(t),s=new Error;n.l(a,o=>{if(n.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var i=o&&("load"===o.type?"missing":o.type),a=o&&o.target&&o.target.src;s.message="Loading chunk "+t+" failed.\n("+i+": "+a+")",s.name="ChunkLoadError",s.type=i,s.request=a,r[1](s)}},"chunk-"+t,t)}};var t=(t,o)=>{var r,i,[a,s,l]=o,c=0;if(a.some(t=>0!==e[t])){for(r in s)n.o(s,r)&&(n.m[r]=s[r]);if(l)l(n)}for(t&&t(o);c<a.length;c++)i=a[c],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0},o=globalThis.webpackChunkalphabet_game=globalThis.webpackChunkalphabet_game||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})(),(()=>{"use strict";var e=n(43),t=n(391);function o(e,t){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},o(e,t)}function r(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,o(e,t)}var i=n(173),a=n.n(i);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},s.apply(null,arguments)}function l(e){return"/"===e.charAt(0)}function c(e,t){for(var n=t,o=n+1,r=e.length;o<r;n+=1,o+=1)e[n]=e[o];e.pop()}const u=function(e,t){void 0===t&&(t="");var n,o=e&&e.split("/")||[],r=t&&t.split("/")||[],i=e&&l(e),a=t&&l(t),s=i||a;if(e&&l(e)?r=o:o.length&&(r.pop(),r=r.concat(o)),!r.length)return"/";if(r.length){var u=r[r.length-1];n="."===u||".."===u||""===u}else n=!1;for(var d=0,f=r.length;f>=0;f--){var h=r[f];"."===h?c(r,f):".."===h?(c(r,f),d++):d&&(c(r,f),d--)}if(!s)for(;d--;d)r.unshift("..");!s||""===r[0]||r[0]&&l(r[0])||r.unshift("");var p=r.join("/");return n&&"/"!==p.substr(-1)&&(p+="/"),p};function d(e){return e.valueOf?e.valueOf():Object.prototype.valueOf.call(e)}const f=function e(t,n){if(t===n)return!0;if(null==t||null==n)return!1;if(Array.isArray(t))return Array.isArray(n)&&t.length===n.length&&t.every(function(t,o){return e(t,n[o])});if("object"===typeof t||"object"===typeof n){var o=d(t),r=d(n);return o!==t||r!==n?e(o,r):Object.keys(Object.assign({},t,n)).every(function(o){return e(t[o],n[o])})}return!1};var h="Invariant failed";function p(e,t){if(!e)throw new Error(h)}function m(e){return"/"===e.charAt(0)?e:"/"+e}function g(e,t){return function(e,t){return 0===e.toLowerCase().indexOf(t.toLowerCase())&&-1!=="/?#".indexOf(e.charAt(t.length))}(e,t)?e.substr(t.length):e}function b(e){return"/"===e.charAt(e.length-1)?e.slice(0,-1):e}function v(e){var t=e.pathname,n=e.search,o=e.hash,r=t||"/";return n&&"?"!==n&&(r+="?"===n.charAt(0)?n:"?"+n),o&&"#"!==o&&(r+="#"===o.charAt(0)?o:"#"+o),r}function y(e,t,n,o){var r;"string"===typeof e?(r=function(e){var t=e||"/",n="",o="",r=t.indexOf("#");-1!==r&&(o=t.substr(r),t=t.substr(0,r));var i=t.indexOf("?");return-1!==i&&(n=t.substr(i),t=t.substr(0,i)),{pathname:t,search:"?"===n?"":n,hash:"#"===o?"":o}}(e),r.state=t):(void 0===(r=s({},e)).pathname&&(r.pathname=""),r.search?"?"!==r.search.charAt(0)&&(r.search="?"+r.search):r.search="",r.hash?"#"!==r.hash.charAt(0)&&(r.hash="#"+r.hash):r.hash="",void 0!==t&&void 0===r.state&&(r.state=t));try{r.pathname=decodeURI(r.pathname)}catch(i){throw i instanceof URIError?new URIError('Pathname "'+r.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):i}return n&&(r.key=n),o?r.pathname?"/"!==r.pathname.charAt(0)&&(r.pathname=u(r.pathname,o.pathname)):r.pathname=o.pathname:r.pathname||(r.pathname="/"),r}function w(){var e=null;var t=[];return{setPrompt:function(t){return e=t,function(){e===t&&(e=null)}},confirmTransitionTo:function(t,n,o,r){if(null!=e){var i="function"===typeof e?e(t,n):e;"string"===typeof i?"function"===typeof o?o(i,r):r(!0):r(!1!==i)}else r(!0)},appendListener:function(e){var n=!0;function o(){n&&e.apply(void 0,arguments)}return t.push(o),function(){n=!1,t=t.filter(function(e){return e!==o})}},notifyListeners:function(){for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];t.forEach(function(e){return e.apply(void 0,n)})}}}var $=!("undefined"===typeof window||!window.document||!window.document.createElement);function x(e,t){t(window.confirm(e))}var k="popstate",S="hashchange";function E(){try{return window.history.state||{}}catch(e){return{}}}function C(e){void 0===e&&(e={}),$||p(!1);var t=window.history,n=function(){var e=window.navigator.userAgent;return(-1===e.indexOf("Android 2.")&&-1===e.indexOf("Android 4.0")||-1===e.indexOf("Mobile Safari")||-1!==e.indexOf("Chrome")||-1!==e.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history}(),o=!(-1===window.navigator.userAgent.indexOf("Trident")),r=e,i=r.forceRefresh,a=void 0!==i&&i,l=r.getUserConfirmation,c=void 0===l?x:l,u=r.keyLength,d=void 0===u?6:u,f=e.basename?b(m(e.basename)):"";function h(e){var t=e||{},n=t.key,o=t.state,r=window.location,i=r.pathname+r.search+r.hash;return f&&(i=g(i,f)),y(i,o,n)}function C(){return Math.random().toString(36).substr(2,d)}var T=w();function _(e){s(V,e),V.length=t.length,T.notifyListeners(V.location,V.action)}function P(e){(function(e){return void 0===e.state&&-1===navigator.userAgent.indexOf("CriOS")})(e)||N(h(e.state))}function L(){N(h(E()))}var R=!1;function N(e){if(R)R=!1,_();else{T.confirmTransitionTo(e,"POP",c,function(t){t?_({action:"POP",location:e}):function(e){var t=V.location,n=I.indexOf(t.key);-1===n&&(n=0);var o=I.indexOf(e.key);-1===o&&(o=0);var r=n-o;r&&(R=!0,D(r))}(e)})}}var O=h(E()),I=[O.key];function z(e){return f+v(e)}function D(e){t.go(e)}var A=0;function M(e){1===(A+=e)&&1===e?(window.addEventListener(k,P),o&&window.addEventListener(S,L)):0===A&&(window.removeEventListener(k,P),o&&window.removeEventListener(S,L))}var B=!1;var V={length:t.length,action:"POP",location:O,createHref:z,push:function(e,o){var r="PUSH",i=y(e,o,C(),V.location);T.confirmTransitionTo(i,r,c,function(e){if(e){var o=z(i),s=i.key,l=i.state;if(n)if(t.pushState({key:s,state:l},null,o),a)window.location.href=o;else{var c=I.indexOf(V.location.key),u=I.slice(0,c+1);u.push(i.key),I=u,_({action:r,location:i})}else window.location.href=o}})},replace:function(e,o){var r="REPLACE",i=y(e,o,C(),V.location);T.confirmTransitionTo(i,r,c,function(e){if(e){var o=z(i),s=i.key,l=i.state;if(n)if(t.replaceState({key:s,state:l},null,o),a)window.location.replace(o);else{var c=I.indexOf(V.location.key);-1!==c&&(I[c]=i.key),_({action:r,location:i})}else window.location.replace(o)}})},go:D,goBack:function(){D(-1)},goForward:function(){D(1)},block:function(e){void 0===e&&(e=!1);var t=T.setPrompt(e);return B||(M(1),B=!0),function(){return B&&(B=!1,M(-1)),t()}},listen:function(e){var t=T.appendListener(e);return M(1),function(){M(-1),t()}}};return V}function T(e,t,n){return Math.min(Math.max(e,t),n)}var _=n(123),P=n.n(_);n(681);function L(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(-1!==t.indexOf(o))continue;n[o]=e[o]}return n}var R=n(219),N=n.n(R),O=1073741823,I="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{};var z=e.createContext||function(t,n){var o,i,s="__create-react-context-"+function(){var e="__global_unique_id__";return I[e]=(I[e]||0)+1}()+"__",l=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return(t=e.call.apply(e,[this].concat(o))||this).emitter=function(e){var t=[];return{on:function(e){t.push(e)},off:function(e){t=t.filter(function(t){return t!==e})},get:function(){return e},set:function(n,o){e=n,t.forEach(function(t){return t(e,o)})}}}(t.props.value),t}r(t,e);var o=t.prototype;return o.getChildContext=function(){var e;return(e={})[s]=this.emitter,e},o.componentWillReceiveProps=function(e){if(this.props.value!==e.value){var t,o=this.props.value,r=e.value;((i=o)===(a=r)?0!==i||1/i===1/a:i!==i&&a!==a)?t=0:(t="function"===typeof n?n(o,r):O,0!==(t|=0)&&this.emitter.set(e.value,t))}var i,a},o.render=function(){return this.props.children},t}(e.Component);l.childContextTypes=((o={})[s]=a().object.isRequired,o);var c=function(e){function n(){for(var t,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return(t=e.call.apply(e,[this].concat(o))||this).observedBits=void 0,t.state={value:t.getValue()},t.onUpdate=function(e,n){0!==((0|t.observedBits)&n)&&t.setState({value:t.getValue()})},t}r(n,e);var o=n.prototype;return o.componentWillReceiveProps=function(e){var t=e.observedBits;this.observedBits=void 0===t||null===t?O:t},o.componentDidMount=function(){this.context[s]&&this.context[s].on(this.onUpdate);var e=this.props.observedBits;this.observedBits=void 0===e||null===e?O:e},o.componentWillUnmount=function(){this.context[s]&&this.context[s].off(this.onUpdate)},o.getValue=function(){return this.context[s]?this.context[s].get():t},o.render=function(){return(e=this.props.children,Array.isArray(e)?e[0]:e)(this.state.value);var e},n}(e.Component);return c.contextTypes=((i={})[s]=a().object,i),{Provider:l,Consumer:c}},D=function(e){var t=z();return t.displayName=e,t},A=D("Router-History"),M=D("Router"),B=function(t){function n(e){var n;return(n=t.call(this,e)||this).state={location:e.history.location},n._isMounted=!1,n._pendingLocation=null,e.staticContext||(n.unlisten=e.history.listen(function(e){n._pendingLocation=e})),n}r(n,t),n.computeRootMatch=function(e){return{path:"/",url:"/",params:{},isExact:"/"===e}};var o=n.prototype;return o.componentDidMount=function(){var e=this;this._isMounted=!0,this.unlisten&&this.unlisten(),this.props.staticContext||(this.unlisten=this.props.history.listen(function(t){e._isMounted&&e.setState({location:t})})),this._pendingLocation&&this.setState({location:this._pendingLocation})},o.componentWillUnmount=function(){this.unlisten&&(this.unlisten(),this._isMounted=!1,this._pendingLocation=null)},o.render=function(){return e.createElement(M.Provider,{value:{history:this.props.history,location:this.state.location,match:n.computeRootMatch(this.state.location.pathname),staticContext:this.props.staticContext}},e.createElement(A.Provider,{children:this.props.children||null,value:this.props.history}))},n}(e.Component);e.Component;var V=function(e){function t(){return e.apply(this,arguments)||this}r(t,e);var n=t.prototype;return n.componentDidMount=function(){this.props.onMount&&this.props.onMount.call(this,this)},n.componentDidUpdate=function(e){this.props.onUpdate&&this.props.onUpdate.call(this,this,e)},n.componentWillUnmount=function(){this.props.onUnmount&&this.props.onUnmount.call(this,this)},n.render=function(){return null},t}(e.Component);var j={},H=0;function F(e,t){return void 0===e&&(e="/"),void 0===t&&(t={}),"/"===e?e:function(e){if(j[e])return j[e];var t=P().compile(e);return H<1e4&&(j[e]=t,H++),t}(e)(t,{pretty:!0})}function W(t){var n=t.computedMatch,o=t.to,r=t.push,i=void 0!==r&&r;return e.createElement(M.Consumer,null,function(t){t||p(!1);var r=t.history,a=t.staticContext,l=i?r.push:r.replace,c=y(n?"string"===typeof o?F(o,n.params):s({},o,{pathname:F(o.pathname,n.params)}):o);return a?(l(c),null):e.createElement(V,{onMount:function(){l(c)},onUpdate:function(e,t){var n,o,r=y(t.to);n=r,o=s({},c,{key:r.key}),n.pathname===o.pathname&&n.search===o.search&&n.hash===o.hash&&n.key===o.key&&f(n.state,o.state)||l(c)},to:o})})}var U={},q=0;function Q(e,t){void 0===t&&(t={}),("string"===typeof t||Array.isArray(t))&&(t={path:t});var n=t,o=n.path,r=n.exact,i=void 0!==r&&r,a=n.strict,s=void 0!==a&&a,l=n.sensitive,c=void 0!==l&&l;return[].concat(o).reduce(function(t,n){if(!n&&""!==n)return null;if(t)return t;var o=function(e,t){var n=""+t.end+t.strict+t.sensitive,o=U[n]||(U[n]={});if(o[e])return o[e];var r=[],i={regexp:P()(e,r,t),keys:r};return q<1e4&&(o[e]=i,q++),i}(n,{end:i,strict:s,sensitive:c}),r=o.regexp,a=o.keys,l=r.exec(e);if(!l)return null;var u=l[0],d=l.slice(1),f=e===u;return i&&!f?null:{path:n,url:"/"===n&&""===u?"/":u,isExact:f,params:a.reduce(function(e,t,n){return e[t.name]=d[n],e},{})}},null)}var K=function(t){function n(){return t.apply(this,arguments)||this}return r(n,t),n.prototype.render=function(){var t=this;return e.createElement(M.Consumer,null,function(n){n||p(!1);var o=t.props.location||n.location,r=s({},n,{location:o,match:t.props.computedMatch?t.props.computedMatch:t.props.path?Q(o.pathname,t.props):n.match}),i=t.props,a=i.children,l=i.component,c=i.render;return Array.isArray(a)&&function(t){return 0===e.Children.count(t)}(a)&&(a=null),e.createElement(M.Provider,{value:r},r.match?a?"function"===typeof a?a(r):a:l?e.createElement(l,r):c?c(r):null:"function"===typeof a?a(r):null)})},n}(e.Component);function X(e){return"/"===e.charAt(0)?e:"/"+e}function Y(e,t){if(!e)return t;var n=X(e);return 0!==t.pathname.indexOf(n)?t:s({},t,{pathname:t.pathname.substr(n.length)})}function G(e){return"string"===typeof e?e:v(e)}function Z(e){return function(){p(!1)}}function J(){}e.Component;e.Component;e.useContext;var ee=n(384),te=(n(507),n(619));function ne(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}Object.create;Object.create;"function"===typeof SuppressedError&&SuppressedError;var oe=n(950),re=n(441),ie=n(406);const ae=(e,t)=>null!==t.closest(e),se=(e,t)=>"string"===typeof e&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},t):t,le=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionScrollStart=(0,re.lh)(this,"ionScrollStart",7),this.ionScroll=(0,re.lh)(this,"ionScroll",7),this.ionScrollEnd=(0,re.lh)(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.color=void 0,this.fullscreen=!1,this.forceOverscroll=void 0,this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}connectedCallback(){if(this.isMainContent=null===this.el.closest("ion-menu, ion-popover, ion-modal"),(0,ee.l)(this.el)){const e=this.tabsElement=this.el.closest("ion-tabs");null!==e&&(this.tabsLoadCallback=()=>this.resize(),e.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),(0,ee.l)(this.el)){const{tabsElement:e,tabsLoadCallback:t}=this;null!==e&&void 0!==t&&e.removeEventListener("ionTabBarLoaded",t),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{null!==this.el.offsetParent&&this.resize()},100)}shouldForceOverscroll(){const{forceOverscroll:e}=this,t=(0,te.b)(this);return void 0===e?"ios"===t&&(0,te.a)("ios"):e}resize(){re.L2.isBrowser&&(this.fullscreen?(0,re.gv)(()=>this.readDimensions()):0===this.cTop&&0===this.cBottom||(this.cTop=this.cBottom=0,(0,re.$x)(this)))}readDimensions(){const e=ce(this.el),t=Math.max(this.el.offsetTop,0),n=Math.max(e.offsetHeight-t-this.el.offsetHeight,0);(t!==this.cTop||n!==this.cBottom)&&(this.cTop=t,this.cBottom=n,(0,re.$x)(this))}onScroll(e){const t=Date.now(),n=!this.isScrolling;this.lastScroll=t,n&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,(0,re.gv)(t=>{this.queued=!1,this.detail.event=e,ue(this.detail,this.scrollEl,t,n),this.ionScroll.emit(this.detail)}))}async getScrollElement(){return this.scrollEl||await new Promise(e=>(0,ee.c)(this.el,e)),Promise.resolve(this.scrollEl)}async getBackgroundElement(){return this.backgroundContentEl||await new Promise(e=>(0,ee.c)(this.el,e)),Promise.resolve(this.backgroundContentEl)}scrollToTop(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.scrollToPoint(void 0,0,e)}async scrollToBottom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const t=await this.getScrollElement(),n=t.scrollHeight-t.clientHeight;return this.scrollToPoint(void 0,n,e)}async scrollByPoint(e,t,n){const o=await this.getScrollElement();return this.scrollToPoint(e+o.scrollLeft,t+o.scrollTop,n)}async scrollToPoint(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const o=await this.getScrollElement();if(n<32)return null!=t&&(o.scrollTop=t),void(null!=e&&(o.scrollLeft=e));let r,i=0;const a=new Promise(e=>r=e),s=o.scrollTop,l=o.scrollLeft,c=null!=t?t-s:0,u=null!=e?e-l:0,d=e=>{const t=Math.min(1,(e-i)/n)-1,a=Math.pow(t,3)+1;0!==c&&(o.scrollTop=Math.floor(a*c+s)),0!==u&&(o.scrollLeft=Math.floor(a*u+l)),a<1?requestAnimationFrame(d):r()};return requestAnimationFrame(e=>{i=e,d(e)}),a}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){const{isMainContent:e,scrollX:t,scrollY:n,el:o}=this,r=(0,ie.i)(o)?"rtl":"ltr",i=(0,te.b)(this),a=this.shouldForceOverscroll(),s="ios"===i,l=e?"main":"div";return this.resize(),(0,re.h)(re.xr,{key:"e13815c0e6f6095150b112d3a1aaf2f509aa0d0b",class:se(this.color,{[i]:!0,"content-sizing":ae("ion-popover",this.el),overscroll:a,[`content-${r}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},(0,re.h)("div",{key:"8006c4a10d8f7dc83c646246961d018a8097236e",ref:e=>this.backgroundContentEl=e,id:"background-content",part:"background"}),(0,re.h)(l,{key:"4dd2f58421493f7a4ca42f8f5d7b85cda8e320ea",class:{"inner-scroll":!0,"scroll-x":t,"scroll-y":n,overscroll:(t||n)&&a},ref:e=>this.scrollEl=e,onScroll:this.scrollEvents?e=>this.onScroll(e):void 0,part:"scroll"},(0,re.h)("slot",{key:"37904f8f1d8319156cd901feb21930ef674fe0f7"})),s?(0,re.h)("div",{class:"transition-effect"},(0,re.h)("div",{class:"transition-cover"}),(0,re.h)("div",{class:"transition-shadow"})):null,(0,re.h)("slot",{key:"8f696583903af0548d064dca1a6bae060e127485",name:"fixed"}))}get el(){return this}static get style(){return':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.outer-content){--background:var(--ion-color-step-50, #f2f2f2)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}'}},[1,"ion-content",{color:[513],fullscreen:[4],forceOverscroll:[1028,"force-overscroll"],scrollX:[4,"scroll-x"],scrollY:[4,"scroll-y"],scrollEvents:[4,"scroll-events"],getScrollElement:[64],getBackgroundElement:[64],scrollToTop:[64],scrollToBottom:[64],scrollByPoint:[64],scrollToPoint:[64]},[[9,"resize","onResize"]]]),ce=e=>{const t=e.closest("ion-tabs");if(t)return t;const n=e.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content");return n||(e=>{var t;return e.parentElement?e.parentElement:(null===(t=e.parentNode)||void 0===t?void 0:t.host)?e.parentNode.host:null})(e)},ue=(e,t,n,o)=>{const r=e.currentX,i=e.currentY,a=e.currentTime,s=t.scrollLeft,l=t.scrollTop,c=n-a;if(o&&(e.startTime=n,e.startX=s,e.startY=l,e.velocityX=e.velocityY=0),e.currentTime=n,e.currentX=e.scrollLeft=s,e.currentY=e.scrollTop=l,e.deltaX=s-e.startX,e.deltaY=l-e.startY,c>0&&c<100){const t=(s-r)/c,n=(l-i)/c;e.velocityX=.7*t+.3*e.velocityX,e.velocityY=.7*n+.3*e.velocityY}};const de=function(){if("undefined"===typeof customElements)return;["ion-content"].forEach(e=>{if("ion-content"===e)customElements.get(e)||customElements.define(e,le)})};var fe=n(721);const he=e=>{const t=document.querySelector(`${e}.ion-cloned-element`);if(null!==t)return t;const n=document.createElement(e);return n.classList.add("ion-cloned-element"),n.style.setProperty("display","none"),document.body.appendChild(n),n},pe=e=>{if(!e)return;const t=e.querySelectorAll("ion-toolbar");return{el:e,toolbars:Array.from(t).map(e=>{const t=e.querySelector("ion-title");return{el:e,background:e.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:t,innerTitleEl:t?t.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(e.querySelectorAll("ion-buttons"))}})}},me=(e,t)=>{"fade"!==e.collapse&&(void 0===t?e.style.removeProperty("--opacity-scale"):e.style.setProperty("--opacity-scale",t.toString()))},ge=(e,t,n,o)=>{(0,re.bN)(()=>{const r=o.scrollTop;((e,t,n)=>{if(!e[0].isIntersecting)return;const o=e[0].intersectionRatio>.9||n<=0?0:100*(1-e[0].intersectionRatio)/75;me(t.el,1===o?void 0:o)})(e,t,r);const i=e[0],a=i.intersectionRect,s=a.width*a.height,l=i.rootBounds.width*i.rootBounds.height,c=0===s&&0===l,u=Math.abs(a.left-i.boundingClientRect.left),d=Math.abs(a.right-i.boundingClientRect.right);if(!c&&!(s>0&&(u>=5||d>=5)))if(i.isIntersecting)be(t,!1),be(n);else{(0===a.x&&0===a.y||0!==a.width&&0!==a.height)&&r>0&&(be(t),be(n,!1),me(t.el))}})},be=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=e.el;t?(n.classList.remove("header-collapse-condense-inactive"),n.removeAttribute("aria-hidden")):(n.classList.add("header-collapse-condense-inactive"),n.setAttribute("aria-hidden","true"))},ve=function(){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach(n=>{const o=n.ionTitleEl,r=n.innerTitleEl;o&&"large"===o.size&&(r.style.transition=t?"all 0.2s ease-in-out":"",r.style.transform=`scale3d(${e}, ${e}, 1)`)})},ye=(e,t,n)=>{(0,re.gv)(()=>{const o=e.scrollTop,r=t.clientHeight,i=n?n.clientHeight:0;if(null!==n&&o<i)return t.style.setProperty("--opacity-scale","0"),void e.style.setProperty("clip-path",`inset(${r}px 0px 0px 0px)`);const a=o-i,s=(0,ee.m)(0,a/10,1);(0,re.bN)(()=>{e.style.removeProperty("clip-path"),t.style.setProperty("--opacity-scale",s.toString())})})},we=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.inheritedAttributes={},this.setupFadeHeader=async(e,t)=>{const n=this.scrollEl=await(0,fe.g)(e);this.contentScrollCallback=()=>{ye(this.scrollEl,this.el,t)},n.addEventListener("scroll",this.contentScrollCallback),ye(this.scrollEl,this.el,t)},this.collapse=void 0,this.translucent=!1}componentWillLoad(){this.inheritedAttributes=(0,ee.i)(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}async checkCollapsibleHeader(){if("ios"!==(0,te.b)(this))return;const{collapse:e}=this,t="condense"===e,n="fade"===e;if(this.destroyCollapsibleHeader(),t){const e=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),t=e?(0,fe.f)(e):null;(0,re.bN)(()=>{he("ion-title").size="large",he("ion-back-button")}),await this.setupCondenseHeader(t,e)}else if(n){const e=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),t=e?(0,fe.f)(e):null;if(!t)return void(0,fe.p)(this.el);const n=t.querySelector('ion-header[collapse="condense"]');await this.setupFadeHeader(t,n)}}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}async setupCondenseHeader(e,t){if(!e||!t)return void(0,fe.p)(this.el);if("undefined"===typeof IntersectionObserver)return;this.scrollEl=await(0,fe.g)(e);const n=t.querySelectorAll("ion-header");if(this.collapsibleMainHeader=Array.from(n).find(e=>"condense"!==e.collapse),!this.collapsibleMainHeader)return;const o=pe(this.collapsibleMainHeader),r=pe(this.el);if(!o||!r)return;be(o,!1),me(o.el,0);this.intersectionObserver=new IntersectionObserver(e=>{ge(e,o,r,this.scrollEl)},{root:e,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),this.intersectionObserver.observe(r.toolbars[r.toolbars.length-1].el),this.contentScrollCallback=()=>{((e,t,n)=>{(0,re.gv)(()=>{const o=e.scrollTop,r=(0,ee.m)(1,1+-o/500,1.1);null===n.querySelector("ion-refresher.refresher-native")&&(0,re.bN)(()=>{ve(t.toolbars,r)})})})(this.scrollEl,r,e)},this.scrollEl.addEventListener("scroll",this.contentScrollCallback),(0,re.bN)(()=>{void 0!==this.collapsibleMainHeader&&this.collapsibleMainHeader.classList.add("header-collapse-main")})}render(){const{translucent:e,inheritedAttributes:t}=this,n=(0,te.b)(this),o=this.collapse||"none",r=ae("ion-menu",this.el)?"none":"banner";return(0,re.h)(re.xr,Object.assign({key:"9fa0af97b605f9fe98b13361bc3d1289745c549f",role:r,class:{[n]:!0,[`header-${n}`]:!0,"header-translucent":this.translucent,[`header-collapse-${o}`]:!0,[`header-translucent-${n}`]:this.translucent}},t),"ios"===n&&e&&(0,re.h)("div",{key:"1a780d2625302f2465718e304bdd3794c89c9845",class:"header-background"}),(0,re.h)("slot",{key:"b2b8557b44be40c590bfcc362ac4350f9f8b889e"}))}get el(){return this}static get style(){return{ios:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",md:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"}}},[36,"ion-header",{collapse:[1],translucent:[4]}]);const $e=function(){if("undefined"===typeof customElements)return;["ion-header"].forEach(e=>{if("ion-header"===e)customElements.get(e)||customElements.define(e,we)})},xe=(e,t,n,o,r)=>Se(e[1],t[1],n[1],o[1],r).map(r=>ke(e[0],t[0],n[0],o[0],r)),ke=(e,t,n,o,r)=>r*(3*t*Math.pow(r-1,2)+r*(-3*n*r+3*n+o*r))-e*Math.pow(r-1,3),Se=(e,t,n,o,r)=>Ee((o-=r)-3*(n-=r)+3*(t-=r)-(e-=r),3*n-6*t+3*e,3*t-3*e,e).filter(e=>e>=0&&e<=1),Ee=(e,t,n,o)=>{if(0===e)return((e,t,n)=>{const o=t*t-4*e*n;return o<0?[]:[(-t+Math.sqrt(o))/(2*e),(-t-Math.sqrt(o))/(2*e)]})(t,n,o);const r=(3*(n/=e)-(t/=e)*t)/3,i=(2*t*t*t-9*t*n+27*(o/=e))/27;if(0===r)return[Math.pow(-i,1/3)];if(0===i)return[Math.sqrt(-r),-Math.sqrt(-r)];const a=Math.pow(i/2,2)+Math.pow(r/3,3);if(0===a)return[Math.pow(i/2,.5)-t/3];if(a>0)return[Math.pow(-i/2+Math.sqrt(a),1/3)-Math.pow(i/2+Math.sqrt(a),1/3)-t/3];const s=Math.sqrt(Math.pow(-r/3,3)),l=Math.acos(-i/(2*Math.sqrt(Math.pow(-r/3,3)))),c=2*Math.pow(s,1/3);return[c*Math.cos(l/3)-t/3,c*Math.cos((l+2*Math.PI)/3)-t/3,c*Math.cos((l+4*Math.PI)/3)-t/3]};var Ce=n(435),Te=n(903);const _e=async(e,t,n,o,r,i)=>{var a;if(e)return e.attachViewToDom(t,n,r,o);if(!i&&"string"!==typeof n&&!(n instanceof HTMLElement))throw new Error("framework delegate is missing");const s="string"===typeof n?null===(a=t.ownerDocument)||void 0===a?void 0:a.createElement(n):n;return o&&o.forEach(e=>s.classList.add(e)),r&&Object.assign(s,r),t.appendChild(s),await new Promise(e=>(0,ee.c)(s,e)),s};class Pe{constructor(e,t){this.component=e,this.params=t,this.state=1}async init(e){if(this.state=2,!this.element){const t=this.component;this.element=await _e(this.delegate,e,t,["ion-page","ion-page-invisible"],this.params)}}_destroy(){(0,ee.p)(3!==this.state,"view state must be ATTACHED");const e=this.element;e&&(this.delegate?this.delegate.removeViewFromDom(e.parentElement,e):e.remove()),this.nav=void 0,this.state=3}}const Le=(e,t,n)=>!!e&&(e.component===t&&(0,ee.s)(e.params,n)),Re=(e,t)=>e?e instanceof Pe?e:new Pe(e,t):null,Ne=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=(0,re.lh)(this,"ionNavWillLoad",7),this.ionNavWillChange=(0,re.lh)(this,"ionNavWillChange",3),this.ionNavDidChange=(0,re.lh)(this,"ionNavDidChange",3),this.transInstr=[],this.gestureOrAnimationInProgress=!1,this.useRouter=!1,this.isTransitioning=!1,this.destroyed=!1,this.views=[],this.didLoad=!1,this.delegate=void 0,this.swipeGesture=void 0,this.animated=!0,this.animation=void 0,this.rootParams=void 0,this.root=void 0}swipeGestureChanged(){this.gesture&&this.gesture.enable(!0===this.swipeGesture)}rootChanged(){const e=re.L2.isDev;void 0!==this.root&&!1!==this.didLoad&&(this.useRouter?e&&(0,Ce.p)("<ion-nav> does not support a root attribute when using ion-router.",this.el):void 0!==this.root&&this.setRoot(this.root,this.rootParams))}componentWillLoad(){if(this.useRouter=null!==document.querySelector("ion-router")&&null===this.el.closest("[no-router]"),void 0===this.swipeGesture){const e=(0,te.b)(this);this.swipeGesture=te.c.getBoolean("swipeBackEnabled","ios"===e)}this.ionNavWillLoad.emit()}async componentDidLoad(){this.didLoad=!0,this.rootChanged(),this.gesture=(await n.e(806).then(n.bind(n,806))).createSwipeBackGesture(this.el,this.canStart.bind(this),this.onStart.bind(this),this.onMove.bind(this),this.onEnd.bind(this)),this.swipeGestureChanged()}connectedCallback(){this.destroyed=!1}disconnectedCallback(){for(const e of this.views)(0,Te.l)(e.element,Te.d),e._destroy();this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.transInstr.length=0,this.views.length=0,this.destroyed=!0}push(e,t,n,o){return this.insert(-1,e,t,n,o)}insert(e,t,n,o,r){return this.insertPages(e,[{component:t,componentProps:n}],o,r)}insertPages(e,t,n,o){return this.queueTrns({insertStart:e,insertViews:t,opts:n},o)}pop(e,t){return this.removeIndex(-1,1,e,t)}popTo(e,t,n){const o={removeStart:-1,removeCount:-1,opts:t};return"object"===typeof e&&e.component?(o.removeView=e,o.removeStart=1):"number"===typeof e&&(o.removeStart=e+1),this.queueTrns(o,n)}popToRoot(e,t){return this.removeIndex(1,-1,e,t)}removeIndex(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;return this.queueTrns({removeStart:e,removeCount:t,opts:n},o)}setRoot(e,t,n,o){return this.setPages([{component:e,componentProps:t}],n,o)}setPages(e,t,n){return null!==t&&void 0!==t||(t={}),!0!==t.animated&&(t.animated=!1),this.queueTrns({insertStart:0,insertViews:e,removeStart:0,removeCount:-1,opts:t},n)}setRouteId(e,t,n,o){const r=this.getActiveSync();if(Le(r,e,t))return Promise.resolve({changed:!1,element:r.element});let i;const a=new Promise(e=>i=e);let s;const l={updateURL:!1,viewIsReady:e=>{let t;const n=new Promise(e=>t=e);return i({changed:!0,element:e,markVisible:async()=>{t(),await s}}),n}};if("root"===n)s=this.setRoot(e,t,l);else{const r=this.views.find(n=>Le(n,e,t));r?s=this.popTo(r,Object.assign(Object.assign({},l),{direction:"back",animationBuilder:o})):"forward"===n?s=this.push(e,t,Object.assign(Object.assign({},l),{animationBuilder:o})):"back"===n&&(s=this.setRoot(e,t,Object.assign(Object.assign({},l),{direction:"back",animated:!0,animationBuilder:o})))}return a}async getRouteId(){const e=this.getActiveSync();if(e)return{id:e.element.tagName,params:e.params,element:e.element}}async getActive(){return this.getActiveSync()}async getByIndex(e){return this.views[e]}async canGoBack(e){return this.canGoBackSync(e)}async getPrevious(e){return this.getPreviousSync(e)}getLength(){return this.views.length}getActiveSync(){return this.views[this.views.length-1]}canGoBackSync(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getActiveSync();return!(!e||!this.getPreviousSync(e))}getPreviousSync(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getActiveSync();if(!e)return;const t=this.views,n=t.indexOf(e);return n>0?t[n-1]:void 0}async queueTrns(e,t){var n,o;if(this.isTransitioning&&(null===(n=e.opts)||void 0===n?void 0:n.skipIfBusy))return!1;const r=new Promise((t,n)=>{e.resolve=t,e.reject=n});if(e.done=t,e.opts&&!1!==e.opts.updateURL&&this.useRouter){const t=document.querySelector("ion-router");if(t){const n=await t.canTransition();if(!1===n)return!1;if("string"===typeof n)return t.push(n,e.opts.direction||"back"),!1}}return 0===(null===(o=e.insertViews)||void 0===o?void 0:o.length)&&(e.insertViews=void 0),this.transInstr.push(e),this.nextTrns(),r}success(e,t){if(this.destroyed)this.fireError("nav controller was destroyed",t);else if(t.done&&t.done(e.hasCompleted,e.requiresTransition,e.enteringView,e.leavingView,e.direction),t.resolve(e.hasCompleted),!1!==t.opts.updateURL&&this.useRouter){const t=document.querySelector("ion-router");if(t){const n="back"===e.direction?"back":"forward";t.navChanged(n)}}}failed(e,t){this.destroyed?this.fireError("nav controller was destroyed",t):(this.transInstr.length=0,this.fireError(e,t))}fireError(e,t){t.done&&t.done(!1,!1,e),t.reject&&!this.destroyed?t.reject(e):t.resolve(!1)}nextTrns(){if(this.isTransitioning)return!1;const e=this.transInstr.shift();return!!e&&(this.runTransition(e),!0)}async runTransition(e){try{this.ionNavWillChange.emit(),this.isTransitioning=!0,this.prepareTI(e);const t=this.getActiveSync(),n=this.getEnteringView(e,t);if(!t&&!n)throw new Error("no views in the stack to be removed");n&&1===n.state&&await n.init(this.el),this.postViewInit(n,t,e);const o=(e.enteringRequiresTransition||e.leavingRequiresTransition)&&n!==t;if(o&&e.opts&&t){"back"===e.opts.direction&&(e.opts.animationBuilder=e.opts.animationBuilder||(null===n||void 0===n?void 0:n.animationBuilder)),t.animationBuilder=e.opts.animationBuilder}let r;r=o?await this.transition(n,t,e):{hasCompleted:!0,requiresTransition:!1},this.success(r,e),this.ionNavDidChange.emit()}catch(t){this.failed(t,e)}this.isTransitioning=!1,this.nextTrns()}prepareTI(e){var t,n,o;const r=this.views.length;if(null!==(t=e.opts)&&void 0!==t||(e.opts={}),null!==(n=(o=e.opts).delegate)&&void 0!==n||(o.delegate=this.delegate),void 0!==e.removeView){(0,ee.p)(void 0!==e.removeStart,"removeView needs removeStart"),(0,ee.p)(void 0!==e.removeCount,"removeView needs removeCount");const t=this.views.indexOf(e.removeView);if(t<0)throw new Error("removeView was not found");e.removeStart+=t}void 0!==e.removeStart&&(e.removeStart<0&&(e.removeStart=r-1),e.removeCount<0&&(e.removeCount=r-e.removeStart),e.leavingRequiresTransition=e.removeCount>0&&e.removeStart+e.removeCount===r),e.insertViews&&((e.insertStart<0||e.insertStart>r)&&(e.insertStart=r),e.enteringRequiresTransition=e.insertStart===r);const i=e.insertViews;if(!i)return;(0,ee.p)(i.length>0,"length can not be zero");const a=i.map(e=>e instanceof Pe?e:"component"in e?Re(e.component,null===e.componentProps?void 0:e.componentProps):Re(e,void 0)).filter(e=>null!==e);if(0===a.length)throw new Error("invalid views to insert");for(const s of a){s.delegate=e.opts.delegate;const t=s.nav;if(t&&t!==this)throw new Error("inserted view was already inserted");if(3===s.state)throw new Error("inserted view was already destroyed")}e.insertViews=a}getEnteringView(e,t){const n=e.insertViews;if(void 0!==n)return n[n.length-1];const o=e.removeStart;if(void 0!==o){const n=this.views,r=o+e.removeCount;for(let e=n.length-1;e>=0;e--){const i=n[e];if((e<o||e>=r)&&i!==t)return i}}}postViewInit(e,t,n){var o,r,i;(0,ee.p)(t||e,"Both leavingView and enteringView are null"),(0,ee.p)(n.resolve,"resolve must be valid"),(0,ee.p)(n.reject,"reject must be valid");const a=n.opts,{insertViews:s,removeStart:l,removeCount:c}=n;let u;if(void 0!==l&&void 0!==c){(0,ee.p)(l>=0,"removeStart can not be negative"),(0,ee.p)(c>=0,"removeCount can not be negative"),u=[];for(let n=l;n<l+c;n++){const o=this.views[n];void 0!==o&&o!==e&&o!==t&&u.push(o)}null!==(o=a.direction)&&void 0!==o||(a.direction="back")}const d=this.views.length+(null!==(r=null===s||void 0===s?void 0:s.length)&&void 0!==r?r:0)-(null!==c&&void 0!==c?c:0);if((0,ee.p)(d>=0,"final balance can not be negative"),0===d)throw console.warn("You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.",this,this.el),new Error("navigation stack needs at least one root page");if(s){let e=n.insertStart;for(const t of s)this.insertViewAt(t,e),e++;n.enteringRequiresTransition&&(null!==(i=a.direction)&&void 0!==i||(a.direction="forward"))}if(u&&u.length>0){for(const e of u)(0,Te.l)(e.element,Te.b),(0,Te.l)(e.element,Te.c),(0,Te.l)(e.element,Te.d);for(const e of u)this.destroyView(e)}}async transition(e,t,n){const o=n.opts,r=o.progressAnimation?e=>{void 0===e||this.gestureOrAnimationInProgress?this.sbAni=e:(this.gestureOrAnimationInProgress=!0,e.onFinish(()=>{this.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0}),e.progressEnd(0,0,0))}:void 0,i=(0,te.b)(this),a=e.element,s=t&&t.element,l=Object.assign(Object.assign({mode:i,showGoBack:this.canGoBackSync(e),baseEl:this.el,progressCallback:r,animated:this.animated&&te.c.getBoolean("animated",!0),enteringEl:a,leavingEl:s},o),{animationBuilder:o.animationBuilder||this.animation||te.c.get("navAnimation")}),{hasCompleted:c}=await(0,Te.t)(l);return this.transitionFinish(c,e,t,o)}transitionFinish(e,t,n,o){const r=e?t:n;return r&&this.unmountInactiveViews(r),{hasCompleted:e,requiresTransition:!0,enteringView:t,leavingView:n,direction:o.direction}}insertViewAt(e,t){const n=this.views,o=n.indexOf(e);o>-1?((0,ee.p)(e.nav===this,"view is not part of the nav"),n.splice(o,1),n.splice(t,0,e)):((0,ee.p)(!e.nav,"nav is used"),e.nav=this,n.splice(t,0,e))}removeView(e){(0,ee.p)(2===e.state||3===e.state,"view state should be loaded or destroyed");const t=this.views,n=t.indexOf(e);(0,ee.p)(n>-1,"view must be part of the stack"),n>=0&&t.splice(n,1)}destroyView(e){e._destroy(),this.removeView(e)}unmountInactiveViews(e){if(this.destroyed)return;const t=this.views,n=t.indexOf(e);for(let o=t.length-1;o>=0;o--){const e=t[o],r=e.element;r&&(o>n?((0,Te.l)(r,Te.d),this.destroyView(e)):o<n&&(0,Te.s)(r,!0))}}canStart(){return!this.gestureOrAnimationInProgress&&!!this.swipeGesture&&!this.isTransitioning&&0===this.transInstr.length&&this.canGoBackSync()}onStart(){this.gestureOrAnimationInProgress=!0,this.pop({direction:"back",progressAnimation:!0})}onMove(e){this.sbAni&&this.sbAni.progressStep(e)}onEnd(e,t,n){if(this.sbAni){this.sbAni.onFinish(()=>{this.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0});let o=e?-.001:.001;e?o+=xe([0,0],[.32,.72],[0,1],[1,1],t)[0]:(this.sbAni.easing("cubic-bezier(1, 0, 0.68, 0.28)"),o+=xe([0,0],[1,0],[.68,.28],[1,1],t)[0]),this.sbAni.progressEnd(e?1:0,o,n)}else this.gestureOrAnimationInProgress=!1}render(){return(0,re.h)("slot",{key:"6894eccc60e446294b01261477691ea1e88348ab"})}get el(){return this}static get watchers(){return{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}}static get style(){return":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}"}},[1,"ion-nav",{delegate:[16],swipeGesture:[1028,"swipe-gesture"],animated:[4],animation:[16],rootParams:[16],root:[1],push:[64],insert:[64],insertPages:[64],pop:[64],popTo:[64],popToRoot:[64],removeIndex:[64],setRoot:[64],setPages:[64],setRouteId:[64],getRouteId:[64],getActive:[64],getByIndex:[64],canGoBack:[64],getPrevious:[64]},void 0,{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}]);const Oe=function(){if("undefined"===typeof customElements)return;["ion-nav"].forEach(e=>{if("ion-nav"===e)customElements.get(e)||customElements.define(e,Ne)})},Ie=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionStyle=(0,re.lh)(this,"ionStyle",7),this.color=void 0,this.size=void 0}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){const e=this.getSize();this.ionStyle.emit({[`title-${e}`]:!0})}getSize(){return void 0!==this.size?this.size:"default"}render(){const e=(0,te.b)(this),t=this.getSize();return(0,re.h)(re.xr,{key:"6f43362b782ef7d340c241bb66f1469663c03cc1",class:se(this.color,{[e]:!0,[`title-${t}`]:!0,"title-rtl":"rtl"===document.dir})},(0,re.h)("div",{key:"9c3ff1a289e533ee3426b71ab5560fbea3529502",class:"toolbar-title"},(0,re.h)("slot",{key:"50d5cc5a1519ad58f1994d2f8c8f08f62baac1fe"})))}get el(){return this}static get watchers(){return{size:["sizeChanged"]}}static get style(){return{ios:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}@supports (inset-inline-start: 0){:host{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host{left:0}:host-context([dir=rtl]){left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)){left:unset;right:unset;right:0}}}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",md:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}"}}},[33,"ion-title",{color:[513],size:[1]},void 0,{size:["sizeChanged"]}]);const ze=function(){if("undefined"===typeof customElements)return;["ion-title"].forEach(e=>{if("ion-title"===e)customElements.get(e)||customElements.define(e,Ie)})},De=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.childrenStyles=new Map,this.color=void 0}componentWillLoad(){const e=Array.from(this.el.querySelectorAll("ion-buttons")),t=e.find(e=>"start"===e.slot);t&&t.classList.add("buttons-first-slot");const n=e.reverse(),o=n.find(e=>"end"===e.slot)||n.find(e=>"primary"===e.slot)||n.find(e=>"secondary"===e.slot);o&&o.classList.add("buttons-last-slot")}childrenStyle(e){e.stopPropagation();const t=e.target.tagName,n=e.detail,o={},r=this.childrenStyles.get(t)||{};let i=!1;Object.keys(n).forEach(e=>{const t=`toolbar-${e}`,a=n[e];a!==r[t]&&(i=!0),a&&(o[t]=!0)}),i&&(this.childrenStyles.set(t,o),(0,re.$x)(this))}render(){const e=(0,te.b)(this),t={};return this.childrenStyles.forEach(e=>{Object.assign(t,e)}),(0,re.h)(re.xr,{key:"8907ed75fbb2b1dced55c481bba6363f1dca815b",class:Object.assign(Object.assign({},t),se(this.color,{[e]:!0,"in-toolbar":ae("ion-toolbar",this.el)}))},(0,re.h)("div",{key:"6bfa09b08d6517f0d680f53b739854cecd631bc9",class:"toolbar-background"}),(0,re.h)("div",{key:"1531bd6dd9e0a5843309bba854b744c453037ad0",class:"toolbar-container"},(0,re.h)("slot",{key:"881b41697d386eae651b019128573f0fa432cd33",name:"start"}),(0,re.h)("slot",{key:"64a284e6eae5311ac3125dfadb4bb32bdba9d089",name:"secondary"}),(0,re.h)("div",{key:"c1f47503563b38084b27d7ba54f17ec478482b94",class:"toolbar-content"},(0,re.h)("slot",{key:"9a85acfba72252705619ae32acae9c14f81aa57d"})),(0,re.h)("slot",{key:"89e08bd761dc6940dbebc5d06f5f080af204aa72",name:"primary"}),(0,re.h)("slot",{key:"a1cb7d95627f8a3d24dd4b9c11718fc164f53674",name:"end"})))}get el(){return this}static get style(){return{ios:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, #f7f7f7));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.2))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",md:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, #c1c4cd)));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}"}}},[33,"ion-toolbar",{color:[513]},[[0,"ionStyle","childrenStyle"]]]);const Ae=function(){if("undefined"===typeof customElements)return;["ion-toolbar"].forEach(e=>{if("ion-toolbar"===e)customElements.get(e)||customElements.define(e,De)})};var Me=n(434);const Be=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost()}componentDidLoad(){re.L2.isBrowser&&je(async()=>{const e=(0,te.a)(window,"hybrid");if(te.c.getBoolean("_testing")||n.e(938).then(n.bind(n,938)).then(e=>e.startTapClick(te.c)),te.c.getBoolean("statusTap",e)&&n.e(247).then(n.bind(n,247)).then(e=>e.startStatusTap()),te.c.getBoolean("inputShims",Ve())){const e=(0,te.a)(window,"ios")?"ios":"android";n.e(82).then(n.bind(n,82)).then(t=>t.startInputShims(te.c,e))}const t=await Promise.resolve().then(n.bind(n,434)),o=e||(0,Me.shouldUseCloseWatcher)();te.c.getBoolean("hardwareBackButton",o)?t.startHardwareBackButton():((0,Me.shouldUseCloseWatcher)()&&(0,Ce.p)("experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),t.blockHardwareBackButton()),"undefined"!==typeof window&&n.e(768).then(n.bind(n,768)).then(e=>e.startKeyboardAssist(window)),n.e(834).then(n.bind(n,834)).then(e=>this.focusVisible=e.startFocusVisible())})}async setFocus(e){this.focusVisible&&this.focusVisible.setFocus(e)}render(){const e=(0,te.b)(this);return(0,re.h)(re.xr,{key:"6d7c57453b4be454690e8f1a0721f1e3da8f92aa",class:{[e]:!0,"ion-page":!0,"force-statusbar-padding":te.c.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}"}},[0,"ion-app",{setFocus:[64]}]),Ve=()=>{if((0,te.a)(window,"ios")&&(0,te.a)(window,"mobile"))return!0;return!(!(0,te.a)(window,"android")||!(0,te.a)(window,"mobileweb"))},je=e=>{"requestIdleCallback"in window?window.requestIdleCallback(e):setTimeout(e,32)};const He=function(){if("undefined"===typeof customElements)return;["ion-app"].forEach(e=>{if("ion-app"===e)customElements.get(e)||customElements.define(e,Be)})},Fe=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=(0,re.lh)(this,"ionNavWillLoad",7),this.ionNavWillChange=(0,re.lh)(this,"ionNavWillChange",3),this.ionNavDidChange=(0,re.lh)(this,"ionNavDidChange",3),this.lockController=(()=>{let e;return{lock:async()=>{const t=e;let n;return e=new Promise(e=>n=e),void 0!==t&&await t,n}}})(),this.gestureOrAnimationInProgress=!1,this.mode=(0,te.b)(this),this.delegate=void 0,this.animated=!0,this.animation=void 0,this.swipeHandler=void 0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(void 0!==this.swipeHandler)}async connectedCallback(){const e=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(await n.e(806).then(n.bind(n,806))).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>e(),e=>{var t;return null===(t=this.ani)||void 0===t?void 0:t.progressStep(e)},(e,t,n)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(e)},{oneTimeCallback:!0});let o=e?-.001:.001;e?o+=xe([0,0],[.32,.72],[0,1],[1,1],t)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),o+=xe([0,0],[1,0],[.68,.28],[1,1],t)[0]),this.ani.progressEnd(e?1:0,o,n)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}async commit(e,t,n){const o=await this.lockController.lock();let r=!1;try{r=await this.transition(e,t,n)}catch(i){console.error(i)}return o(),r}async setRouteId(e,t,n,o){return{changed:await this.setRoot(e,t,{duration:"root"===n?0:void 0,direction:"back"===n?"back":"forward",animationBuilder:o}),element:this.activeEl}}async getRouteId(){const e=this.activeEl;return e?{id:e.tagName,element:e,params:this.activeParams}:void 0}async setRoot(e,t,n){if(this.activeComponent===e&&(0,ee.s)(t,this.activeParams))return!1;const o=this.activeEl,r=await _e(this.delegate,this.el,e,["ion-page","ion-page-invisible"],t);return this.activeComponent=e,this.activeEl=r,this.activeParams=t,await this.commit(r,o,n),await((e,t)=>{if(t){if(e){const n=t.parentElement;return e.removeViewFromDom(n,t)}t.remove()}return Promise.resolve()})(this.delegate,o),!0}async transition(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t===e)return!1;this.ionNavWillChange.emit();const{el:o,mode:r}=this,i=this.animated&&te.c.getBoolean("animated",!0),a=n.animationBuilder||this.animation||te.c.get("navAnimation");return await(0,Te.t)(Object.assign(Object.assign({mode:r,animated:i,enteringEl:e,leavingEl:t,baseEl:o,deepWait:(0,ee.l)(o),progressCallback:n.progressAnimation?e=>{void 0===e||this.gestureOrAnimationInProgress?this.ani=e:(this.gestureOrAnimationInProgress=!0,e.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),e.progressEnd(0,0,0))}:void 0},n),{animationBuilder:a})),this.ionNavDidChange.emit(),!0}render(){return(0,re.h)("slot",{key:"0949db1bcfde67b462abe9cae72c7a7fd70ea678"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}"}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);const We=function(){if("undefined"===typeof customElements)return;["ion-router-outlet"].forEach(e=>{if("ion-router-outlet"===e)customElements.get(e)||customElements.define(e,Fe)})};var Ue=n(286),qe=n(793);const Qe=e=>{if(void 0===Ue.d||e===qe.a.None||void 0===e)return null;const t=Ue.d.querySelector("ion-app");return null!==t&&void 0!==t?t:Ue.d.body},Ke=e=>{const t=Qe(e);return null===t?0:t.clientHeight},Xe=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionTabBarChanged=(0,re.lh)(this,"ionTabBarChanged",7),this.ionTabBarLoaded=(0,re.lh)(this,"ionTabBarLoaded",7),this.keyboardCtrl=null,this.keyboardVisible=!1,this.color=void 0,this.selectedTab=void 0,this.translucent=!1}selectedTabChanged(){void 0!==this.selectedTab&&this.ionTabBarChanged.emit({tab:this.selectedTab})}componentWillLoad(){this.selectedTabChanged()}async connectedCallback(){this.keyboardCtrl=await(async e=>{let t,n,o,r;const i=async()=>{const e=await qe.K.getResizeMode(),i=void 0===e?void 0:e.mode;t=()=>{void 0===r&&(r=Ke(i)),o=!0,a(o,i)},n=()=>{o=!1,a(o,i)},null===Ue.w||void 0===Ue.w||Ue.w.addEventListener("keyboardWillShow",t),null===Ue.w||void 0===Ue.w||Ue.w.addEventListener("keyboardWillHide",n)},a=(t,n)=>{e&&e(t,s(n))},s=e=>{if(0===r||r===Ke(e))return;const t=Qe(e);return null!==t?new Promise(e=>{const n=new ResizeObserver(()=>{t.clientHeight===r&&(n.disconnect(),e())});n.observe(t)}):void 0};return await i(),{init:i,destroy:()=>{null===Ue.w||void 0===Ue.w||Ue.w.removeEventListener("keyboardWillShow",t),null===Ue.w||void 0===Ue.w||Ue.w.removeEventListener("keyboardWillHide",n),t=n=void 0},isKeyboardVisible:()=>o}})(async(e,t)=>{!1===e&&void 0!==t&&await t,this.keyboardVisible=e})}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}componentDidLoad(){this.ionTabBarLoaded.emit()}render(){const{color:e,translucent:t,keyboardVisible:n}=this,o=(0,te.b)(this),r=n&&"top"!==this.el.getAttribute("slot");return(0,re.h)(re.xr,{key:"5083528e7f802d2f323ce50585edc98eeb9754c6",role:"tablist","aria-hidden":r?"true":null,class:se(e,{[o]:!0,"tab-bar-translucent":t,"tab-bar-hidden":r})},(0,re.h)("slot",{key:"eb33cdd12da49062219d4aa17a319c3e6361c5c5"}))}get el(){return this}static get watchers(){return{selectedTab:["selectedTabChanged"]}}static get style(){return{ios:":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-color-step-50, #f7f7f7));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:0.55px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.2))));--color:var(--ion-tab-bar-color, var(--ion-color-step-600, #666666));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #3880ff));height:50px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.tab-bar-translucent){--background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(210%) blur(20px);backdrop-filter:saturate(210%) blur(20px)}:host(.ion-color.tab-bar-translucent){background:rgba(var(--ion-color-base-rgb), 0.8)}:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.6)}}",md:":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-background-color, #fff));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:1px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.07))));--color:var(--ion-tab-bar-color, var(--ion-color-step-650, #595959));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #3880ff));height:56px}"}}},[33,"ion-tab-bar",{color:[513],selectedTab:[1,"selected-tab"],translucent:[4],keyboardVisible:[32]},void 0,{selectedTab:["selectedTabChanged"]}]);const Ye=function(){if("undefined"===typeof customElements)return;["ion-tab-bar"].forEach(e=>{if("ion-tab-bar"===e)customElements.get(e)||customElements.define(e,Xe)})},Ge=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.type="bounded"}async addRipple(e,t){return new Promise(n=>{(0,re.gv)(()=>{const o=this.el.getBoundingClientRect(),r=o.width,i=o.height,a=Math.sqrt(r*r+i*i),s=Math.max(i,r),l=this.unbounded?s:a+Je,c=Math.floor(s*et),u=l/c;let d=e-o.left,f=t-o.top;this.unbounded&&(d=.5*r,f=.5*i);const h=d-.5*c,p=f-.5*c,m=.5*r-d,g=.5*i-f;(0,re.bN)(()=>{const e=document.createElement("div");e.classList.add("ripple-effect");const t=e.style;t.top=p+"px",t.left=h+"px",t.width=t.height=c+"px",t.setProperty("--final-scale",`${u}`),t.setProperty("--translate-end",`${m}px, ${g}px`);(this.el.shadowRoot||this.el).appendChild(e),setTimeout(()=>{n(()=>{Ze(e)})},325)})})})}get unbounded(){return"unbounded"===this.type}render(){const e=(0,te.b)(this);return(0,re.h)(re.xr,{key:"f1129019a6d556b008c754aeb79618c69baea9f8",role:"presentation",class:{[e]:!0,unbounded:this.unbounded}})}get el(){return this}static get style(){return":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}"}},[1,"ion-ripple-effect",{type:[1],addRipple:[64]}]),Ze=e=>{e.classList.add("fade-out"),setTimeout(()=>{e.remove()},200)},Je=10,et=.5;const tt=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionTabButtonClick=(0,re.lh)(this,"ionTabButtonClick",7),this.inheritedAttributes={},this.onKeyUp=e=>{"Enter"!==e.key&&" "!==e.key||this.selectTab(e)},this.onClick=e=>{this.selectTab(e)},this.disabled=!1,this.download=void 0,this.href=void 0,this.rel=void 0,this.layout=void 0,this.selected=!1,this.tab=void 0,this.target=void 0}onTabBarChanged(e){const t=e.target,n=this.el.parentElement;(e.composedPath().includes(n)||(null===t||void 0===t?void 0:t.contains(this.el)))&&(this.selected=this.tab===e.detail.tab)}componentWillLoad(){this.inheritedAttributes=Object.assign({},(0,ee.d)(this.el,["aria-label"])),void 0===this.layout&&(this.layout=te.c.get("tabButtonLayout","icon-top"))}selectTab(e){void 0!==this.tab&&(this.disabled||this.ionTabButtonClick.emit({tab:this.tab,href:this.href,selected:this.selected}),e.preventDefault())}get hasLabel(){return!!this.el.querySelector("ion-label")}get hasIcon(){return!!this.el.querySelector("ion-icon")}render(){const{disabled:e,hasIcon:t,hasLabel:n,href:o,rel:r,target:i,layout:a,selected:s,tab:l,inheritedAttributes:c}=this,u=(0,te.b)(this),d={download:this.download,href:o,rel:r,target:i};return(0,re.h)(re.xr,{key:"c7b6a72766b71f34800137dadcf29af657bebddf",onClick:this.onClick,onKeyup:this.onKeyUp,id:void 0!==l?`tab-button-${l}`:null,class:{[u]:!0,"tab-selected":s,"tab-disabled":e,"tab-has-label":n,"tab-has-icon":t,"tab-has-label-only":n&&!t,"tab-has-icon-only":t&&!n,[`tab-layout-${a}`]:!0,"ion-activatable":!0,"ion-selectable":!0,"ion-focusable":!0}},(0,re.h)("a",Object.assign({key:"a1eca4a5cf0dfdb55099811d03f204f7b3807a2e"},d,{class:"button-native",part:"native",role:"tab","aria-selected":s?"true":null,"aria-disabled":e?"true":null,tabindex:e?"-1":void 0},c),(0,re.h)("span",{key:"888a6d8b95c2f0ca8f74f492729bd28f0d3273d5",class:"button-inner"},(0,re.h)("slot",{key:"83a234af52ffce9ff0f4cc497712c962115a5813"})),"md"===u&&(0,re.h)("ion-ripple-effect",{key:"771aff1b83233411e0cf706c3e94c78bca534794",type:"unbounded"})))}get el(){return this}static get style(){return{ios:':host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:2px;--padding-bottom:0;--padding-start:2px;max-width:240px;font-size:10px}::slotted(ion-badge){-webkit-padding-start:6px;padding-inline-start:6px;-webkit-padding-end:6px;padding-inline-end:6px;padding-top:1px;padding-bottom:1px;top:4px;height:auto;font-size:12px;line-height:16px}@supports (inset-inline-start: 0){::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}}@supports not (inset-inline-start: 0){::slotted(ion-badge){left:calc(50% + 6px)}:host-context([dir=rtl]) ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}[dir=rtl] ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}@supports selector(:dir(rtl)){::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:calc(50% + 6px)}}}::slotted(ion-icon){margin-top:2px;margin-bottom:2px;font-size:30px}::slotted(ion-icon::before){vertical-align:top}::slotted(ion-label){margin-top:0;margin-bottom:1px;min-height:11px;font-weight:500}:host(.tab-has-label-only) ::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:12px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-label),:host(.tab-layout-icon-start) ::slotted(ion-label),:host(.tab-layout-icon-hide) ::slotted(ion-label){margin-top:2px;margin-bottom:2px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-icon),:host(.tab-layout-icon-start) ::slotted(ion-icon){min-width:24px;height:26px;margin-top:2px;margin-bottom:1px;font-size:24px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:calc(50% + 12px)}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){left:calc(50% + 12px)}:host-context([dir=rtl]):host(.tab-layout-icon-bottom) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-bottom ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 12px)}@supports selector(:dir(rtl)){:host(.tab-layout-icon-bottom:dir(rtl)) ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 12px)}}}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:1px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:4px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:10px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:calc(50% + 35px)}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){left:calc(50% + 35px)}:host-context([dir=rtl]):host(.tab-layout-icon-start) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-start ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-layout-icon-end) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-end ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 35px)}@supports selector(:dir(rtl)){:host(.tab-layout-icon-start:dir(rtl)) ::slotted(ion-badge),:host(.tab-layout-icon-end:dir(rtl)) ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 35px)}}}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:10px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:calc(50% + 30px)}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){left:calc(50% + 30px)}:host-context([dir=rtl]):host(.tab-layout-icon-hide) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-hide ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-has-label-only) ::slotted(ion-badge),:host-context([dir=rtl]).tab-has-label-only ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 30px)}@supports selector(:dir(rtl)){:host(.tab-layout-icon-hide:dir(rtl)) ::slotted(ion-badge),:host(.tab-has-label-only:dir(rtl)) ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 30px)}}}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-label-hide) ::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}',md:':host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:12px;--padding-bottom:0;--padding-start:12px;max-width:168px;font-size:12px;font-weight:normal;letter-spacing:0.03em}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;text-transform:none}::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;-webkit-transform-origin:center center;transform-origin:center center;font-size:22px}:host-context([dir=rtl]) ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){::slotted(ion-icon):dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}::slotted(ion-badge){border-radius:8px;-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;padding-top:3px;padding-bottom:2px;top:8px;min-width:12px;font-size:8px;font-weight:normal}@supports (inset-inline-start: 0){::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}}@supports not (inset-inline-start: 0){::slotted(ion-badge){left:calc(50% + 6px)}:host-context([dir=rtl]) ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}[dir=rtl] ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}@supports selector(:dir(rtl)){::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:calc(50% + 6px)}}}::slotted(ion-badge:empty){display:block;min-width:8px;height:8px}:host(.tab-layout-icon-top) ::slotted(ion-icon){margin-top:6px;margin-bottom:2px}:host(.tab-layout-icon-top) ::slotted(ion-label){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){top:8px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:70%}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){left:70%}:host-context([dir=rtl]):host(.tab-layout-icon-bottom) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-bottom ::slotted(ion-badge){left:unset;right:unset;right:70%}@supports selector(:dir(rtl)){:host(.tab-layout-icon-bottom:dir(rtl)) ::slotted(ion-badge){left:unset;right:unset;right:70%}}}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:6px;margin-bottom:0}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:16px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:80%}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){left:80%}:host-context([dir=rtl]):host(.tab-layout-icon-start) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-start ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-layout-icon-end) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-end ::slotted(ion-badge){left:unset;right:unset;right:80%}@supports selector(:dir(rtl)){:host(.tab-layout-icon-start:dir(rtl)) ::slotted(ion-badge),:host(.tab-layout-icon-end:dir(rtl)) ::slotted(ion-badge){left:unset;right:unset;right:80%}}}:host(.tab-layout-icon-start) ::slotted(ion-icon){-webkit-margin-end:6px;margin-inline-end:6px}:host(.tab-layout-icon-end) ::slotted(ion-icon){-webkit-margin-start:6px;margin-inline-start:6px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:16px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:70%}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){left:70%}:host-context([dir=rtl]):host(.tab-layout-icon-hide) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-hide ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-has-label-only) ::slotted(ion-badge),:host-context([dir=rtl]).tab-has-label-only ::slotted(ion-badge){left:unset;right:unset;right:70%}@supports selector(:dir(rtl)){:host(.tab-layout-icon-hide:dir(rtl)) ::slotted(ion-badge),:host(.tab-has-label-only:dir(rtl)) ::slotted(ion-badge){left:unset;right:unset;right:70%}}}:host(.tab-layout-icon-hide) ::slotted(ion-label),:host(.tab-has-label-only) ::slotted(ion-label){margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){margin-top:0;margin-bottom:0;font-size:24px}'}}},[33,"ion-tab-button",{disabled:[4],download:[1],href:[1],rel:[1],layout:[1025],selected:[1028],tab:[1],target:[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]]);const nt=function(){if("undefined"===typeof customElements)return;["ion-tab-button","ion-ripple-effect"].forEach(e=>{switch(e){case"ion-tab-button":customElements.get(e)||customElements.define(e,tt);break;case"ion-ripple-effect":customElements.get(e)||function(){if("undefined"===typeof customElements)return;["ion-ripple-effect"].forEach(e=>{"ion-ripple-effect"===e&&(customElements.get(e)||customElements.define(e,Ge))})}()}})};let ot;const rt=()=>{if("undefined"===typeof window)return new Map;if(!ot){const e=window;e.Ionicons=e.Ionicons||{},ot=e.Ionicons.map=e.Ionicons.map||new Map}return ot},it=(e,t)=>{const n=rt().get(e);if(n)return n;try{return(0,re.OX)(`svg/${e}.svg`)}catch(o){console.warn(`[Ionicons Warning]: Could not load icon with name "${e}". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`,t)}},at=(e,t,n,o,r)=>{if(n="ios"===(n&&ut(n))?"ios":"md",o&&"ios"===n?e=ut(o):r&&"md"===n?e=ut(r):(e||!t||lt(t)||(e=t),ct(e)&&(e=ut(e))),!ct(e)||""===e.trim())return null;return""!==e.replace(/[a-z]|-|\d/gi,"")?null:e},st=e=>ct(e)&&(e=e.trim(),lt(e))?e:null,lt=e=>e.length>0&&/(\/|\.)/.test(e),ct=e=>"string"===typeof e,ut=e=>e.toLowerCase(),dt=e=>{if(1===e.nodeType){if("script"===e.nodeName.toLowerCase())return!1;for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;if(ct(n)&&0===n.toLowerCase().indexOf("on"))return!1}for(let t=0;t<e.childNodes.length;t++)if(!dt(e.childNodes[t]))return!1}return!0},ft=new Map,ht=new Map;let pt;const mt=(e,t)=>{let n=ht.get(e);if(!n){if("undefined"===typeof fetch||"undefined"===typeof document)return ft.set(e,""),Promise.resolve();if((e=>e.startsWith("data:image/svg+xml"))(e)&&(e=>-1!==e.indexOf(";utf8,"))(e)){pt||(pt=new DOMParser);const t=pt.parseFromString(e,"text/html").querySelector("svg");return t&&ft.set(e,t.outerHTML),Promise.resolve()}n=fetch(e).then(n=>{if(n.ok)return n.text().then(n=>{n&&!1!==t&&(n=(e=>{const t=document.createElement("div");t.innerHTML=e;for(let o=t.childNodes.length-1;o>=0;o--)"svg"!==t.childNodes[o].nodeName.toLowerCase()&&t.removeChild(t.childNodes[o]);const n=t.firstElementChild;if(n&&"svg"===n.nodeName.toLowerCase()){const e=n.getAttribute("class")||"";if(n.setAttribute("class",(e+" s-ion-icon").trim()),dt(n))return t.innerHTML}return""})(n)),ft.set(e,n||"")});ft.set(e,"")}),ht.set(e,n)}return n},gt=(0,re.w$)(class extends re.wt{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.iconName=null,this.inheritedAttributes={},this.didLoadIcon=!1,this.svgContent=void 0,this.isVisible=!1,this.mode=bt(),this.color=void 0,this.ios=void 0,this.md=void 0,this.flipRtl=void 0,this.name=void 0,this.src=void 0,this.icon=void 0,this.size=void 0,this.lazy=!1,this.sanitize=!0}componentWillLoad(){this.inheritedAttributes=function(e){const t={};return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).forEach(n=>{e.hasAttribute(n)&&(null!==e.getAttribute(n)&&(t[n]=e.getAttribute(n)),e.removeAttribute(n))}),t}(this.el,["aria-label"])}connectedCallback(){this.waitUntilVisible(this.el,"50px",()=>{this.isVisible=!0,this.loadIcon()})}componentDidLoad(){this.didLoadIcon||this.loadIcon()}disconnectedCallback(){this.io&&(this.io.disconnect(),this.io=void 0)}waitUntilVisible(e,t,n){if(re.L2.isBrowser&&this.lazy&&"undefined"!==typeof window&&window.IntersectionObserver){const o=this.io=new window.IntersectionObserver(e=>{e[0].isIntersecting&&(o.disconnect(),this.io=void 0,n())},{rootMargin:t});o.observe(e)}else n()}loadIcon(){if(re.L2.isBrowser&&this.isVisible){const e=(e=>{let t=st(e.src);if(t)return t;if(t=at(e.name,e.icon,e.mode,e.ios,e.md),t)return it(t,e);if(e.icon){if(t=st(e.icon),t)return t;if(t=st(e.icon[e.mode]),t)return t}return null})(this);e&&(ft.has(e)?this.svgContent=ft.get(e):mt(e,this.sanitize).then(()=>this.svgContent=ft.get(e)),this.didLoadIcon=!0)}this.iconName=at(this.name,this.icon,this.mode,this.ios,this.md)}render(){const{flipRtl:e,iconName:t,inheritedAttributes:n,el:o}=this,r=this.mode||"md",i=!!t&&((t.includes("arrow")||t.includes("chevron"))&&!1!==e),a=e||i;return(0,re.h)(re.xr,Object.assign({role:"img",class:Object.assign(Object.assign({[r]:!0},vt(this.color)),{[`icon-${this.size}`]:!!this.size,"flip-rtl":a,"icon-rtl":a&&(s=o,s&&""!==s.dir?"rtl"===s.dir.toLowerCase():"rtl"===(null===document||void 0===document?void 0:document.dir.toLowerCase()))})},n),re.L2.isBrowser&&this.svgContent?(0,re.h)("div",{class:"icon-inner",innerHTML:this.svgContent}):(0,re.h)("div",{class:"icon-inner"}));var s}static get assetsDirs(){return["svg"]}get el(){return this}static get watchers(){return{name:["loadIcon"],src:["loadIcon"],icon:["loadIcon"],ios:["loadIcon"],md:["loadIcon"]}}static get style(){return":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}"}},[1,"ion-icon",{mode:[1025],color:[1],ios:[1],md:[1],flipRtl:[4,"flip-rtl"],name:[513],src:[1],icon:[8],size:[1],lazy:[4],sanitize:[4],svgContent:[32],isVisible:[32]}]),bt=()=>re.L2.isBrowser&&"undefined"!==typeof document&&document.documentElement.getAttribute("mode")||"md",vt=e=>e?{"ion-color":!0,[`ion-color-${e}`]:!0}:null;const yt=function(){if("undefined"===typeof customElements)return;["ion-icon"].forEach(e=>{if("ion-icon"===e)customElements.get(e)||customElements.define(e,gt)})},wt=e.createContext({onIonViewWillEnter:()=>{},ionViewWillEnter:()=>{},onIonViewDidEnter:()=>{},ionViewDidEnter:()=>{},onIonViewWillLeave:()=>{},ionViewWillLeave:()=>{},onIonViewDidLeave:()=>{},ionViewDidLeave:()=>{},cleanupIonViewWillEnter:()=>{},cleanupIonViewDidEnter:()=>{},cleanupIonViewWillLeave:()=>{},cleanupIonViewDidLeave:()=>{}}),$t=class{constructor(){this.ionViewWillEnterCallbacks=[],this.ionViewDidEnterCallbacks=[],this.ionViewWillLeaveCallbacks=[],this.ionViewDidLeaveCallbacks=[],this.ionViewWillEnterDestructorCallbacks=[],this.ionViewDidEnterDestructorCallbacks=[],this.ionViewWillLeaveDestructorCallbacks=[],this.ionViewDidLeaveDestructorCallbacks=[]}onIonViewWillEnter(e){if(e.id){const t=this.ionViewWillEnterCallbacks.findIndex(t=>t.id===e.id);t>-1?this.ionViewWillEnterCallbacks[t]=e:this.ionViewWillEnterCallbacks.push(e)}else this.ionViewWillEnterCallbacks.push(e)}teardownCallback(e,t){const n=t.filter(t=>t.id===e.id);0!==n.length&&(n.forEach(e=>{e&&"function"===typeof e.destructor&&e.destructor()}),t=t.filter(t=>t.id!==e.id))}cleanupIonViewWillEnter(e){this.teardownCallback(e,this.ionViewWillEnterDestructorCallbacks)}cleanupIonViewDidEnter(e){this.teardownCallback(e,this.ionViewDidEnterDestructorCallbacks)}cleanupIonViewWillLeave(e){this.teardownCallback(e,this.ionViewWillLeaveDestructorCallbacks)}cleanupIonViewDidLeave(e){this.teardownCallback(e,this.ionViewDidLeaveDestructorCallbacks)}ionViewWillEnter(){this.ionViewWillEnterCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewWillEnterDestructorCallbacks.push({id:e.id,destructor:t})})}onIonViewDidEnter(e){if(e.id){const t=this.ionViewDidEnterCallbacks.findIndex(t=>t.id===e.id);t>-1?this.ionViewDidEnterCallbacks[t]=e:this.ionViewDidEnterCallbacks.push(e)}else this.ionViewDidEnterCallbacks.push(e)}ionViewDidEnter(){this.ionViewDidEnterCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewDidEnterDestructorCallbacks.push({id:e.id,destructor:t})})}onIonViewWillLeave(e){if(e.id){const t=this.ionViewWillLeaveCallbacks.findIndex(t=>t.id===e.id);t>-1?this.ionViewWillLeaveCallbacks[t]=e:this.ionViewWillLeaveCallbacks.push(e)}else this.ionViewWillLeaveCallbacks.push(e)}ionViewWillLeave(){this.ionViewWillLeaveCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewWillLeaveDestructorCallbacks.push({id:e.id,destructor:t})})}onIonViewDidLeave(e){if(e.id){const t=this.ionViewDidLeaveCallbacks.findIndex(t=>t.id===e.id);t>-1?this.ionViewDidLeaveCallbacks[t]=e:this.ionViewDidLeaveCallbacks.push(e)}else this.ionViewDidLeaveCallbacks.push(e)}ionViewDidLeave(){this.ionViewDidLeaveCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewDidLeaveDestructorCallbacks.push({id:e.id,destructor:t})}),this.componentCanBeDestroyed()}onComponentCanBeDestroyed(e){this.componentCanBeDestroyedCallback=e}componentCanBeDestroyed(){this.componentCanBeDestroyedCallback&&this.componentCanBeDestroyedCallback()}},xt=e.createContext({getIonRedirect:()=>{},getIonRoute:()=>{},getPageManager:()=>{},getStackManager:()=>{},goBack:e=>{"undefined"!==typeof window&&("string"===typeof e?window.location.pathname=e:window.history.back())},navigate:e=>{"undefined"!==typeof window&&(window.location.pathname=e)},hasIonicRouter:()=>!1,routeInfo:void 0,setCurrentTab:()=>{},changeTab:(e,t)=>{"undefined"!==typeof window&&(window.location.pathname=t)},resetTab:(e,t)=>{"undefined"!==typeof window&&(window.location.pathname=t)}}),kt=e=>e.toLowerCase().split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(""),St=e=>e.replace(/([A-Z])/g,e=>`-${e[0].toLowerCase()}`),Et=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e instanceof Element){const o=Ct(e.classList,t,n);""!==o&&(e.className=o),Object.keys(t).forEach(n=>{if("children"!==n&&"style"!==n&&"ref"!==n&&"class"!==n&&"className"!==n&&"forwardedRef"!==n)if(0===n.indexOf("on")&&n[2]===n[2].toUpperCase()){const o=n.substring(2),r=o[0].toLowerCase()+o.substring(1);Tt(r)||_t(e,r,t[n])}else{e[n]=t[n];"string"===typeof t[n]&&e.setAttribute(St(n),t[n])}})}},Ct=(e,t,n)=>{const o=t.className||t.class,r=n.className||n.class,i=Pt(e),a=Pt(o?o.split(" "):[]),s=Pt(r?r.split(" "):[]),l=[];return i.forEach(e=>{a.has(e)?(l.push(e),a.delete(e)):s.has(e)||l.push(e)}),a.forEach(e=>l.push(e)),l.join(" ")},Tt=e=>{if("undefined"===typeof document)return!0;{const t="on"+(e=>"doubleclick"===e?"dblclick":e)(e);let n=t in document;if(!n){const e=document.createElement("div");e.setAttribute(t,"return;"),n="function"===typeof e[t]}return n}},_t=(e,t,n)=>{const o=e.__events||(e.__events={}),r=o[t];r&&e.removeEventListener(t,r),e.addEventListener(t,o[t]=function(e){n&&n.call(this,e)})},Pt=e=>{const t=new Map;return e.forEach(e=>t.set(e,e)),t},Lt=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{((e,t)=>{"function"===typeof e?e(t):null!=e&&(e.current=t)})(t,e)})}},Rt=(t,n,o,r)=>{void 0!==r&&r();const i=kt(t),a=class extends e.Component{constructor(e){super(e),this.setComponentElRef=e=>{this.componentEl=e}}componentDidMount(){this.componentDidUpdate(this.props)}componentDidUpdate(e){Et(this.componentEl,this.props,e)}render(){const n=this.props,{children:r,forwardedRef:i,style:a,className:s,ref:l}=n,c=ne(n,["children","forwardedRef","style","className","ref"]);let u=Object.keys(c).reduce((e,t)=>{const n=c[t];if(0===t.indexOf("on")&&t[2]===t[2].toUpperCase()){const o=t.substring(2).toLowerCase();"undefined"!==typeof document&&Tt(o)&&(e[t]=n)}else{const o=typeof n;"string"!==o&&"boolean"!==o&&"number"!==o||(e[St(t)]=n)}return e},{});o&&(u=o(this.props,u));const d=Object.assign(Object.assign({},u),{ref:Lt(i,this.setComponentElRef),style:a});return(0,e.createElement)(t,d,r)}static get displayName(){return i}};return n&&(a.contextType=n),((t,n)=>{const o=(n,o)=>e.createElement(t,Object.assign({},n,{forwardedRef:o}));return o.displayName=n,e.forwardRef(o)})(a,i)},Nt=Rt("ion-content",void 0,void 0,de),Ot=Rt("ion-header",void 0,void 0,$e),It=Rt("ion-title",void 0,void 0,ze),zt=Rt("ion-toolbar",void 0,void 0,Ae),Dt=(t,n)=>{const o=(n,o)=>e.createElement(t,Object.assign({},n,{forwardedRef:o}));return o.displayName=n,e.forwardRef(o)},At=()=>{if("undefined"!==typeof window){const e=window.Ionic;if(e&&e.config)return e.config}return null},Mt=e.createContext({addOverlay:()=>{},removeOverlay:()=>{}}),Bt=t=>{let{onAddOverlay:n,onRemoveOverlay:o}=t;const[r,i]=(0,e.useState)({}),a=(0,e.useRef)({});(0,e.useEffect)(()=>{n(s),o(l)},[]);const s=(e,t,n)=>{const o=Object.assign({},a.current);o[e]={component:t,containerElement:n},a.current=o,i(o)},l=e=>{const t=Object.assign({},a.current);delete t[e],a.current=t,i(t)},c=Object.keys(r);return e.createElement(e.Fragment,null,c.map(e=>{const t=r[e];return oe.createPortal(t.component,t.containerElement,`overlay-${e}`)}))},Vt=Rt("ion-tab-button",void 0,void 0,nt),jt=Rt("ion-tab-bar",void 0,void 0,Ye),Ht=Rt("ion-router-outlet",void 0,void 0,We),Ft=Rt("ion-app",void 0,void 0,He),Wt=Rt("ion-icon",void 0,void 0,yt),Ut=(()=>class extends e.Component{constructor(e){super(e),this.ionContext={addOverlay:(e,t,n)=>{this.addOverlayCallback&&this.addOverlayCallback(e,t,n)},removeOverlay:e=>{this.removeOverlayCallback&&this.removeOverlayCallback(e)}}}render(){return e.createElement(Mt.Provider,{value:this.ionContext},e.createElement(Ft,Object.assign({},this.props),this.props.children),e.createElement(Bt,{onAddOverlay:e=>{this.addOverlayCallback=e},onRemoveOverlay:e=>{this.removeOverlayCallback=e}}))}static get displayName(){return"IonApp"}})(),qt=e.createContext({registerIonPage:()=>{},isInOutlet:()=>!1});class Qt extends e.PureComponent{constructor(t){super(t),this.ionPageElementRef=e.createRef(),this.stableMergedRefs=Lt(this.ionPageElementRef,this.props.forwardedRef),this.ionViewWillEnterHandler=this.ionViewWillEnterHandler.bind(this),this.ionViewDidEnterHandler=this.ionViewDidEnterHandler.bind(this),this.ionViewWillLeaveHandler=this.ionViewWillLeaveHandler.bind(this),this.ionViewDidLeaveHandler=this.ionViewDidLeaveHandler.bind(this)}componentDidMount(){this.ionPageElementRef.current&&(this.context.isInOutlet()&&this.ionPageElementRef.current.classList.add("ion-page-invisible"),this.context.registerIonPage(this.ionPageElementRef.current,this.props.routeInfo),this.ionPageElementRef.current.addEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionPageElementRef.current.addEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionPageElementRef.current.addEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionPageElementRef.current.addEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}componentWillUnmount(){this.ionPageElementRef.current&&(this.ionPageElementRef.current.removeEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionPageElementRef.current.removeEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionPageElementRef.current.removeEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler))}ionViewWillEnterHandler(){this.ionLifeCycleContext.ionViewWillEnter()}ionViewDidEnterHandler(){this.ionLifeCycleContext.ionViewDidEnter()}ionViewWillLeaveHandler(){this.ionLifeCycleContext.ionViewWillLeave()}ionViewDidLeaveHandler(){this.ionLifeCycleContext.ionViewDidLeave()}render(){const t=this.props,{className:n,children:o,routeInfo:r,forwardedRef:i}=t,a=ne(t,["className","children","routeInfo","forwardedRef"]);return e.createElement(wt.Consumer,null,t=>(this.ionLifeCycleContext=t,e.createElement("div",Object.assign({className:n?`${n} ion-page`:"ion-page",ref:this.stableMergedRefs},a),o)))}static get contextType(){return qt}}class Kt extends e.Component{constructor(e){super(e)}render(){const t=this.props,{className:n,children:o,forwardedRef:r}=t,i=ne(t,["className","children","forwardedRef"]);return this.context.hasIonicRouter()?e.createElement(Qt,Object.assign({className:n?`${n}`:"",routeInfo:this.context.routeInfo,forwardedRef:r},i),o):e.createElement("div",Object.assign({className:n?`ion-page ${n}`:"ion-page",ref:r},i),o)}static get displayName(){return"IonPage"}static get contextType(){return xt}}const Xt=Dt(Kt,"IonPage"),Yt={main:0},Gt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"main";var t;const n=(null!==(t=Yt[e])&&void 0!==t?t:0)+1;return Yt[e]=n,n.toString()},Zt=Rt("ion-nav",void 0,void 0,Oe),Jt=(Dt(t=>{var{children:n,forwardedRef:o}=t,r=ne(t,["children","forwardedRef"]);const[i,a]=(0,e.useState)([]),s=e=>a(t=>[...t,e]),l=e=>a(t=>t.filter(t=>t!==e)),c=(0,e.useMemo)(()=>((e,t)=>{const n=new WeakMap,o=`react-delegate-${Gt()}`;let r=0;return{attachViewToDom:async(t,i,a,s)=>{const l=document.createElement("div");s&&l.classList.add(...s),t.appendChild(l);const c=i(a),u=`${o}-${r++}`,d=(0,oe.createPortal)(c,l,u);return n.set(l,d),e(d),Promise.resolve(l)},removeViewFromDom:(e,o)=>{const r=n.get(o);return r&&t(r),o.remove(),Promise.resolve()}}})(s,l),[]);return e.createElement(Zt,Object.assign({delegate:c,ref:o},r),i)},"IonNav"),e.createContext({activeTab:void 0,selectTab:()=>!1}),"undefined"!==typeof HTMLElement?HTMLElement:class{});class en extends e.Component{constructor(e){super(e),this.outletIsReady=!1,this.ionViewWillEnterHandler=this.ionViewWillEnterHandler.bind(this),this.ionViewDidEnterHandler=this.ionViewDidEnterHandler.bind(this),this.ionViewWillLeaveHandler=this.ionViewWillLeaveHandler.bind(this),this.ionViewDidLeaveHandler=this.ionViewDidLeaveHandler.bind(this)}componentDidMount(){this.ionRouterOutlet&&(this.outletIsReady||(0,ee.c)(this.ionRouterOutlet,()=>{this.outletIsReady=!0,this.context.registerIonPage(this.ionRouterOutlet,this.props.routeInfo)}),this.ionRouterOutlet.addEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionRouterOutlet.addEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionRouterOutlet.addEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionRouterOutlet.addEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}componentWillUnmount(){this.ionRouterOutlet&&(this.ionRouterOutlet.removeEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionRouterOutlet.removeEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionRouterOutlet.removeEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionRouterOutlet.removeEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}ionViewWillEnterHandler(){this.ionLifeCycleContext.ionViewWillEnter()}ionViewDidEnterHandler(){this.ionLifeCycleContext.ionViewDidEnter()}ionViewWillLeaveHandler(){this.ionLifeCycleContext.ionViewWillLeave()}ionViewDidLeaveHandler(){this.ionLifeCycleContext.ionViewDidLeave()}render(){const t=this.props,{StackManager:n,children:o,routeInfo:r}=t,i=ne(t,["StackManager","children","routeInfo"]);return e.createElement(wt.Consumer,null,t=>(this.ionLifeCycleContext=t,e.createElement(n,{routeInfo:r},e.createElement(Ht,Object.assign({setRef:e=>this.ionRouterOutlet=e},i),o))))}static get contextType(){return qt}}class tn extends e.Component{constructor(e){super(e)}render(){const t=this.context.getStackManager(),n=this.props,{children:o,forwardedRef:r}=n,i=ne(n,["children","forwardedRef"]);return this.context.hasIonicRouter()?i.ionPage?e.createElement(en,Object.assign({StackManager:t,routeInfo:this.context.routeInfo},i),o):e.createElement(t,{routeInfo:this.context.routeInfo},e.createElement(Ht,Object.assign({},i,{forwardedRef:r}),o)):e.createElement(Ht,Object.assign({ref:r},this.props),this.props.children)}static get contextType(){return xt}}const nn=Dt(tn,"IonRouterOutlet"),on=(()=>class extends e.Component{constructor(e){super(e),this.handleIonTabButtonClick=this.handleIonTabButtonClick.bind(this)}handleIonTabButtonClick(){this.props.onClick&&this.props.onClick(new CustomEvent("ionTabButtonClick",{detail:{tab:this.props.tab,href:this.props.href,routeOptions:this.props.routerOptions}}))}render(){const t=ne(this.props,["onClick"]);return e.createElement(Vt,Object.assign({onIonTabButtonClick:this.handleIonTabButtonClick},t))}static get displayName(){return"IonTabButton"}})();class rn extends e.PureComponent{constructor(t){super(t),this.setActiveTabOnContext=e=>{};const n={};e.Children.forEach(t.children,e=>{var o,r,i,a;null!=e&&"object"===typeof e&&e.props&&(e.type===on||e.type.isTabButton)&&(n[e.props.tab]={originalHref:e.props.href,currentHref:e.props.href,originalRouteOptions:e.props.href===(null===(o=t.routeInfo)||void 0===o?void 0:o.pathname)?null===(r=t.routeInfo)||void 0===r?void 0:r.routeOptions:void 0,currentRouteOptions:e.props.href===(null===(i=t.routeInfo)||void 0===i?void 0:i.pathname)?null===(a=t.routeInfo)||void 0===a?void 0:a.routeOptions:void 0})}),this.state={tabs:n},this.onTabButtonClick=this.onTabButtonClick.bind(this),this.renderTabButton=this.renderTabButton.bind(this),this.setActiveTabOnContext=this.setActiveTabOnContext.bind(this),this.selectTab=this.selectTab.bind(this)}componentDidMount(){const e=this.state.tabs,t=Object.keys(e).find(t=>{const n=e[t].originalHref;return this.props.routeInfo.pathname.startsWith(n)});t&&this.setState({activeTab:t})}componentDidUpdate(){this.state.activeTab&&this.setActiveTabOnContext(this.state.activeTab)}selectTab(e){const t=this.state.tabs[e];return!!t&&(this.onTabButtonClick(new CustomEvent("ionTabButtonClick",{detail:{href:t.currentHref,tab:e,selected:e===this.state.activeTab,routeOptions:void 0}})),!0)}static getDerivedStateFromProps(t,n){var o,r,i;const a=Object.assign({},n.tabs),s=Object.keys(n.tabs).find(e=>{const o=n.tabs[e].originalHref;return t.routeInfo.pathname.startsWith(o)});e.Children.forEach(t.children,e=>{if(null!=e&&"object"===typeof e&&e.props&&(e.type===on||e.type.isTabButton)){const t=a[e.props.tab];t&&t.originalHref===e.props.href||(a[e.props.tab]={originalHref:e.props.href,currentHref:e.props.href,originalRouteOptions:e.props.routeOptions,currentRouteOptions:e.props.routeOptions})}});const{activeTab:l}=n;if(s&&l){const e=n.tabs[l].currentHref,c=n.tabs[l].currentRouteOptions;s===l&&e===(null===(o=t.routeInfo)||void 0===o?void 0:o.pathname)&&c===(null===(r=t.routeInfo)||void 0===r?void 0:r.routeOptions)||(a[s]={originalHref:a[s].originalHref,currentHref:t.routeInfo.pathname+(t.routeInfo.search||""),originalRouteOptions:a[s].originalRouteOptions,currentRouteOptions:null===(i=t.routeInfo)||void 0===i?void 0:i.routeOptions},"pop"===t.routeInfo.routeAction&&s!==l&&(a[l]={originalHref:a[l].originalHref,currentHref:a[l].originalHref,originalRouteOptions:a[l].originalRouteOptions,currentRouteOptions:a[l].currentRouteOptions}))}return s&&t.onSetCurrentTab(s,t.routeInfo),{activeTab:s,tabs:a}}onTabButtonClick(e,t){const n=this.state.tabs[e.detail.tab],o=n.originalHref,r=e.detail.href,{activeTab:i}=this.state;t&&t(e),i===e.detail.tab?o!==r&&this.context.resetTab(e.detail.tab,o,n.originalRouteOptions):(this.props.onIonTabsWillChange&&this.props.onIonTabsWillChange(new CustomEvent("ionTabWillChange",{detail:{tab:e.detail.tab}})),this.props.onIonTabsDidChange&&this.props.onIonTabsDidChange(new CustomEvent("ionTabDidChange",{detail:{tab:e.detail.tab}})),this.setActiveTabOnContext(e.detail.tab),this.context.changeTab(e.detail.tab,r,e.detail.routeOptions))}renderTabButton(t){return n=>{var o,r;if(null!=n&&n.props&&(n.type===on||n.type.isTabButton)){const i=n.props.tab===t?null===(o=this.props.routeInfo)||void 0===o?void 0:o.pathname:this.state.tabs[n.props.tab].currentHref,a=n.props.tab===t?null===(r=this.props.routeInfo)||void 0===r?void 0:r.routeOptions:this.state.tabs[n.props.tab].currentRouteOptions;return e.cloneElement(n,{href:i,routeOptions:a,onClick:e=>this.onTabButtonClick(e,n.props.onClick)})}return null}}render(){const{activeTab:t}=this.state;return e.createElement(jt,Object.assign({},this.props,{selectedTab:t}),e.Children.map(this.props.children,this.renderTabButton(t)))}static get contextType(){return xt}}const an=e.memo(t=>{var{forwardedRef:n}=t,o=ne(t,["forwardedRef"]);const r=(0,e.useContext)(xt);return e.createElement(rn,Object.assign({ref:n},o,{routeInfo:o.routeInfo||r.routeInfo||{pathname:window.location.pathname},onSetCurrentTab:r.setCurrentTab}),o.children)});Dt(an,"IonTabBar");class sn extends Jt{constructor(){super()}}if("undefined"!==typeof window&&window.customElements){window.customElements.get("ion-tabs")||window.customElements.define("ion-tabs",sn)}class ln extends e.PureComponent{constructor(e){super(e),this.props.name&&console.warn('In Ionic React, you import icons from "ionicons/icons" and set the icon you imported to the "icon" property. Setting the "name" property has no effect.')}render(){var t,n;const o=this.props,{icon:r,ios:i,md:a,mode:s}=o,l=ne(o,["icon","ios","md","mode"]);let c;const u=At(),d=s||(null===u||void 0===u?void 0:u.get("mode"));return c=i||a?"ios"===d?null!==(t=null!==i&&void 0!==i?i:a)&&void 0!==t?t:r:null!==(n=null!==a&&void 0!==a?a:i)&&void 0!==n?n:r:r,e.createElement(Wt,Object.assign({ref:this.props.forwardedRef,icon:c},l),this.props.children)}static get contextType(){return xt}}Dt(ln,"IonIcon");class cn extends e.PureComponent{render(){const t=this.context.getIonRoute();return this.context.hasIonicRouter()&&cn?e.createElement(t,Object.assign({},this.props)):(console.error("You either do not have an Ionic Router package, or your router does not support using <IonRoute>"),null)}static get contextType(){return xt}}e.PureComponent;const un=e.createContext({routeInfo:void 0,push:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},back:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},canGoBack:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},nativeBack:()=>{throw new Error("An Ionic Router is required for IonRouterContext")}});e.PureComponent;const dn=e.createContext({addViewItem:()=>{},canGoBack:()=>{},clearOutlet:()=>{},createViewItem:()=>{},findViewItemByPathname:()=>{},findLeavingViewItemByRouteInfo:()=>{},findViewItemByRouteInfo:()=>{},getChildrenToRender:()=>{},goBack:()=>{},unMountViewItem:()=>{}});class fn extends e.Component{constructor(e){super(e),this.ionLifeCycleContext=new $t,this._isMounted=!1,this.ionLifeCycleContext.onComponentCanBeDestroyed(()=>{this.props.mount||this._isMounted&&this.setState({show:!1},()=>this.props.removeView())}),this.state={show:!0}}componentDidMount(){this._isMounted=!0}componentWillUnmount(){this._isMounted=!1}render(){const{show:t}=this.state;return e.createElement(wt.Provider,{value:this.ionLifeCycleContext},t&&this.props.children)}}class hn{constructor(){this.locationHistory=[],this.tabHistory={}}add(e){"push"===e.routeAction||null==e.routeAction?this._add(e):"pop"===e.routeAction?this._pop(e):"replace"===e.routeAction&&this._replace(e),"root"===e.routeDirection&&(this._clear(),this._add(e))}clearTabStack(e){const t=this._getRouteInfosByKey(e);t&&(t.forEach(e=>{this.locationHistory=this.locationHistory.filter(t=>t.id!==e.id)}),this.tabHistory[e]=[])}update(e){const t=this.locationHistory.findIndex(t=>t.id===e.id);t>-1&&this.locationHistory.splice(t,1,e);const n=this.tabHistory[e.tab||""];if(n){const t=n.findIndex(t=>t.id===e.id);t>-1?n.splice(t,1,e):n.push(e)}else e.tab&&(this.tabHistory[e.tab]=[e])}_add(e){const t=this._getRouteInfosByKey(e.tab);t&&(this._areRoutesEqual(t[t.length-1],e)&&t.pop(),t.push(e)),this.locationHistory.push(e)}_areRoutesEqual(e,t){return!(!e||!t)&&(e.pathname===t.pathname&&e.search===t.search)}_pop(e){const t=this._getRouteInfosByKey(e.tab);t&&(t.pop(),t.pop(),t.push(e)),this.locationHistory.pop(),this.locationHistory.pop(),this.locationHistory.push(e)}_replace(e){const t=this._getRouteInfosByKey(e.tab);t&&t.pop(),this.locationHistory.pop(),this._add(e)}_clear(){Object.keys(this.tabHistory).forEach(e=>this.tabHistory[e]=[]),this.locationHistory=[]}_getRouteInfosByKey(e){let t;return e&&(t=this.tabHistory[e],t||(t=this.tabHistory[e]=[])),t}getFirstRouteInfoForTab(e){const t=this._getRouteInfosByKey(e);if(t)return t[0]}getCurrentRouteInfoForTab(e){const t=this._getRouteInfosByKey(e);if(t)return t[t.length-1]}findLastLocation(e){const t=this._getRouteInfosByKey(e.tab);if(t)for(let n=t.length-2;n>=0;n--){const o=t[n];if(o&&o.pathname===e.pushedByRoute)return o}for(let n=this.locationHistory.length-2;n>=0;n--){const t=this.locationHistory[n];if(t&&t.pathname===e.pushedByRoute)return t}}previous(){return this.locationHistory[this.locationHistory.length-2]||this.locationHistory[this.locationHistory.length-1]}current(){return this.locationHistory[this.locationHistory.length-1]}canGoBack(){return this.locationHistory.length>1}}class pn extends e.PureComponent{constructor(e){super(e),this.ionRouterContextValue={push:(e,t,n,o,r)=>{this.navigate(e,t,n,r,o)},back:e=>{this.goBack(void 0,e)},canGoBack:()=>this.props.locationHistory.canGoBack(),nativeBack:()=>this.props.onNativeBack(),routeInfo:this.props.routeInfo},this.state={goBack:this.goBack.bind(this),hasIonicRouter:()=>!0,navigate:this.navigate.bind(this),getIonRedirect:this.getIonRedirect.bind(this),getIonRoute:this.getIonRoute.bind(this),getStackManager:this.getStackManager.bind(this),getPageManager:this.getPageManager.bind(this),routeInfo:this.props.routeInfo,setCurrentTab:this.props.onSetCurrentTab,changeTab:this.props.onChangeTab,resetTab:this.props.onResetTab}}componentDidMount(){"undefined"!==typeof document&&(this.handleHardwareBackButton=this.handleHardwareBackButton.bind(this),document.addEventListener("ionBackButton",this.handleHardwareBackButton))}componentWillUnmount(){"undefined"!==typeof document&&document.removeEventListener("ionBackButton",this.handleHardwareBackButton)}handleHardwareBackButton(e){e.detail.register(0,e=>{this.nativeGoBack(),e()})}goBack(e,t){this.props.onNavigateBack(e,t)}nativeGoBack(){this.props.onNativeBack()}navigate(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"forward",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"push",o=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0,i=arguments.length>5?arguments[5]:void 0;this.props.onNavigate(e,n,t,o,r,i)}getPageManager(){return Qt}getIonRedirect(){return this.props.ionRedirect}getIonRoute(){return this.props.ionRoute}getStackManager(){return this.props.stackManager}render(){return e.createElement(xt.Provider,{value:Object.assign(Object.assign({},this.state),{routeInfo:this.props.routeInfo})},e.createElement(un.Provider,{value:Object.assign(Object.assign({},this.ionRouterContextValue),{routeInfo:this.props.routeInfo})},this.props.children))}}class mn{constructor(){this.viewStacks={},this.add=this.add.bind(this),this.clear=this.clear.bind(this),this.getViewItemsForOutlet=this.getViewItemsForOutlet.bind(this),this.remove=this.remove.bind(this)}add(e){const{outletId:t}=e;this.viewStacks[t]?this.viewStacks[t].push(e):this.viewStacks[t]=[e]}clear(e){return setTimeout(()=>{delete this.viewStacks[e]},500)}getViewItemsForOutlet(e){return this.viewStacks[e]||[]}remove(e){const{outletId:t}=e,n=this.viewStacks[t];if(n){const o=n.find(t=>t.id===e.id);o&&(o.mount=!1,this.viewStacks[t]=n.filter(e=>e.id!==o.id))}}getStackIds(){return Object.keys(this.viewStacks)}getAllViewItems(){const e=this.getStackIds(),t=[];return e.forEach(e=>{t.push(...this.viewStacks[e])}),t}}class gn extends e.PureComponent{render(){return e.createElement(K,Object.assign({path:this.props.path,exact:this.props.exact,render:this.props.render},void 0!==this.props.computedMatch?{computedMatch:this.props.computedMatch}:{}))}}const bn=e=>{let{pathname:t,componentProps:n}=e;const{exact:o,component:r}=n,i=Q(t,{exact:o,path:n.path||n.from,component:r});return i||!1};class vn extends mn{constructor(){super(),this.createViewItem=this.createViewItem.bind(this),this.findViewItemByRouteInfo=this.findViewItemByRouteInfo.bind(this),this.findLeavingViewItemByRouteInfo=this.findLeavingViewItemByRouteInfo.bind(this),this.getChildrenToRender=this.getChildrenToRender.bind(this),this.findViewItemByPathname=this.findViewItemByPathname.bind(this)}createViewItem(e,t,n,o){const r={id:Gt("viewItem"),outletId:e,ionPageElement:o,reactElement:t,mount:!0,ionRoute:!1};return t.type===cn&&(r.ionRoute=!0,r.disableIonPageManagement=t.props.disableIonPageManagement),r.routeData={match:bn({pathname:n.pathname,componentProps:t.props}),childProps:t.props},r}getChildrenToRender(t,n,o){const r=this.getViewItemsForOutlet(t);e.Children.forEach(n.props.children,e=>{const t=r.find(t=>yn(e,t.routeData.childProps.path||t.routeData.childProps.from));t&&(t.reactElement=e)});return r.map(t=>{let n;if(t.ionRoute&&!t.disableIonPageManagement)n=e.createElement(fn,{key:`view-${t.id}`,mount:t.mount,removeView:()=>this.remove(t)},e.cloneElement(t.reactElement,{computedMatch:t.routeData.match}));else{const r=yn(t.reactElement,o.pathname);n=e.createElement(fn,{key:`view-${t.id}`,mount:t.mount,removeView:()=>this.remove(t)},e.cloneElement(t.reactElement,{computedMatch:t.routeData.match})),!r&&t.routeData.match&&(t.routeData.match=void 0,t.mount=!1)}return n})}findViewItemByRouteInfo(e,t,n){const{viewItem:o,match:r}=this.findViewItemByPath(e.pathname,t);return(void 0===n||!0===n)&&o&&r&&(o.routeData.match=r),o}findLeavingViewItemByRouteInfo(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const{viewItem:o}=this.findViewItemByPath(e.lastPathname,t,n);return o}findViewItemByPathname(e,t){const{viewItem:n}=this.findViewItemByPath(e,t);return n}findViewItemByPath(e,t,n){let o,r,i;if(t)i=this.getViewItemsForOutlet(t),i.some(a),o||i.some(s);else{const e=this.getAllViewItems();e.some(a),o||e.some(s)}return{viewItem:o,match:r};function a(t){var i,a;if(n&&!t.ionRoute)return!1;if(r=bn({pathname:e,componentProps:t.routeData.childProps}),r){const e=r.path.includes(":");if(!e||e&&r.url===(null===(a=null===(i=t.routeData)||void 0===i?void 0:i.match)||void 0===a?void 0:a.url))return o=t,!0}return!1}function s(t){return!t.routeData.childProps.path&&!t.routeData.childProps.from&&(r={path:e,url:e,isExact:!0,params:{}},o=t,!0)}}}function yn(e,t){return bn({pathname:t,componentProps:e.props})}const wn=e=>!e.classList.contains("ion-page-invisible")&&!e.classList.contains("ion-page-hidden");class $n extends e.PureComponent{constructor(e){super(e),this.stackContextValue={registerIonPage:this.registerIonPage.bind(this),isInOutlet:()=>!0},this.pendingPageTransition=!1,this.registerIonPage=this.registerIonPage.bind(this),this.transitionPage=this.transitionPage.bind(this),this.handlePageTransition=this.handlePageTransition.bind(this),this.id=Gt("routerOutlet"),this.prevProps=void 0,this.skipTransition=!1}componentDidMount(){this.clearOutletTimeout&&clearTimeout(this.clearOutletTimeout),this.routerOutletElement&&(this.setupRouterOutlet(this.routerOutletElement),this.handlePageTransition(this.props.routeInfo))}componentDidUpdate(e){const{pathname:t}=this.props.routeInfo,{pathname:n}=e.routeInfo;t!==n?(this.prevProps=e,this.handlePageTransition(this.props.routeInfo)):this.pendingPageTransition&&(this.handlePageTransition(this.props.routeInfo),this.pendingPageTransition=!1)}componentWillUnmount(){this.clearOutletTimeout=this.context.clearOutlet(this.id)}async handlePageTransition(t){var n,o;if(this.routerOutletElement&&this.routerOutletElement.commit){let r=this.context.findViewItemByRouteInfo(t,this.id),i=this.context.findLeavingViewItemByRouteInfo(t,this.id);!i&&t.prevRouteLastPathname&&(i=this.context.findViewItemByPathname(t.prevRouteLastPathname,this.id)),i&&("replace"===t.routeAction?i.mount=!1:"push"!==t.routeAction||"forward"!==t.routeDirection?"none"!==t.routeDirection&&r!==i&&(i.mount=!1):(null===(n=t.routeOptions)||void 0===n?void 0:n.unmount)&&(i.mount=!1));const a=function(t,n){let o;if(e.Children.forEach(t,e=>{bn({pathname:n.pathname,componentProps:e.props})&&(o=e)}),o)return o;return e.Children.forEach(t,e=>{e.props.path||e.props.from||(o=e)}),o}(null===(o=this.ionRouterOutlet)||void 0===o?void 0:o.props.children,t);if(r?r.reactElement=a:a&&(r=this.context.createViewItem(this.id,a,t),this.context.addViewItem(r)),r&&r.ionPageElement){if(r===i&&r.routeData.match.url!==t.pathname)return;if(!i&&this.props.routeInfo.prevRouteLastPathname&&(i=this.context.findViewItemByPathname(this.props.routeInfo.prevRouteLastPathname,this.id)),wn(r.ionPageElement)&&void 0!==i&&!wn(i.ionPageElement))return;this.transitionPage(t,r,i)}else!i||a||r||i.ionPageElement&&(i.ionPageElement.classList.add("ion-page-hidden"),i.ionPageElement.setAttribute("aria-hidden","true"));this.forceUpdate()}else this.pendingPageTransition=!0}registerIonPage(e,t){const n=this.context.findViewItemByRouteInfo(t,this.id);if(n){const t=n.ionPageElement;if(n.ionPageElement=e,n.ionRoute=!0,t===e)return}this.handlePageTransition(t)}async setupRouterOutlet(e){e.swipeHandler={canStart:()=>{const t=At();if(!(t&&t.get("swipeBackEnabled","ios"===e.mode)))return!1;const{routeInfo:n}=this.props,o=this.prevProps&&this.prevProps.routeInfo.pathname===n.pushedByRoute?this.prevProps.routeInfo:{pathname:n.pushedByRoute||""},r=this.context.findViewItemByRouteInfo(o,this.id,!1);return!!r&&r.mount&&r.routeData.match.path!==n.pathname},onStart:async()=>{const{routeInfo:e}=this.props,t=this.prevProps&&this.prevProps.routeInfo.pathname===e.pushedByRoute?this.prevProps.routeInfo:{pathname:e.pushedByRoute||""},n=this.context.findViewItemByRouteInfo(t,this.id,!1),o=this.context.findViewItemByRouteInfo(e,this.id,!1);return n&&o&&await this.transitionPage(e,n,o,"back",!0),Promise.resolve()},onEnd:e=>{if(e)this.skipTransition=!0,this.context.goBack();else{const{routeInfo:e}=this.props,t=this.prevProps&&this.prevProps.routeInfo.pathname===e.pushedByRoute?this.prevProps.routeInfo:{pathname:e.pushedByRoute||""},n=this.context.findViewItemByRouteInfo(t,this.id,!1);if(n!==this.context.findViewItemByRouteInfo(e,this.id,!1)&&void 0!==(null===n||void 0===n?void 0:n.ionPageElement)){const{ionPageElement:e}=n;e.setAttribute("aria-hidden","true"),e.classList.add("ion-page-hidden")}}}}}async transitionPage(e,t,n,o){let r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const i=async(t,n)=>{const o=this.skipTransition;o?this.skipTransition=!1:(t.classList.add("ion-page"),t.classList.add("ion-page-invisible")),await a.commit(t,n,{duration:o||void 0===l?0:void 0,direction:l,showGoBack:!!e.pushedByRoute,progressAnimation:r,animationBuilder:e.routeAnimation})},a=this.routerOutletElement,s="none"===e.routeDirection||"root"===e.routeDirection?void 0:e.routeDirection,l=null!==o&&void 0!==o?o:s;if(t&&t.ionPageElement&&this.routerOutletElement)if(n&&n.ionPageElement&&t===n){if(c=n.reactElement,u=e.pathname,d=!0,bn({pathname:u,componentProps:Object.assign(Object.assign({},c.props),{exact:d})})){const e=function(e){let t;if(t="string"===typeof e?e:e.outerHTML,document){const e=document.createElement("div");e.innerHTML=t,e.style.zIndex="";const n=e.getElementsByTagName("ion-back-button");return n[0]&&n[0].remove(),e.firstChild}}(n.ionPageElement.outerHTML);e&&(this.routerOutletElement.appendChild(e),await i(t.ionPageElement,e),this.routerOutletElement.removeChild(e))}else await i(t.ionPageElement,void 0)}else await i(t.ionPageElement,null===n||void 0===n?void 0:n.ionPageElement),n&&n.ionPageElement&&!r&&(n.ionPageElement.classList.add("ion-page-hidden"),n.ionPageElement.setAttribute("aria-hidden","true"));var c,u,d}render(){const{children:t}=this.props,n=e.Children.only(t);this.ionRouterOutlet=n;const o=this.context.getChildrenToRender(this.id,this.ionRouterOutlet,this.props.routeInfo,()=>{this.forceUpdate()});return e.createElement(qt.Provider,{value:this.stackContextValue},e.cloneElement(n,{ref:e=>{n.props.setRef&&n.props.setRef(e),n.props.forwardedRef&&(n.props.forwardedRef.current=e),this.routerOutletElement=e;const{ref:t}=n;"function"===typeof t&&t(e)}},o))}static get contextType(){return dn}}class xn extends e.PureComponent{constructor(e){super(e),this.exitViewFromOtherOutletHandlers=[],this.locationHistory=new hn,this.viewStack=new vn,this.routeMangerContextState={canGoBack:()=>this.locationHistory.canGoBack(),clearOutlet:this.viewStack.clear,findViewItemByPathname:this.viewStack.findViewItemByPathname,getChildrenToRender:this.viewStack.getChildrenToRender,goBack:()=>this.handleNavigateBack(),createViewItem:this.viewStack.createViewItem,findViewItemByRouteInfo:this.viewStack.findViewItemByRouteInfo,findLeavingViewItemByRouteInfo:this.viewStack.findLeavingViewItemByRouteInfo,addViewItem:this.viewStack.add,unMountViewItem:this.viewStack.remove};const t={id:Gt("routeInfo"),pathname:this.props.location.pathname,search:this.props.location.search};this.locationHistory.add(t),this.handleChangeTab=this.handleChangeTab.bind(this),this.handleResetTab=this.handleResetTab.bind(this),this.handleNativeBack=this.handleNativeBack.bind(this),this.handleNavigate=this.handleNavigate.bind(this),this.handleNavigateBack=this.handleNavigateBack.bind(this),this.props.registerHistoryListener(this.handleHistoryChange.bind(this)),this.handleSetCurrentTab=this.handleSetCurrentTab.bind(this),this.state={routeInfo:t}}handleChangeTab(e,t,n){if(!t)return;const o=this.locationHistory.getCurrentRouteInfoForTab(e),[r,i]=t.split("?");o?(this.incomingRouteParams=Object.assign(Object.assign({},o),{routeAction:"push",routeDirection:"none"}),o.pathname===r?(this.incomingRouteParams.routeOptions=n,this.props.history.push(o.pathname+(o.search||""))):(this.incomingRouteParams.pathname=r,this.incomingRouteParams.search=i?"?"+i:void 0,this.incomingRouteParams.routeOptions=n,this.props.history.push(r+(i?"?"+i:"")))):this.handleNavigate(r,"push","none",void 0,n,e)}handleHistoryChange(e,t){var n,o,r;let i;i=this.incomingRouteParams&&"replace"===this.incomingRouteParams.routeAction?this.locationHistory.previous():this.locationHistory.current();if(i.pathname+i.search!==e.pathname){if(!this.incomingRouteParams){if("REPLACE"===t&&(this.incomingRouteParams={routeAction:"replace",routeDirection:"none",tab:this.currentTab}),"POP"===t){const e=this.locationHistory.current();if(e&&e.pushedByRoute){const t=this.locationHistory.findLastLocation(e);this.incomingRouteParams=Object.assign(Object.assign({},t),{routeAction:"pop",routeDirection:"back"})}else this.incomingRouteParams={routeAction:"pop",routeDirection:"none",tab:this.currentTab}}this.incomingRouteParams||(this.incomingRouteParams={routeAction:"push",routeDirection:(null===(n=e.state)||void 0===n?void 0:n.direction)||"forward",routeOptions:null===(o=e.state)||void 0===o?void 0:o.routerOptions,tab:this.currentTab})}let a;if(null===(r=this.incomingRouteParams)||void 0===r?void 0:r.id)a=Object.assign(Object.assign({},this.incomingRouteParams),{lastPathname:i.pathname}),this.locationHistory.add(a);else{const t="push"===this.incomingRouteParams.routeAction&&"forward"===this.incomingRouteParams.routeDirection;if(a=Object.assign(Object.assign({id:Gt("routeInfo")},this.incomingRouteParams),{lastPathname:i.pathname,pathname:e.pathname,search:e.search,params:this.props.match.params,prevRouteLastPathname:i.lastPathname}),t)a.tab=i.tab,a.pushedByRoute=i.pathname;else if("pop"===a.routeAction){const e=this.locationHistory.findLastLocation(a);a.pushedByRoute=null===e||void 0===e?void 0:e.pushedByRoute}else if("push"===a.routeAction&&a.tab!==i.tab){const e=this.locationHistory.getCurrentRouteInfoForTab(a.tab);a.pushedByRoute=null===e||void 0===e?void 0:e.pushedByRoute}else if("replace"===a.routeAction){const e=this.locationHistory.current(),t=null===e||void 0===e?void 0:e.pushedByRoute,n=void 0!==t&&t!==a.pathname?t:a.pushedByRoute;a.lastPathname=(null===e||void 0===e?void 0:e.pathname)||a.lastPathname,a.prevRouteLastPathname=null===e||void 0===e?void 0:e.lastPathname,a.pushedByRoute=n,a.routeDirection=a.routeDirection||(null===e||void 0===e?void 0:e.routeDirection),a.routeAnimation=a.routeAnimation||(null===e||void 0===e?void 0:e.routeAnimation)}this.locationHistory.add(a)}this.setState({routeInfo:a})}this.incomingRouteParams=void 0}handleNativeBack(){const e=this.props.history;(e.goBack||e.back)()}handleNavigate(e,t,n,o,r,i){this.incomingRouteParams=Object.assign(this.incomingRouteParams||{},{routeAction:t,routeDirection:n,routeOptions:r,routeAnimation:o,tab:i}),"push"===t?this.props.history.push(e):this.props.history.replace(e)}handleNavigateBack(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/",t=arguments.length>1?arguments[1]:void 0;const n=At();e=e||n&&n.get("backButtonDefaultHref");const o=this.locationHistory.current();if(o&&o.pushedByRoute){const n=this.locationHistory.findLastLocation(o);if(n){const e=t||o.routeAnimation;if(this.incomingRouteParams=Object.assign(Object.assign({},n),{routeAction:"pop",routeDirection:"back",routeAnimation:e}),o.lastPathname===o.pushedByRoute||n.pathname===o.pushedByRoute&&""===o.tab&&""===n.tab){const e=this.props.history;(e.goBack||e.back)()}else this.handleNavigate(n.pathname+(n.search||""),"pop","back",e)}else this.handleNavigate(e,"pop","back",t)}else this.handleNavigate(e,"pop","back",t)}handleResetTab(e,t,n){const o=this.locationHistory.getFirstRouteInfoForTab(e);if(o){const e=Object.assign({},o);e.pathname=t,e.routeOptions=n,this.incomingRouteParams=Object.assign(Object.assign({},e),{routeAction:"pop",routeDirection:"back"}),this.props.history.push(e.pathname+(e.search||""))}}handleSetCurrentTab(e){this.currentTab=e;const t=Object.assign({},this.locationHistory.current());t.tab!==e&&(t.tab=e,this.locationHistory.update(t))}render(){return e.createElement(dn.Provider,{value:this.routeMangerContextState},e.createElement(pn,{ionRoute:gn,ionRedirect:{},stackManager:$n,routeInfo:this.state.routeInfo,onNativeBack:this.handleNativeBack,onNavigateBack:this.handleNavigateBack,onNavigate:this.handleNavigate,onSetCurrentTab:this.handleSetCurrentTab,onChangeTab:this.handleChangeTab,onResetTab:this.handleResetTab,locationHistory:this.locationHistory},this.props.children))}}const kn=function(t){var n="withRouter("+(t.displayName||t.name)+")",o=function(n){var o=n.wrappedComponentRef,r=L(n,["wrappedComponentRef"]);return e.createElement(M.Consumer,null,function(n){return n||p(!1),e.createElement(t,s({},r,n,{ref:o}))})};return o.displayName=n,o.WrappedComponent=t,N()(o,t)}(xn);kn.displayName="IonRouter";class Sn extends e.Component{constructor(e){super(e);const{history:t}=e,n=ne(e,["history"]);this.history=t||C(n),this.history.listen(this.handleHistoryChange.bind(this)),this.registerHistoryListener=this.registerHistoryListener.bind(this)}handleHistoryChange(e,t){const n=e.location||e,o=e.action||t;this.historyListenHandler&&this.historyListenHandler(n,o)}registerHistoryListener(e){this.historyListenHandler=e}render(){const t=this.props,{children:n}=t,o=ne(t,["children"]);return e.createElement(B,Object.assign({history:this.history},o),e.createElement(kn,{registerHistoryListener:this.registerHistoryListener},n))}}e.Component;e.Component;var En=n(579);const Cn=()=>(0,En.jsxs)("div",{id:"container",children:[(0,En.jsx)("strong",{children:"Ready to create an app?"}),(0,En.jsxs)("p",{children:["Start with Ionic ",(0,En.jsx)("a",{target:"_blank",rel:"noopener noreferrer",href:"https://ionicframework.com/docs/components",children:"UI Components"})]})]}),Tn=()=>(0,En.jsxs)(Xt,{children:[(0,En.jsx)(Ot,{children:(0,En.jsx)(zt,{children:(0,En.jsx)(It,{children:"Blank"})})}),(0,En.jsxs)(Nt,{fullscreen:!0,children:[(0,En.jsx)(Ot,{collapse:"condense",children:(0,En.jsx)(zt,{children:(0,En.jsx)(It,{size:"large",children:"Blank"})})}),(0,En.jsx)(Cn,{})]})]});!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"undefined"!==typeof document&&document.documentElement.classList.add("ion-ce"),(0,te.i)(Object.assign({},e))}();const _n=()=>(0,En.jsx)(Ut,{children:(0,En.jsx)(Sn,{children:(0,En.jsxs)(nn,{children:[(0,En.jsx)(K,{exact:!0,path:"/home",children:(0,En.jsx)(Tn,{})}),(0,En.jsx)(K,{exact:!0,path:"/",children:(0,En.jsx)(W,{to:"/home"})})]})})});Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));const Pn=e=>{e&&e instanceof Function&&n.e(422).then(n.bind(n,422)).then(t=>{let{getCLS:n,getFID:o,getFCP:r,getLCP:i,getTTFB:a}=t;n(e),o(e),r(e),i(e),a(e)})},Ln=document.getElementById("root");(0,t.H)(Ln).render((0,En.jsx)(e.StrictMode,{children:(0,En.jsx)(_n,{})})),"serviceWorker"in navigator&&navigator.serviceWorker.ready.then(e=>{e.unregister()}).catch(e=>{console.error(e.message)}),Pn()})()})();
//# sourceMappingURL=main.889aa13c.js.map