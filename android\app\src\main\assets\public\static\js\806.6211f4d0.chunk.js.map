{"version": 3, "file": "static/js/806.6211f4d0.chunk.js", "mappings": ";2LA8GA,MAAMA,EACFC,WAAAA,CAAYC,EAAMC,EAAIC,EAAMC,EAAUC,GAClCC,KAAKJ,GAAKA,EACVI,KAAKH,KAAOA,EACZG,KAAKD,cAAgBA,EACrBC,KAAKF,SAAsB,IAAXA,EAAqBF,EACrCI,KAAKL,KAAOA,CAChB,CACAM,QAAAA,GACI,QAAKD,KAAKL,MAGHK,KAAKL,KAAKM,SAASD,KAAKH,KACnC,CACAK,KAAAA,GACI,QAAKF,KAAKL,MAGHK,KAAKL,KAAKO,MAAMF,KAAKH,KAAMG,KAAKJ,GAAII,KAAKF,SACpD,CACAK,OAAAA,GACI,IAAKH,KAAKL,KACN,OAAO,EAEX,MAAMS,EAAWJ,KAAKL,KAAKQ,QAAQH,KAAKH,KAAMG,KAAKJ,GAAII,KAAKF,UAI5D,OAHIM,GAAYJ,KAAKD,eACjBC,KAAKL,KAAKI,cAAcC,KAAKJ,IAE1BQ,CACX,CACAC,OAAAA,GACQL,KAAKL,OACLK,KAAKL,KAAKU,QAAQL,KAAKJ,IACnBI,KAAKD,eACLC,KAAKL,KAAKW,aAAaN,KAAKJ,IAGxC,CACAW,OAAAA,GACIP,KAAKK,UACLL,KAAKL,UAAOa,CAChB,EAEJ,MAAMC,EACFf,WAAAA,CAAYC,EAAMC,EAAIc,EAASX,GAC3BC,KAAKJ,GAAKA,EACVI,KAAKU,QAAUA,EACfV,KAAKD,cAAgBA,EACrBC,KAAKL,KAAOA,CAChB,CACAgB,KAAAA,GACI,GAAKX,KAAKL,KAAV,CAGA,GAAIK,KAAKU,QACL,IAAK,MAAME,KAAWZ,KAAKU,QACvBV,KAAKL,KAAKkB,eAAeD,EAASZ,KAAKJ,IAG3CI,KAAKD,eACLC,KAAKL,KAAKI,cAAcC,KAAKJ,GAPjC,CASJ,CACAkB,OAAAA,GACI,GAAKd,KAAKL,KAAV,CAGA,GAAIK,KAAKU,QACL,IAAK,MAAME,KAAWZ,KAAKU,QACvBV,KAAKL,KAAKoB,cAAcH,EAASZ,KAAKJ,IAG1CI,KAAKD,eACLC,KAAKL,KAAKW,aAAaN,KAAKJ,GAPhC,CASJ,CACAW,OAAAA,GACIP,KAAKc,UACLd,KAAKL,UAAOa,CAChB,EAEJ,MAAMQ,EAAqB,qBACrBC,EAAqB,IA7L3B,MACIvB,WAAAA,GACIM,KAAKkB,UAAY,EACjBlB,KAAKmB,eAAiB,IAAIC,IAC1BpB,KAAKqB,iBAAmB,IAAID,IAC5BpB,KAAKsB,eAAiB,IAAIC,GAC9B,CAIAC,aAAAA,CAAcC,GACV,IAAIC,EACJ,OAAO,IAAIjC,EAAgBO,KAAMA,KAAK2B,QAASF,EAAO5B,KAAiC,QAA1B6B,EAAKD,EAAO3B,gBAA6B,IAAP4B,EAAgBA,EAAK,IAAKD,EAAO1B,cACpI,CAIA6B,aAAAA,GAAyB,IAAXC,EAAIC,UAAAC,OAAA,QAAAvB,IAAAsB,UAAA,GAAAA,UAAA,GAAG,CAAC,EAClB,OAAO,IAAIrB,EAAgBT,KAAMA,KAAK2B,QAASE,EAAKnB,UAAWmB,EAAK9B,cACxE,CACAG,KAAAA,CAAM8B,EAAapC,EAAIE,GACnB,OAAKE,KAAKC,SAAS+B,IAInBhC,KAAKmB,eAAec,IAAIrC,EAAIE,IACrB,IAJHE,KAAKmB,eAAee,OAAOtC,IACpB,EAIf,CACAO,OAAAA,CAAQ6B,EAAapC,EAAIE,GACrB,IAAKE,KAAKE,MAAM8B,EAAapC,EAAIE,GAC7B,OAAO,EAEX,MAAMqB,EAAiBnB,KAAKmB,eAC5B,IAAIgB,GAAe,IAInB,GAHAhB,EAAeiB,QAASC,IACpBF,EAAcG,KAAKC,IAAIJ,EAAaE,KAEpCF,IAAgBrC,EAAU,CAC1BE,KAAKwC,WAAa5C,EAClBuB,EAAesB,QACf,MAAMC,EAAQ,IAAIC,YAAY,qBAAsB,CAAEC,OAAQ,CAAEZ,iBAEhE,OADAa,SAASC,cAAcJ,IAChB,CACX,CAEA,OADAvB,EAAee,OAAOtC,IACf,CACX,CACAS,OAAAA,CAAQT,GACJI,KAAKmB,eAAee,OAAOtC,GACvBI,KAAKwC,aAAe5C,IACpBI,KAAKwC,gBAAahC,EAE1B,CACAK,cAAAA,CAAemB,EAAapC,GACxB,IAAIqC,EAAMjC,KAAKqB,iBAAiB0B,IAAIf,QACxBxB,IAARyB,IACAA,EAAM,IAAIV,IACVvB,KAAKqB,iBAAiBY,IAAID,EAAaC,IAE3CA,EAAIe,IAAIpD,EACZ,CACAmB,aAAAA,CAAciB,EAAapC,GACvB,MAAMqC,EAAMjC,KAAKqB,iBAAiB0B,IAAIf,QAC1BxB,IAARyB,GACAA,EAAIC,OAAOtC,EAEnB,CACAG,aAAAA,CAAcH,GACVI,KAAKsB,eAAe0B,IAAIpD,GACS,IAA7BI,KAAKsB,eAAe2B,MACpBJ,SAASK,KAAKC,UAAUH,IAAIhC,EAEpC,CACAV,YAAAA,CAAaV,GACTI,KAAKsB,eAAeY,OAAOtC,GACM,IAA7BI,KAAKsB,eAAe2B,MACpBJ,SAASK,KAAKC,UAAUC,OAAOpC,EAEvC,CACAf,QAAAA,CAAS+B,GACL,YAAwBxB,IAApBR,KAAKwC,aAILxC,KAAKqD,WAAWrB,EAIxB,CACAsB,UAAAA,GACI,YAA2B9C,IAApBR,KAAKwC,UAChB,CACAe,gBAAAA,GACI,OAAOvD,KAAKsB,eAAe2B,KAAO,CACtC,CACAI,UAAAA,CAAWrB,GACP,MAAMwB,EAAWxD,KAAKqB,iBAAiB0B,IAAIf,GAC3C,SAAIwB,GAAYA,EAASP,KAAO,EAIpC,CACAtB,KAAAA,GAEI,OADA3B,KAAKkB,YACElB,KAAKkB,SAChB,GCtGEuC,EAAmBA,CAACC,EAC1BC,EAAWC,EAAU/B,KAGjB,MAAMgC,EAAeC,EAAgBJ,GAC/B,CACEvD,UAAW0B,EAAK1B,QAChB4D,UAAWlC,EAAKkC,WAEhBlC,EAAK1B,QACb,IAAI6C,EACAI,EAUJ,OATIM,EAAoC,iCACpCV,EAAM,kCACNI,EAAS,uCAGTJ,EAAM,mBACNI,EAAS,uBAEbM,EAAGV,GAAKW,EAAWC,EAAUC,GACtB,KACHH,EAAGN,GAAQO,EAAWC,EAAUC,KAGlCC,EAAmBE,IACrB,QAAkBxD,IAAdyD,EACA,IACI,MAAMpC,EAAOqC,OAAOC,eAAe,CAAC,EAAG,UAAW,CAC9CpB,IAAKA,KACDkB,GAAY,KAGpBD,EAAKP,iBAAiB,WAAY,OAE/B5B,EACP,CACA,MAAOuC,GACHH,GAAY,CAChB,CAEJ,QAASA,GAEb,IAAIA,EAEJ,MAoHMI,EAAeL,GACVA,aAAgBM,SAAWN,EAAOA,EAAKO,cAqD5C/C,EAAiBC,IACnB,IAAI+C,GAAiB,EACjBC,GAAgB,EAChBC,GAAgB,EAChBC,GAAe,EACnB,MAAMC,EAAcV,OAAOW,OAAO,CAAE9E,eAAe,EAAO+E,UAAW,IAAKC,gBAAiB,EAAGhB,SAAS,EAAMiB,SAAU,GAAIC,UAAW,IAAMxD,GACtIxB,EAAW2E,EAAY3E,SACvBiF,EAAcN,EAAYM,YAC1BC,EAAUP,EAAYO,QACtBC,EAAQR,EAAYQ,MACpBC,EAAcT,EAAYS,YAC1BC,EAASV,EAAYU,OACrBL,EAAYL,EAAYK,UACxBlB,EAAUa,EAAYb,QACtBwB,EAAcX,EAAYW,YAC1B3C,EAAS,CACX4C,KAAM,MACNC,OAAQ,EACRC,OAAQ,EACRC,UAAW,EACXC,SAAU,EACVC,SAAU,EACVC,UAAW,EACXC,UAAW,EACXC,OAAQ,EACRC,OAAQ,EACRC,YAAa,EACbxD,WAAOlC,EACP2F,UAAM3F,GAEJ4F,EAhFkBC,EAACvB,EAAWwB,EAAQtB,KAC5C,MAAMuB,EAAUvB,GAAY1C,KAAKkE,GAAK,KAChCC,EAAuB,MAAd3B,EACT4B,EAAYpE,KAAKqE,IAAIJ,GACrBtB,EAAYqB,EAASA,EAC3B,IAAIb,EAAS,EACTC,EAAS,EACTkB,GAAQ,EACRC,EAAQ,EACZ,MAAO,CACH3G,KAAAA,CAAM4G,EAAGC,GACLtB,EAASqB,EACTpB,EAASqB,EACTF,EAAQ,EACRD,GAAQ,CACZ,EACAI,MAAAA,CAAOF,EAAGC,GACN,IAAKH,EACD,OAAO,EAEX,MAAMZ,EAASc,EAAIrB,EACbQ,EAASc,EAAIrB,EACbuB,EAAWjB,EAASA,EAASC,EAASA,EAC5C,GAAIgB,EAAWhC,EACX,OAAO,EAEX,MAAMiC,EAAa5E,KAAK6E,KAAKF,GACvBG,GAAUX,EAAST,EAASC,GAAUiB,EAW5C,OATIL,EADAO,EAASV,EACD,EAEHU,GAAUV,GACN,EAGD,EAEZE,GAAQ,GACD,CACX,EACAS,UAASA,IACY,IAAVR,EAEXS,aAAYA,IACDT,IAoCHR,CAAoBzB,EAAYE,UAAWF,EAAYK,UAAWL,EAAYI,UACpFpE,EAAUK,EAAmBO,cAAc,CAC7C3B,KAAM4B,EAAOO,YACblC,SAAU2B,EAAOsD,gBACjBhF,cAAe0B,EAAO1B,gBAiDpBwH,EAAaA,KAGV/C,IAGLG,GAAe,EACXW,GACAA,EAAO1C,KAGT4E,EAAkBA,MACf5G,EAAQT,YAGbqE,GAAiB,EACjBE,GAAgB,EAOhB9B,EAAO6C,OAAS7C,EAAOgD,SACvBhD,EAAO8C,OAAS9C,EAAOiD,SACvBjD,EAAO+C,UAAY/C,EAAOsD,YACtBhB,EACAA,EAAYtC,GAAQ6E,KAAKC,GAGzBA,KAEG,GAULA,EAAcA,KACZnC,GATkBoC,MACtB,GAAwB,qBAAb9E,SAA0B,CACjC,MAAM+E,EAAgB/E,SAAS+E,eACT,OAAlBA,QAA4C,IAAlBA,OAA2B,EAASA,EAAcC,OAC5ED,EAAcC,MAEtB,GAIIF,GAEAxC,GACAA,EAAQvC,GAEZ8B,GAAgB,GAEdoD,EAAQA,KACVtD,GAAiB,EACjBC,GAAgB,EAChBE,GAAe,EACfD,GAAgB,EAChB9D,EAAQP,WAGN0H,EAAaC,IACf,MAAMC,EAAiBzD,EACjB0D,EAAmBxD,EACzBoD,IACKI,IAGLC,EAAgBvF,EAAQoF,GAEpBC,EACI7C,GACAA,EAAMxC,GAKVyC,GACAA,EAAYzC,KAGdwF,EA1UkBC,EAAC3E,EAAI4E,EAAaC,EAAaR,EAAWS,KAClE,IAAIC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAiB,EACrB,MAAMC,EAAoBjB,IACtBgB,EAAiBE,KAAKC,MAZX,IAaNb,EAAYN,MAGZU,GAAeH,IAChBG,EAAcjF,EAAiBC,EAAI,YAAa6E,EAAaC,IAW5DG,IACDA,EAAalF,EAAiBuE,EAAGoB,OAAQ,WAAYC,EAAgBb,IAEpEI,IACDA,EAAgBnF,EAAiBuE,EAAGoB,OAAQ,cAAeC,EAAgBb,MAG7Ec,EAAmBtB,IACjBgB,EAAiBE,KAAKC,OAGrBb,EAAYN,MAGZc,GAAeP,IAChBO,EAAcrF,EAAiBY,EAAYX,GAAK,YAAa6E,EAAaC,IAEzEO,IACDA,EAAYtF,EAAiBY,EAAYX,GAAK,UAAW6F,EAAef,MAG1Ea,EAAkBrB,IACpBwB,IACIzB,GACAA,EAAUC,IAGZuB,EAAiBvB,IACnByB,IACI1B,GACAA,EAAUC,IAGZwB,EAAYA,KACVd,GACAA,IAEAC,GACAA,IAEAC,GACAA,IAEJF,EAAcC,EAAaC,OAAgBpI,GAEzCiJ,EAAYA,KACVX,GACAA,IAEAC,GACAA,IAEJD,EAAcC,OAAYvI,GAExBkJ,EAAOA,KACTF,IACAC,KAEEE,EAAS,WAAU7H,UAAAC,OAAA,QAAAvB,IAAAsB,UAAA,KAAAA,UAAA,IAEb2G,GACAA,IAEAI,GACAA,IAEJJ,EAAeI,OAAerI,EAC9BkJ,MAGKjB,IACDA,EAAehF,EAAiBC,EAAI,aAAcuF,EAAkBT,IAEnEK,IACDA,EAAepF,EAAiBC,EAAI,YAAa4F,EAAiBd,IAG9E,EAKA,MAAO,CACHmB,SACAD,OACAnJ,QAPYA,KACZoJ,GAAO,GACP5B,EAAYQ,EAAcD,OAAc9H,KAgOtB6H,CAAoBzD,EAAYlB,GA9HjCsE,IACjB,MAAM4B,EAAYT,EAAInB,GACtB,QAAIvD,IAAkBC,KAGtBmF,EAAa7B,EAAIpF,GACjBA,EAAO6C,OAAS7C,EAAOgD,SACvBhD,EAAO8C,OAAS9C,EAAOiD,SACvBjD,EAAO+C,UAAY/C,EAAOsD,YAAc0D,EACxChH,EAAOkD,UAAYlD,EAAOmD,UAAYnD,EAAOoD,OAASpD,EAAOqD,OAAS,EACtErD,EAAOF,MAAQsF,IAEX/H,IAAiC,IAArBA,EAAS2C,MAIzBhC,EAAQP,YAEHO,EAAQV,UAGbuE,GAAgB,EACE,IAAdQ,EACOuC,KAEXpB,EAAIlG,MAAM0C,EAAO6C,OAAQ7C,EAAO8C,SACzB,OAEUsC,IAGbxD,GACKG,GAAgBD,IACjBC,GAAe,EACfwD,EAAgBvF,EAAQoF,GACxB8B,sBAAsBvC,KAK9BY,EAAgBvF,EAAQoF,GACpB5B,EAAIY,OAAOpE,EAAOgD,SAAUhD,EAAOiD,YAC9BO,EAAIiB,aAAgBG,KACrBuC,OAmFwEhC,EAAW,CAC3F5H,SAAS,EACT4D,YAEEgG,EAAeA,KACjBjC,IACAM,EAAcsB,OACVrE,GACAA,EAAYzC,IAGpB,MAAO,CACH+G,MAAAA,GAAsB,IAAfA,IAAM7H,UAAAC,OAAA,QAAAvB,IAAAsB,UAAA,KAAAA,UAAA,GACJ6H,IACGnF,GACAuD,OAAUvH,GAEdsH,KAEJM,EAAcuB,OAAOA,EACzB,EACApJ,OAAAA,GACIK,EAAQL,UACR6H,EAAc7H,SAClB,IAGF4H,EAAkBA,CAACvF,EAAQoF,KAC7B,IAAKA,EACD,OAEJ,MAAMgC,EAAQpH,EAAOgD,SACfqE,EAAQrH,EAAOiD,SACfqE,EAAQtH,EAAOsD,YACrB2D,EAAa7B,EAAIpF,GACjB,MAAMgD,EAAWhD,EAAOgD,SAClBC,EAAWjD,EAAOiD,SAElBsE,GADavH,EAAOsD,YAAciD,EAAInB,IACdkC,EAC9B,GAAIC,EAAY,GAAKA,EAAY,IAAK,CAClC,MAAMrE,GAAaF,EAAWoE,GAASG,EACjCpE,GAAaF,EAAWoE,GAASE,EACvCvH,EAAOkD,UAAwB,GAAZA,EAAqC,GAAnBlD,EAAOkD,UAC5ClD,EAAOmD,UAAwB,GAAZA,EAAqC,GAAnBnD,EAAOmD,SAChD,CACAnD,EAAOoD,OAASJ,EAAWhD,EAAO6C,OAClC7C,EAAOqD,OAASJ,EAAWjD,EAAO8C,OAClC9C,EAAOF,MAAQsF,GAEb6B,EAAeA,CAAC7B,EAAIpF,KAGtB,IAAIkE,EAAI,EACJC,EAAI,EACR,GAAIiB,EAAI,CACJ,MAAMoC,EAAiBpC,EAAGoC,eAC1B,GAAIA,GAAkBA,EAAerI,OAAS,EAAG,CAC7C,MAAMsI,EAAQD,EAAe,GAC7BtD,EAAIuD,EAAMC,QACVvD,EAAIsD,EAAME,OACd,WACsB/J,IAAbwH,EAAGwC,QACR1D,EAAIkB,EAAGwC,MACPzD,EAAIiB,EAAGyC,MAEf,CACA7H,EAAOgD,SAAWkB,EAClBlE,EAAOiD,SAAWkB,GAEhBoC,EAAOnB,GACFA,EAAG4B,WAAaV,KAAKC,MC9b1BuB,EAAyBA,CAAChH,EAAIiH,EAAiBC,EAAgBC,EAAeC,KAChF,MAAMC,EAAMrH,EAAGa,cAAcyG,YAC7B,IAAIC,GAAMC,EAAAA,EAAAA,GAAMxH,GAMhB,MAQMyH,EAAavI,GACRqI,GAAOrI,EAAOoD,OAASpD,EAAOoD,OAqCzC,OAAOxE,EAAc,CACjBkC,KACA1B,YAAa,eAKb+C,gBAAiB,IACjBE,UAAW,GACXhF,SAzCc2C,IAMdqI,GAAMC,EAAAA,EAAAA,GAAMxH,GApBEd,KACd,MACM,OAAE6C,GAAW7C,EACnB,OAAIqI,EACOxF,GAAUsF,EAAIK,WAHP,GAKX3F,GALW,IAoBX4F,CAASzI,IAAW+H,KAmC3BxF,QAASyF,EACTtF,OAlCY1C,IAEZ,MACM0I,EADQH,EAAUvI,GACEmI,EAAIK,WAC9BP,EAAcS,IA+BdlG,MA7BWxC,IAEX,MAAM2I,EAAQJ,EAAUvI,GAClB4I,EAAQT,EAAIK,WACZE,EAAYC,EAAQC,EACpBC,EAvBY7I,IACXqI,GAAOrI,EAAOkD,UAAYlD,EAAOkD,UAsBvB4F,CAAa9I,GAExB+I,EAAiBF,GAAY,IAAMA,EAAW,IAAOF,EADjDC,EAAQ,GAGZI,GADUD,EAAiB,EAAIL,EAAYA,GACfE,EAClC,IAAIK,EAAU,EACd,GAAID,EAAkB,EAAG,CACrB,MAAME,EAAMF,EAAkBtJ,KAAKyJ,IAAIN,GACvCI,EAAUvJ,KAAK0J,IAAIF,EAAK,IAC5B,CACAhB,EAAaa,EAAgBL,GAAa,EAAI,KAAOW,EAAAA,EAAAA,GAAM,EAAGX,EAAW,OAASO,M", "sources": ["../node_modules/@ionic/core/components/gesture-controller.js", "../node_modules/@ionic/core/components/index3.js", "../node_modules/@ionic/core/components/swipe-back.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nclass GestureController {\n    constructor() {\n        this.gestureId = 0;\n        this.requestedStart = new Map();\n        this.disabledGestures = new Map();\n        this.disabledScroll = new Set();\n    }\n    /**\n     * Creates a gesture delegate based on the GestureConfig passed\n     */\n    createGesture(config) {\n        var _a;\n        return new GestureDelegate(this, this.newID(), config.name, (_a = config.priority) !== null && _a !== void 0 ? _a : 0, !!config.disableScroll);\n    }\n    /**\n     * Creates a blocker that will block any other gesture events from firing. Set in the ion-gesture component.\n     */\n    createBlocker(opts = {}) {\n        return new BlockerDelegate(this, this.newID(), opts.disable, !!opts.disableScroll);\n    }\n    start(gestureName, id, priority) {\n        if (!this.canStart(gestureName)) {\n            this.requestedStart.delete(id);\n            return false;\n        }\n        this.requestedStart.set(id, priority);\n        return true;\n    }\n    capture(gestureName, id, priority) {\n        if (!this.start(gestureName, id, priority)) {\n            return false;\n        }\n        const requestedStart = this.requestedStart;\n        let maxPriority = -10000;\n        requestedStart.forEach((value) => {\n            maxPriority = Math.max(maxPriority, value);\n        });\n        if (maxPriority === priority) {\n            this.capturedId = id;\n            requestedStart.clear();\n            const event = new CustomEvent('ionGestureCaptured', { detail: { gestureName } });\n            document.dispatchEvent(event);\n            return true;\n        }\n        requestedStart.delete(id);\n        return false;\n    }\n    release(id) {\n        this.requestedStart.delete(id);\n        if (this.capturedId === id) {\n            this.capturedId = undefined;\n        }\n    }\n    disableGesture(gestureName, id) {\n        let set = this.disabledGestures.get(gestureName);\n        if (set === undefined) {\n            set = new Set();\n            this.disabledGestures.set(gestureName, set);\n        }\n        set.add(id);\n    }\n    enableGesture(gestureName, id) {\n        const set = this.disabledGestures.get(gestureName);\n        if (set !== undefined) {\n            set.delete(id);\n        }\n    }\n    disableScroll(id) {\n        this.disabledScroll.add(id);\n        if (this.disabledScroll.size === 1) {\n            document.body.classList.add(BACKDROP_NO_SCROLL);\n        }\n    }\n    enableScroll(id) {\n        this.disabledScroll.delete(id);\n        if (this.disabledScroll.size === 0) {\n            document.body.classList.remove(BACKDROP_NO_SCROLL);\n        }\n    }\n    canStart(gestureName) {\n        if (this.capturedId !== undefined) {\n            // a gesture already captured\n            return false;\n        }\n        if (this.isDisabled(gestureName)) {\n            return false;\n        }\n        return true;\n    }\n    isCaptured() {\n        return this.capturedId !== undefined;\n    }\n    isScrollDisabled() {\n        return this.disabledScroll.size > 0;\n    }\n    isDisabled(gestureName) {\n        const disabled = this.disabledGestures.get(gestureName);\n        if (disabled && disabled.size > 0) {\n            return true;\n        }\n        return false;\n    }\n    newID() {\n        this.gestureId++;\n        return this.gestureId;\n    }\n}\nclass GestureDelegate {\n    constructor(ctrl, id, name, priority, disableScroll) {\n        this.id = id;\n        this.name = name;\n        this.disableScroll = disableScroll;\n        this.priority = priority * 1000000 + id;\n        this.ctrl = ctrl;\n    }\n    canStart() {\n        if (!this.ctrl) {\n            return false;\n        }\n        return this.ctrl.canStart(this.name);\n    }\n    start() {\n        if (!this.ctrl) {\n            return false;\n        }\n        return this.ctrl.start(this.name, this.id, this.priority);\n    }\n    capture() {\n        if (!this.ctrl) {\n            return false;\n        }\n        const captured = this.ctrl.capture(this.name, this.id, this.priority);\n        if (captured && this.disableScroll) {\n            this.ctrl.disableScroll(this.id);\n        }\n        return captured;\n    }\n    release() {\n        if (this.ctrl) {\n            this.ctrl.release(this.id);\n            if (this.disableScroll) {\n                this.ctrl.enableScroll(this.id);\n            }\n        }\n    }\n    destroy() {\n        this.release();\n        this.ctrl = undefined;\n    }\n}\nclass BlockerDelegate {\n    constructor(ctrl, id, disable, disableScroll) {\n        this.id = id;\n        this.disable = disable;\n        this.disableScroll = disableScroll;\n        this.ctrl = ctrl;\n    }\n    block() {\n        if (!this.ctrl) {\n            return;\n        }\n        if (this.disable) {\n            for (const gesture of this.disable) {\n                this.ctrl.disableGesture(gesture, this.id);\n            }\n        }\n        if (this.disableScroll) {\n            this.ctrl.disableScroll(this.id);\n        }\n    }\n    unblock() {\n        if (!this.ctrl) {\n            return;\n        }\n        if (this.disable) {\n            for (const gesture of this.disable) {\n                this.ctrl.enableGesture(gesture, this.id);\n            }\n        }\n        if (this.disableScroll) {\n            this.ctrl.enableScroll(this.id);\n        }\n    }\n    destroy() {\n        this.unblock();\n        this.ctrl = undefined;\n    }\n}\nconst BACKDROP_NO_SCROLL = 'backdrop-no-scroll';\nconst GESTURE_CONTROLLER = new GestureController();\n\nexport { GESTURE_CONTROLLER as G };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { G as GESTURE_CONTROLLER } from './gesture-controller.js';\nexport { G as GESTURE_CONTROLLER } from './gesture-controller.js';\n\nconst addEventListener = (el, // TODO(FW-2832): type\neventName, callback, opts) => {\n    // use event listener options when supported\n    // otherwise it's just a boolean for the \"capture\" arg\n    const listenerOpts = supportsPassive(el)\n        ? {\n            capture: !!opts.capture,\n            passive: !!opts.passive,\n        }\n        : !!opts.capture;\n    let add;\n    let remove;\n    if (el['__zone_symbol__addEventListener']) {\n        add = '__zone_symbol__addEventListener';\n        remove = '__zone_symbol__removeEventListener';\n    }\n    else {\n        add = 'addEventListener';\n        remove = 'removeEventListener';\n    }\n    el[add](eventName, callback, listenerOpts);\n    return () => {\n        el[remove](eventName, callback, listenerOpts);\n    };\n};\nconst supportsPassive = (node) => {\n    if (_sPassive === undefined) {\n        try {\n            const opts = Object.defineProperty({}, 'passive', {\n                get: () => {\n                    _sPassive = true;\n                },\n            });\n            node.addEventListener('optsTest', () => {\n                return;\n            }, opts);\n        }\n        catch (e) {\n            _sPassive = false;\n        }\n    }\n    return !!_sPassive;\n};\nlet _sPassive;\n\nconst MOUSE_WAIT = 2000;\n// TODO(FW-2832): types\nconst createPointerEvents = (el, pointerDown, pointerMove, pointerUp, options) => {\n    let rmTouchStart;\n    let rmTouchMove;\n    let rmTouchEnd;\n    let rmTouchCancel;\n    let rmMouseStart;\n    let rmMouseMove;\n    let rmMouseUp;\n    let lastTouchEvent = 0;\n    const handleTouchStart = (ev) => {\n        lastTouchEvent = Date.now() + MOUSE_WAIT;\n        if (!pointerDown(ev)) {\n            return;\n        }\n        if (!rmTouchMove && pointerMove) {\n            rmTouchMove = addEventListener(el, 'touchmove', pointerMove, options);\n        }\n        /**\n         * Events are dispatched on the element that is tapped and bubble up to\n         * the reference element in the gesture. In the event that the element this\n         * event was first dispatched on is removed from the DOM, the event will no\n         * longer bubble up to our reference element. This leaves the gesture in an\n         * unusable state. To account for this, the touchend and touchcancel listeners\n         * should be added to the event target so that they still fire even if the target\n         * is removed from the DOM.\n         */\n        if (!rmTouchEnd) {\n            rmTouchEnd = addEventListener(ev.target, 'touchend', handleTouchEnd, options);\n        }\n        if (!rmTouchCancel) {\n            rmTouchCancel = addEventListener(ev.target, 'touchcancel', handleTouchEnd, options);\n        }\n    };\n    const handleMouseDown = (ev) => {\n        if (lastTouchEvent > Date.now()) {\n            return;\n        }\n        if (!pointerDown(ev)) {\n            return;\n        }\n        if (!rmMouseMove && pointerMove) {\n            rmMouseMove = addEventListener(getDocument(el), 'mousemove', pointerMove, options);\n        }\n        if (!rmMouseUp) {\n            rmMouseUp = addEventListener(getDocument(el), 'mouseup', handleMouseUp, options);\n        }\n    };\n    const handleTouchEnd = (ev) => {\n        stopTouch();\n        if (pointerUp) {\n            pointerUp(ev);\n        }\n    };\n    const handleMouseUp = (ev) => {\n        stopMouse();\n        if (pointerUp) {\n            pointerUp(ev);\n        }\n    };\n    const stopTouch = () => {\n        if (rmTouchMove) {\n            rmTouchMove();\n        }\n        if (rmTouchEnd) {\n            rmTouchEnd();\n        }\n        if (rmTouchCancel) {\n            rmTouchCancel();\n        }\n        rmTouchMove = rmTouchEnd = rmTouchCancel = undefined;\n    };\n    const stopMouse = () => {\n        if (rmMouseMove) {\n            rmMouseMove();\n        }\n        if (rmMouseUp) {\n            rmMouseUp();\n        }\n        rmMouseMove = rmMouseUp = undefined;\n    };\n    const stop = () => {\n        stopTouch();\n        stopMouse();\n    };\n    const enable = (isEnabled = true) => {\n        if (!isEnabled) {\n            if (rmTouchStart) {\n                rmTouchStart();\n            }\n            if (rmMouseStart) {\n                rmMouseStart();\n            }\n            rmTouchStart = rmMouseStart = undefined;\n            stop();\n        }\n        else {\n            if (!rmTouchStart) {\n                rmTouchStart = addEventListener(el, 'touchstart', handleTouchStart, options);\n            }\n            if (!rmMouseStart) {\n                rmMouseStart = addEventListener(el, 'mousedown', handleMouseDown, options);\n            }\n        }\n    };\n    const destroy = () => {\n        enable(false);\n        pointerUp = pointerMove = pointerDown = undefined;\n    };\n    return {\n        enable,\n        stop,\n        destroy,\n    };\n};\nconst getDocument = (node) => {\n    return node instanceof Document ? node : node.ownerDocument;\n};\n\nconst createPanRecognizer = (direction, thresh, maxAngle) => {\n    const radians = maxAngle * (Math.PI / 180);\n    const isDirX = direction === 'x';\n    const maxCosine = Math.cos(radians);\n    const threshold = thresh * thresh;\n    let startX = 0;\n    let startY = 0;\n    let dirty = false;\n    let isPan = 0;\n    return {\n        start(x, y) {\n            startX = x;\n            startY = y;\n            isPan = 0;\n            dirty = true;\n        },\n        detect(x, y) {\n            if (!dirty) {\n                return false;\n            }\n            const deltaX = x - startX;\n            const deltaY = y - startY;\n            const distance = deltaX * deltaX + deltaY * deltaY;\n            if (distance < threshold) {\n                return false;\n            }\n            const hypotenuse = Math.sqrt(distance);\n            const cosine = (isDirX ? deltaX : deltaY) / hypotenuse;\n            if (cosine > maxCosine) {\n                isPan = 1;\n            }\n            else if (cosine < -maxCosine) {\n                isPan = -1;\n            }\n            else {\n                isPan = 0;\n            }\n            dirty = false;\n            return true;\n        },\n        isGesture() {\n            return isPan !== 0;\n        },\n        getDirection() {\n            return isPan;\n        },\n    };\n};\n\n// TODO(FW-2832): types\nconst createGesture = (config) => {\n    let hasCapturedPan = false;\n    let hasStartedPan = false;\n    let hasFiredStart = true;\n    let isMoveQueued = false;\n    const finalConfig = Object.assign({ disableScroll: false, direction: 'x', gesturePriority: 0, passive: true, maxAngle: 40, threshold: 10 }, config);\n    const canStart = finalConfig.canStart;\n    const onWillStart = finalConfig.onWillStart;\n    const onStart = finalConfig.onStart;\n    const onEnd = finalConfig.onEnd;\n    const notCaptured = finalConfig.notCaptured;\n    const onMove = finalConfig.onMove;\n    const threshold = finalConfig.threshold;\n    const passive = finalConfig.passive;\n    const blurOnStart = finalConfig.blurOnStart;\n    const detail = {\n        type: 'pan',\n        startX: 0,\n        startY: 0,\n        startTime: 0,\n        currentX: 0,\n        currentY: 0,\n        velocityX: 0,\n        velocityY: 0,\n        deltaX: 0,\n        deltaY: 0,\n        currentTime: 0,\n        event: undefined,\n        data: undefined,\n    };\n    const pan = createPanRecognizer(finalConfig.direction, finalConfig.threshold, finalConfig.maxAngle);\n    const gesture = GESTURE_CONTROLLER.createGesture({\n        name: config.gestureName,\n        priority: config.gesturePriority,\n        disableScroll: config.disableScroll,\n    });\n    const pointerDown = (ev) => {\n        const timeStamp = now(ev);\n        if (hasStartedPan || !hasFiredStart) {\n            return false;\n        }\n        updateDetail(ev, detail);\n        detail.startX = detail.currentX;\n        detail.startY = detail.currentY;\n        detail.startTime = detail.currentTime = timeStamp;\n        detail.velocityX = detail.velocityY = detail.deltaX = detail.deltaY = 0;\n        detail.event = ev;\n        // Check if gesture can start\n        if (canStart && canStart(detail) === false) {\n            return false;\n        }\n        // Release fallback\n        gesture.release();\n        // Start gesture\n        if (!gesture.start()) {\n            return false;\n        }\n        hasStartedPan = true;\n        if (threshold === 0) {\n            return tryToCapturePan();\n        }\n        pan.start(detail.startX, detail.startY);\n        return true;\n    };\n    const pointerMove = (ev) => {\n        // fast path, if gesture is currently captured\n        // do minimum job to get user-land even dispatched\n        if (hasCapturedPan) {\n            if (!isMoveQueued && hasFiredStart) {\n                isMoveQueued = true;\n                calcGestureData(detail, ev);\n                requestAnimationFrame(fireOnMove);\n            }\n            return;\n        }\n        // gesture is currently being detected\n        calcGestureData(detail, ev);\n        if (pan.detect(detail.currentX, detail.currentY)) {\n            if (!pan.isGesture() || !tryToCapturePan()) {\n                abortGesture();\n            }\n        }\n    };\n    const fireOnMove = () => {\n        // Since fireOnMove is called inside a RAF, onEnd() might be called,\n        // we must double check hasCapturedPan\n        if (!hasCapturedPan) {\n            return;\n        }\n        isMoveQueued = false;\n        if (onMove) {\n            onMove(detail);\n        }\n    };\n    const tryToCapturePan = () => {\n        if (!gesture.capture()) {\n            return false;\n        }\n        hasCapturedPan = true;\n        hasFiredStart = false;\n        // reset start position since the real user-land event starts here\n        // If the pan detector threshold is big, not resetting the start position\n        // will cause a jump in the animation equal to the detector threshold.\n        // the array of positions used to calculate the gesture velocity does not\n        // need to be cleaned, more points in the positions array always results in a\n        // more accurate value of the velocity.\n        detail.startX = detail.currentX;\n        detail.startY = detail.currentY;\n        detail.startTime = detail.currentTime;\n        if (onWillStart) {\n            onWillStart(detail).then(fireOnStart);\n        }\n        else {\n            fireOnStart();\n        }\n        return true;\n    };\n    const blurActiveElement = () => {\n        if (typeof document !== 'undefined') {\n            const activeElement = document.activeElement;\n            if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur) {\n                activeElement.blur();\n            }\n        }\n    };\n    const fireOnStart = () => {\n        if (blurOnStart) {\n            blurActiveElement();\n        }\n        if (onStart) {\n            onStart(detail);\n        }\n        hasFiredStart = true;\n    };\n    const reset = () => {\n        hasCapturedPan = false;\n        hasStartedPan = false;\n        isMoveQueued = false;\n        hasFiredStart = true;\n        gesture.release();\n    };\n    // END *************************\n    const pointerUp = (ev) => {\n        const tmpHasCaptured = hasCapturedPan;\n        const tmpHasFiredStart = hasFiredStart;\n        reset();\n        if (!tmpHasFiredStart) {\n            return;\n        }\n        calcGestureData(detail, ev);\n        // Try to capture press\n        if (tmpHasCaptured) {\n            if (onEnd) {\n                onEnd(detail);\n            }\n            return;\n        }\n        // Not captured any event\n        if (notCaptured) {\n            notCaptured(detail);\n        }\n    };\n    const pointerEvents = createPointerEvents(finalConfig.el, pointerDown, pointerMove, pointerUp, {\n        capture: false,\n        passive,\n    });\n    const abortGesture = () => {\n        reset();\n        pointerEvents.stop();\n        if (notCaptured) {\n            notCaptured(detail);\n        }\n    };\n    return {\n        enable(enable = true) {\n            if (!enable) {\n                if (hasCapturedPan) {\n                    pointerUp(undefined);\n                }\n                reset();\n            }\n            pointerEvents.enable(enable);\n        },\n        destroy() {\n            gesture.destroy();\n            pointerEvents.destroy();\n        },\n    };\n};\nconst calcGestureData = (detail, ev) => {\n    if (!ev) {\n        return;\n    }\n    const prevX = detail.currentX;\n    const prevY = detail.currentY;\n    const prevT = detail.currentTime;\n    updateDetail(ev, detail);\n    const currentX = detail.currentX;\n    const currentY = detail.currentY;\n    const timestamp = (detail.currentTime = now(ev));\n    const timeDelta = timestamp - prevT;\n    if (timeDelta > 0 && timeDelta < 100) {\n        const velocityX = (currentX - prevX) / timeDelta;\n        const velocityY = (currentY - prevY) / timeDelta;\n        detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n        detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n    }\n    detail.deltaX = currentX - detail.startX;\n    detail.deltaY = currentY - detail.startY;\n    detail.event = ev;\n};\nconst updateDetail = (ev, detail) => {\n    // get X coordinates for either a mouse click\n    // or a touch depending on the given event\n    let x = 0;\n    let y = 0;\n    if (ev) {\n        const changedTouches = ev.changedTouches;\n        if (changedTouches && changedTouches.length > 0) {\n            const touch = changedTouches[0];\n            x = touch.clientX;\n            y = touch.clientY;\n        }\n        else if (ev.pageX !== undefined) {\n            x = ev.pageX;\n            y = ev.pageY;\n        }\n    }\n    detail.currentX = x;\n    detail.currentY = y;\n};\nconst now = (ev) => {\n    return ev.timeStamp || Date.now();\n};\n\nexport { createGesture };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { m as clamp } from './helpers.js';\nimport { i as isRTL } from './dir.js';\nimport { createGesture } from './index3.js';\n\nconst createSwipeBackGesture = (el, canStartHandler, onStartHandler, onMoveHandler, onEndHandler) => {\n    const win = el.ownerDocument.defaultView;\n    let rtl = isRTL(el);\n    /**\n     * Determine if a gesture is near the edge\n     * of the screen. If true, then the swipe\n     * to go back gesture should proceed.\n     */\n    const isAtEdge = (detail) => {\n        const threshold = 50;\n        const { startX } = detail;\n        if (rtl) {\n            return startX >= win.innerWidth - threshold;\n        }\n        return startX <= threshold;\n    };\n    const getDeltaX = (detail) => {\n        return rtl ? -detail.deltaX : detail.deltaX;\n    };\n    const getVelocityX = (detail) => {\n        return rtl ? -detail.velocityX : detail.velocityX;\n    };\n    const canStart = (detail) => {\n        /**\n         * The user's locale can change mid-session,\n         * so we need to check text direction at\n         * the beginning of every gesture.\n         */\n        rtl = isRTL(el);\n        return isAtEdge(detail) && canStartHandler();\n    };\n    const onMove = (detail) => {\n        // set the transition animation's progress\n        const delta = getDeltaX(detail);\n        const stepValue = delta / win.innerWidth;\n        onMoveHandler(stepValue);\n    };\n    const onEnd = (detail) => {\n        // the swipe back gesture has ended\n        const delta = getDeltaX(detail);\n        const width = win.innerWidth;\n        const stepValue = delta / width;\n        const velocity = getVelocityX(detail);\n        const z = width / 2.0;\n        const shouldComplete = velocity >= 0 && (velocity > 0.2 || delta > z);\n        const missing = shouldComplete ? 1 - stepValue : stepValue;\n        const missingDistance = missing * width;\n        let realDur = 0;\n        if (missingDistance > 5) {\n            const dur = missingDistance / Math.abs(velocity);\n            realDur = Math.min(dur, 540);\n        }\n        onEndHandler(shouldComplete, stepValue <= 0 ? 0.01 : clamp(0, stepValue, 0.9999), realDur);\n    };\n    return createGesture({\n        el,\n        gestureName: 'goback-swipe',\n        /**\n         * Swipe to go back should have priority over other horizontal swipe\n         * gestures. These gestures have a priority of 100 which is why 101 was chosen here.\n         */\n        gesturePriority: 101,\n        threshold: 10,\n        canStart,\n        onStart: onStartHandler,\n        onMove,\n        onEnd,\n    });\n};\n\nexport { createSwipeBackGesture };\n"], "names": ["GestureDelegate", "constructor", "ctrl", "id", "name", "priority", "disableScroll", "this", "canStart", "start", "capture", "captured", "release", "enableScroll", "destroy", "undefined", "BlockerDelegate", "disable", "block", "gesture", "disableGesture", "unblock", "enableGesture", "BACKDROP_NO_SCROLL", "GESTURE_CONTROLLER", "gestureId", "requestedStart", "Map", "disabledGestures", "disabledScroll", "Set", "createGesture", "config", "_a", "newID", "createBlocker", "opts", "arguments", "length", "<PERSON><PERSON><PERSON>", "set", "delete", "maxPriority", "for<PERSON>ach", "value", "Math", "max", "capturedId", "clear", "event", "CustomEvent", "detail", "document", "dispatchEvent", "get", "add", "size", "body", "classList", "remove", "isDisabled", "isCaptured", "isScrollDisabled", "disabled", "addEventListener", "el", "eventName", "callback", "listenerOpts", "supportsPassive", "passive", "node", "_sPassive", "Object", "defineProperty", "e", "getDocument", "Document", "ownerDocument", "hasCapturedPan", "hasStartedPan", "hasFiredStart", "isMoveQueued", "finalConfig", "assign", "direction", "gesturePriority", "maxAngle", "threshold", "onWillStart", "onStart", "onEnd", "notCaptured", "onMove", "blurOnStart", "type", "startX", "startY", "startTime", "currentX", "currentY", "velocityX", "velocityY", "deltaX", "deltaY", "currentTime", "data", "pan", "createPanRecognizer", "thresh", "radians", "PI", "isDirX", "maxCosine", "cos", "dirty", "isPan", "x", "y", "detect", "distance", "hypotenuse", "sqrt", "cosine", "isGesture", "getDirection", "fireOnMove", "tryToCapturePan", "then", "fireOnStart", "blurActiveElement", "activeElement", "blur", "reset", "pointerUp", "ev", "tmpHasCaptured", "tmpHasFiredStart", "calcGestureData", "pointerEvents", "createPointerEvents", "pointerDown", "pointer<PERSON><PERSON>", "options", "rmTouchStart", "rmTouchMove", "rmTouchEnd", "rmTouchCancel", "rmMouseStart", "rmMouseMove", "rmMouseUp", "lastTouchEvent", "handleTouchStart", "Date", "now", "target", "handleTouchEnd", "handleMouseDown", "handleMouseUp", "stopTouch", "stopMouse", "stop", "enable", "timeStamp", "updateDetail", "requestAnimationFrame", "abortGesture", "prevX", "prevY", "prevT", "<PERSON><PERSON><PERSON><PERSON>", "changedTouches", "touch", "clientX", "clientY", "pageX", "pageY", "createSwipeBackGesture", "canStartHandler", "onStartHandler", "onMoveHandler", "onEndHandler", "win", "defaultView", "rtl", "isRTL", "getDeltaX", "innerWidth", "isAtEdge", "<PERSON><PERSON><PERSON><PERSON>", "delta", "width", "velocity", "getVelocityX", "shouldComplete", "missingDistance", "realDur", "dur", "abs", "min", "clamp"], "sourceRoot": ""}