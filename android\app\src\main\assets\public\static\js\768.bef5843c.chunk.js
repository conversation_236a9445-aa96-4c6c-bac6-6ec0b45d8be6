/*! For license information please see 768.bef5843c.chunk.js.LICENSE.txt */
"use strict";(globalThis.webpackChunkalphabet_game=globalThis.webpackChunkalphabet_game||[]).push([[768],{768:(e,t,i)=>{i.r(t),i.d(t,{KEYBOARD_DID_CLOSE:()=>s,KEYBOARD_DID_OPEN:()=>o,copyVisualViewport:()=>k,keyboardDidClose:()=>u,keyboardDidOpen:()=>c,keyboardDidResize:()=>w,resetKeyboardAssist:()=>n,setKeyboardClose:()=>l,setKeyboardOpen:()=>b,startKeyboardAssist:()=>p,trackViewportChanges:()=>D});var a=i(793);const o="ionKeyboardDidShow",s="ionKeyboardDidHide";let d={},r={},h=!1;const n=()=>{d={},r={},h=!1},p=e=>{if(a.K.getEngine())g(e);else{if(!e.visualViewport)return;r=k(e.visualViewport),e.visualViewport.onresize=()=>{D(e),c()||w(e)?b(e):u(e)&&l(e)}}},g=e=>{e.addEventListener("keyboardDidShow",t=>b(e,t)),e.addEventListener("keyboardDidHide",()=>l(e))},b=(e,t)=>{f(e,t),h=!0},l=e=>{y(e),h=!1},c=()=>{const e=(d.height-r.height)*r.scale;return!h&&d.width===r.width&&e>150},w=e=>h&&!u(e),u=e=>h&&r.height===e.innerHeight,f=(e,t)=>{const i=t?t.keyboardHeight:e.innerHeight-r.height,a=new CustomEvent(o,{detail:{keyboardHeight:i}});e.dispatchEvent(a)},y=e=>{const t=new CustomEvent(s);e.dispatchEvent(t)},D=e=>{d=Object.assign({},r),r=k(e.visualViewport)},k=e=>({width:Math.round(e.width),height:Math.round(e.height),offsetTop:e.offsetTop,offsetLeft:e.offsetLeft,pageTop:e.pageTop,pageLeft:e.pageLeft,scale:e.scale})}}]);
//# sourceMappingURL=768.bef5843c.chunk.js.map