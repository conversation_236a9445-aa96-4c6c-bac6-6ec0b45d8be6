{"version": 3, "file": "static/js/768.bef5843c.chunk.js", "mappings": ";gaAKA,MAAMA,EAAoB,qBACpBC,EAAqB,qBAG3B,IAAIC,EAAyB,CAAC,EAC1BC,EAAwB,CAAC,EACzBC,GAAe,EAInB,MAAMC,EAAsBA,KACxBH,EAAyB,CAAC,EAC1BC,EAAwB,CAAC,EACzBC,GAAe,GAEbE,EAAuBC,IAUzB,GATqBC,EAAAA,EAASC,YAU1BC,EAAqBH,OAEpB,CACD,IAAKA,EAAII,eACL,OAEJR,EAAwBS,EAAmBL,EAAII,gBAC/CJ,EAAII,eAAeE,SAAW,KAC1BC,EAAqBP,GACjBQ,KAAqBC,EAAkBT,GACvCU,EAAgBV,GAEXW,EAAiBX,IACtBY,EAAiBZ,GAG7B,GAOEG,EAAwBH,IAC1BA,EAAIa,iBAAiB,kBAAoBC,GAAOJ,EAAgBV,EAAKc,IACrEd,EAAIa,iBAAiB,kBAAmB,IAAMD,EAAiBZ,KAE7DU,EAAkBA,CAACV,EAAKc,KAC1BC,EAAsBf,EAAKc,GAC3BjB,GAAe,GAEbe,EAAoBZ,IACtBgB,EAAuBhB,GACvBH,GAAe,GAcbW,EAAkBA,KACpB,MAAMS,GAA0BtB,EAAuBuB,OAAStB,EAAsBsB,QAAUtB,EAAsBuB,MACtH,OAAStB,GACLF,EAAuByB,QAAUxB,EAAsBwB,OACvDH,EA3EmB,KAiFrBR,EAAqBT,GAChBH,IAAiBc,EAAiBX,GAQvCW,EAAoBX,GACfH,GAAgBD,EAAsBsB,SAAWlB,EAAIqB,YAK1DN,EAAwBA,CAACf,EAAKsB,KAChC,MAAMC,EAAiBD,EAAWA,EAASC,eAAiBvB,EAAIqB,YAAczB,EAAsBsB,OAC9FJ,EAAK,IAAIU,YAAY/B,EAAmB,CAC1CgC,OAAQ,CAAEF,oBAEdvB,EAAI0B,cAAcZ,IAKhBE,EAA0BhB,IAC5B,MAAMc,EAAK,IAAIU,YAAY9B,GAC3BM,EAAI0B,cAAcZ,IAQhBP,EAAwBP,IAC1BL,EAAyBgC,OAAOC,OAAO,CAAC,EAAGhC,GAC3CA,EAAwBS,EAAmBL,EAAII,iBAM7CC,EAAsBD,IACjB,CACHgB,MAAOS,KAAKC,MAAM1B,EAAegB,OACjCF,OAAQW,KAAKC,MAAM1B,EAAec,QAClCa,UAAW3B,EAAe2B,UAC1BC,WAAY5B,EAAe4B,WAC3BC,QAAS7B,EAAe6B,QACxBC,SAAU9B,EAAe8B,SACzBf,MAAOf,EAAee,O", "sources": ["../node_modules/@ionic/core/components/keyboard2.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { K as Keyboard } from './keyboard.js';\n\nconst KEYBOARD_DID_OPEN = 'ionKeyboardDidShow';\nconst KEYBOARD_DID_CLOSE = 'ionKeyboardDidHide';\nconst KEYBOARD_THRESHOLD = 150;\n// TODO(FW-2832): types\nlet previousVisualViewport = {};\nlet currentVisualViewport = {};\nlet keyboardOpen = false;\n/**\n * This is only used for tests\n */\nconst resetKeyboardAssist = () => {\n    previousVisualViewport = {};\n    currentVisualViewport = {};\n    keyboardOpen = false;\n};\nconst startKeyboardAssist = (win) => {\n    const nativeEngine = Keyboard.getEngine();\n    /**\n     * If the native keyboard plugin is available\n     * then we are running in a native environment. As a result\n     * we should only listen on the native events instead of\n     * using the Visual Viewport as the Ionic webview manipulates\n     * how it resizes such that the Visual Viewport API is not\n     * reliable here.\n     */\n    if (nativeEngine) {\n        startNativeListeners(win);\n    }\n    else {\n        if (!win.visualViewport) {\n            return;\n        }\n        currentVisualViewport = copyVisualViewport(win.visualViewport);\n        win.visualViewport.onresize = () => {\n            trackViewportChanges(win);\n            if (keyboardDidOpen() || keyboardDidResize(win)) {\n                setKeyboardOpen(win);\n            }\n            else if (keyboardDidClose(win)) {\n                setKeyboardClose(win);\n            }\n        };\n    }\n};\n/**\n * Listen for events fired by native keyboard plugin\n * in Capacitor/Cordova so devs only need to listen\n * in one place.\n */\nconst startNativeListeners = (win) => {\n    win.addEventListener('keyboardDidShow', (ev) => setKeyboardOpen(win, ev));\n    win.addEventListener('keyboardDidHide', () => setKeyboardClose(win));\n};\nconst setKeyboardOpen = (win, ev) => {\n    fireKeyboardOpenEvent(win, ev);\n    keyboardOpen = true;\n};\nconst setKeyboardClose = (win) => {\n    fireKeyboardCloseEvent(win);\n    keyboardOpen = false;\n};\n/**\n * Returns `true` if the `keyboardOpen` flag is not\n * set, the previous visual viewport width equal the current\n * visual viewport width, and if the scaled difference\n * of the previous visual viewport height minus the current\n * visual viewport height is greater than KEYBOARD_THRESHOLD\n *\n * We need to be able to accommodate users who have zooming\n * enabled in their browser (or have zoomed in manually) which\n * is why we take into account the current visual viewport's\n * scale value.\n */\nconst keyboardDidOpen = () => {\n    const scaledHeightDifference = (previousVisualViewport.height - currentVisualViewport.height) * currentVisualViewport.scale;\n    return (!keyboardOpen &&\n        previousVisualViewport.width === currentVisualViewport.width &&\n        scaledHeightDifference > KEYBOARD_THRESHOLD);\n};\n/**\n * Returns `true` if the keyboard is open,\n * but the keyboard did not close\n */\nconst keyboardDidResize = (win) => {\n    return keyboardOpen && !keyboardDidClose(win);\n};\n/**\n * Determine if the keyboard was closed\n * Returns `true` if the `keyboardOpen` flag is set and\n * the current visual viewport height equals the\n * layout viewport height.\n */\nconst keyboardDidClose = (win) => {\n    return keyboardOpen && currentVisualViewport.height === win.innerHeight;\n};\n/**\n * Dispatch a keyboard open event\n */\nconst fireKeyboardOpenEvent = (win, nativeEv) => {\n    const keyboardHeight = nativeEv ? nativeEv.keyboardHeight : win.innerHeight - currentVisualViewport.height;\n    const ev = new CustomEvent(KEYBOARD_DID_OPEN, {\n        detail: { keyboardHeight },\n    });\n    win.dispatchEvent(ev);\n};\n/**\n * Dispatch a keyboard close event\n */\nconst fireKeyboardCloseEvent = (win) => {\n    const ev = new CustomEvent(KEYBOARD_DID_CLOSE);\n    win.dispatchEvent(ev);\n};\n/**\n * Given a window object, create a copy of\n * the current visual and layout viewport states\n * while also preserving the previous visual and\n * layout viewport states\n */\nconst trackViewportChanges = (win) => {\n    previousVisualViewport = Object.assign({}, currentVisualViewport);\n    currentVisualViewport = copyVisualViewport(win.visualViewport);\n};\n/**\n * Creates a deep copy of the visual viewport\n * at a given state\n */\nconst copyVisualViewport = (visualViewport) => {\n    return {\n        width: Math.round(visualViewport.width),\n        height: Math.round(visualViewport.height),\n        offsetTop: visualViewport.offsetTop,\n        offsetLeft: visualViewport.offsetLeft,\n        pageTop: visualViewport.pageTop,\n        pageLeft: visualViewport.pageLeft,\n        scale: visualViewport.scale,\n    };\n};\n\nexport { KEYBOARD_DID_CLOSE, KEYBOARD_DID_OPEN, copyVisualViewport, keyboardDidClose, keyboardDidOpen, keyboardDidResize, resetKeyboardAssist, setKeyboardClose, setKeyboardOpen, startKeyboardAssist, trackViewportChanges };\n"], "names": ["KEYBOARD_DID_OPEN", "KEYBOARD_DID_CLOSE", "previousVisualViewport", "currentVisualViewport", "keyboardOpen", "resetKeyboardAssist", "startKeyboardAssist", "win", "Keyboard", "getEngine", "startNativeListeners", "visualViewport", "copyVisualViewport", "onresize", "trackViewportChanges", "keyboardDidOpen", "keyboardDidResize", "setKeyboardOpen", "keyboardDidClose", "setKeyboardClose", "addEventListener", "ev", "fireKeyboardOpenEvent", "fireKeyboardCloseEvent", "scaledHeightDifference", "height", "scale", "width", "innerHeight", "nativeEv", "keyboardHeight", "CustomEvent", "detail", "dispatchEvent", "Object", "assign", "Math", "round", "offsetTop", "offsetLeft", "pageTop", "pageLeft"], "sourceRoot": ""}