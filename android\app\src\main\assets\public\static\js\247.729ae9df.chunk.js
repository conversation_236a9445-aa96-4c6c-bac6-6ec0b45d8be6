/*! For license information please see 247.729ae9df.chunk.js.LICENSE.txt */
"use strict";(globalThis.webpackChunkalphabet_game=globalThis.webpackChunkalphabet_game||[]).push([[247],{247:(e,t,a)=>{a.r(t),a.d(t,{startStatusTap:()=>r});var n=a(441),s=a(721),o=a(384);const r=()=>{const e=window;e.addEventListener("statusTap",()=>{(0,n.gv)(()=>{const t=e.innerWidth,a=e.innerHeight,r=document.elementFromPoint(t/2,a/2);if(!r)return;const i=(0,s.a)(r);i&&new Promise(e=>(0,o.c)(i,e)).then(()=>{(0,n.bN)(async()=>{i.style.setProperty("--overflow","hidden"),await(0,s.s)(i,300),i.style.removeProperty("--overflow")})})})})}}}]);
//# sourceMappingURL=247.729ae9df.chunk.js.map