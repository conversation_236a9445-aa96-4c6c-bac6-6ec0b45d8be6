{"version": 3, "file": "static/js/923.eef5654c.chunk.js", "mappings": ";wMAMA,MAEMA,EAAoBC,GACfC,SAASC,cAAc,GAAGF,wBAE/BG,EAAUC,GACLA,EAAGC,YAAcD,EAEtBE,EAAiBC,IACnB,MAAMC,EAAyB,aAAlBD,EAAMP,QAAyBO,EAAQA,EAAML,cAAc,YAClEO,EAAQ,uFACd,GAAY,MAARD,EAAc,CACd,MAAME,EAAYF,EAAKN,cAAc,6DACrC,OAAoB,MAAbQ,EAAoBA,EAAUR,cAAcO,GAAS,IAChE,CACA,OAAOF,EAAML,cAAcO,IAEzBE,EAAgBA,CAACJ,EAAOK,KAC1B,MAAMJ,EAAyB,aAAlBD,EAAMP,QAAyBO,EAAQA,EAAML,cAAc,YACxE,IAAIW,EAAc,GAClB,GAAY,MAARL,EAAc,CACd,MAAME,EAAYF,EAAKN,cAAc,6DACpB,MAAbQ,IACAG,EAAcH,EAAUI,iBAAiB,eAEjD,MAEID,EAAcN,EAAMO,iBAAiB,eAEzC,IAAK,MAAMC,KAAWF,EAAa,CAC/B,MAAMG,EAAeD,EAAQE,QAAQ,cAC/BC,EAAeF,IAAiBA,EAAaG,UAAUC,SAAS,qCAChEC,EAAaN,EAAQb,cAAc,mBACnCoB,EAAkBP,EAAQI,UAAUC,SAAS,oBAC7CG,EAA6B,UAAjBR,EAAQS,MAAqC,KAAjBT,EAAQS,KACtD,GAAmB,OAAfH,GAAuBE,IAAeD,GAAmBJ,GAAgBN,IAAmBU,GAC5F,OAAOD,CAEf,CACA,OAAO,MAkCLI,EAAoBA,CAACC,EAAeC,EAAKf,EAAegB,EAAcC,EAAeC,EAAkBC,EAAmBC,EAAcC,KAC1I,IAAIC,EAAIC,EACR,MAAMC,EAA2BT,EAAM,eAAeE,EAAcQ,MAAQ,OAAYR,EAAcS,KAAO,EAAxB,KAC/EC,EAAgBZ,EAAM,QAAU,OAChCa,EAAgBb,EAAM,OAAS,QAC/Bc,EAAqBd,EAAM,QAAU,OAUrCe,GAAsE,QAAvCR,EAAKJ,EAAiBa,mBAAgC,IAAPT,OAAgB,EAASA,EAAGU,WAAiD,QAAnCT,EAAKH,EAAaW,mBAAgC,IAAPR,OAAgB,EAASA,EAAGS,QAC/LC,EAAcZ,EAAkBa,MAAQf,EAAkBe,MAK1DC,GAAgBd,EAAkBe,OAASC,GAA2BlB,EAAkBiB,OACxFE,EAAmBR,EACnB,SAASG,MAAgBE,KACzB,SAASA,KACTI,EAAiB,WAEjBC,EADmBjD,EAAOyB,GAAc1B,cAAc,YACjBmD,wBAOrCC,EAA8B3B,EAC3ByB,EAAkBN,MAAQ,GAAKM,EAAkBf,MAAQR,EAAcQ,OAA1E,KACGR,EAAcS,KAAOc,EAAkBN,MAAQ,EAAlD,KACAS,EAA4B5B,EAAM,IAAI6B,OAAOC,WAAa5B,EAAcQ,UAAY,GAAGR,EAAcS,SAOrGoB,EAA8B,GAAGzB,EAAkB0B,QAMnDC,EAA4B,GAAG/B,EAAc8B,QAgB7CE,EAAsBjD,EAJS,CACjC,CAAEkD,OAAQ,EAAGC,UAAW,eAAeR,MAA8BK,SACrE,CAAEE,OAAQ,EAAGC,UAAW,eAAeT,MAAgCI,UANvC,CAChC,CAAEI,OAAQ,EAAGC,UAAW,eAAeT,MAAgCI,SACvE,CAAEI,OAAQ,EAAGC,UAAW,eAAeR,MAA8BK,UAsBnEI,EAAiBpD,EAJS,CAC5B,CAAEkD,OAAQ,EAAGG,QAAS,EAAGF,UAAWZ,GACpC,CAAEW,OAAQ,EAAGG,QAAS,EAAGF,UAAWb,IANT,CAC3B,CAAEY,OAAQ,EAAGG,QAAS,EAAGF,UAAWb,GACpC,CAAEY,OAAQ,EAAGG,QAAS,EAAGF,UAAWZ,IAuBlCe,EAAiBtD,EALS,CAC5B,CAAEkD,OAAQ,EAAGG,QAAS,EAAGF,UAAW,YACpC,CAAED,OAAQ,GAAKG,QAAS,EAAGF,UAAW,cACtC,CAAED,OAAQ,EAAGG,QAAS,EAAGF,UAAW,eART,CAC3B,CAAED,OAAQ,EAAGG,QAAS,EAAGF,UAAW,cACpC,CAAED,OAAQ,GAAKG,QAAS,EAAGF,UAAW,cACtC,CAAED,OAAQ,EAAGG,QAAS,EAAGF,UAAW,aAQlCI,GAAkCC,EAAAA,EAAAA,KAClCC,GAAkCD,EAAAA,EAAAA,KAClCE,GAA8BF,EAAAA,EAAAA,KAC9BG,EAAqBxE,EAAiB,mBACtCyE,EAAyBrE,EAAOoE,GAAoBrE,cAAc,gBAClEuE,EAAyBtE,EAAOoE,GAAoBrE,cAAc,YACxEqE,EAAmBG,KAAO9C,EAAa8C,KACvCH,EAAmBI,KAAO/C,EAAa+C,KACvCJ,EAAmBK,KAAOhD,EAAagD,KACvCL,EAAmBM,MAAQjD,EAAaiD,MACxCN,EAAmBO,SAAWlD,EAAakD,SAC3CP,EAAmBQ,MAAMC,YAAY,UAAW,SAChDT,EAAmBQ,MAAMC,YAAY,WAAY,SACjDX,EAAgCY,WAAWR,GAC3CN,EAAgCc,WAAWT,GAC3CF,EAA4BW,WAAWV,GACvCD,EACKY,aAAa,CACdC,SAAU,WACVxB,IAAK,MACL,CAAClB,GAAqB,QAErB2C,UAAUvB,GACfM,EACKe,aAAa,CACd,mBAAoB,GAAG3C,UAEtB8C,eAAe,KAChBzD,EAAamD,MAAMC,YAAY,UAAW,QAC1CT,EAAmBQ,MAAMC,YAAYzC,EAAeH,KAEnDkD,cAAc,KACf1D,EAAamD,MAAMC,YAAY,UAAW,IAC1CT,EAAmBQ,MAAMC,YAAY,UAAW,QAChDT,EAAmBQ,MAAMQ,eAAehD,KAEvC6C,UAAUpB,GACfK,EACKa,aAAa,CACd,mBAAoB,GAAG1C,aAEtB4C,UAAUlB,GACfxC,EAAc8D,aAAa,CACvBrB,EACAE,EACAC,KAGFmB,EAAoBA,CAAC/D,EAAeC,EAAKf,EAAeoB,EAAc0D,EAAezD,EAAmBH,EAAkBC,KAC5H,IAAIG,EAAIC,EAIR,MAAMwD,EAAWhE,EAAM,QAAU,OAC3BiE,EAAqBjE,EAAM,eAAe+D,EAAcrD,WAAa,GAAGqD,EAAcpD,SAOtFuD,EAAoB,GAAGH,EAAc/B,QAerCmC,EAAkBnE,EAClB,IAAI6B,OAAOC,WAAa1B,EAAkBM,MAVT,MAW9BN,EAAkBgE,EAXY,EAWjC,KASAC,EAAqBjE,EAAkBkE,EADb,EACR,KAsBlBvD,GAAsE,QAAvCR,EAAKJ,EAAiBa,mBAAgC,IAAPT,OAAgB,EAASA,EAAGU,WAAiD,QAAnCT,EAAKH,EAAaW,mBAAgC,IAAPR,OAAgB,EAASA,EAAGS,QAC/LC,EAAcd,EAAkBe,MAAQb,EAAkBa,MAC1DC,EAAehB,EAAkBiB,QAAUf,EAAkBe,OAASC,GACtEiD,EAAc,WACdC,EAAYzD,EAA6B,SAASG,MAAgBE,KAAkB,SAASA,KAe7FqD,EAAYxF,EAdU,CACxB,CAAEkD,OAAQ,EAAGG,QAAS,EAAGF,UAAW,eAAe+B,MAAoBE,SAAuBG,KAC9F,CAAErC,OAAQ,GAAKG,QAAS,GACxB,CAAEH,OAAQ,EAAGG,QAAS,EAAGF,UAAW,oBAAqC8B,SAAyBK,MAE3E,CACvB,CACIpC,OAAQ,EACRG,QAAS,IACTF,UAAW,oBAAqC8B,SAAyBK,KAE7E,CAAEpC,OAAQ,GAAKG,QAAS,GACxB,CAAEH,OAAQ,EAAGG,QAAS,EAAGF,UAAW,eAAe+B,MAAoBE,SAAuBG,MAG5FE,EAAgBtG,EAAiB,aACjCuG,GAA4BlC,EAAAA,EAAAA,KAClCiC,EAAcE,UAAYvE,EAAauE,UACvCF,EAAcG,KAAOxE,EAAawE,KAClCH,EAAcxB,MAAQ7C,EAAa6C,MACnCyB,EAA0BrB,WAAWoB,GACrCC,EACKpB,aAAa,CACd,mBAAoB,GAAGS,QAOvB3C,OAAQ,GAAG0C,EAAc1C,WACzByD,QAAS,GACTtB,SAAU,WACV,CAACQ,GAAWC,IAEXP,eAAe,KAChBrD,EAAa+C,MAAMC,YAAY,UAAW,OAEzCM,cAAc,KACftD,EAAa+C,MAAMC,YAAY,UAAW,IAC1CqB,EAActB,MAAMC,YAAY,UAAW,UAE1CI,UAAUgB,GACf1E,EAAc8D,aAAac,IAEzBI,EAAyBA,CAACC,EAAOC,KACnC,IAAI1E,EACJ,IACI,MAAM2E,EAAS,8BACTC,EAAU,UACVC,EAAY,YACZC,EAAS,KACTC,EAAc,GACdC,EAAoC,QAA5BP,EAAMQ,cAAcC,IAC5BC,EAAYH,EAAQ,SAAW,QAC/BI,EAAWJ,EAAQ,MAAQ,OAC3BK,EAAaX,EAAKW,WAClBC,EAAYZ,EAAKY,UACjB5G,EAAmC,SAAnBgG,EAAKa,UACrBC,EAAYH,EAAWrH,cAAc,wBACrCyH,EAAYJ,EAAWzG,iBAAiB,qEACxC8G,EAAqBL,EAAWzG,iBAAiB,qCACjDY,GAAgB0C,EAAAA,EAAAA,KAChByD,GAA2BzD,EAAAA,EAAAA,KAQjC,GAPA1C,EACKuD,WAAWsC,GACXO,UAAmC,QAAxB5F,EAAK0E,EAAKkB,gBAA6B,IAAP5F,EAAgBA,EAAK,IAjW5D,KAkWJ6F,OAAOnB,EAAKmB,QAAUlB,GACtBmB,KAAK,QACLC,kBAAkB,sBAEnBT,GAAuB,OAAVb,QAA4BuB,IAAVvB,EAAqB,CACpD,MAAMwB,GAAoB/D,EAAAA,EAAAA,KAC1B+D,EAAkBlD,WAAW0B,GAC7BjF,EAAc8D,aAAa2C,EAC/B,CAqBA,GApBKT,GAA2C,IAA9BE,EAAmBQ,QAAqC,IAArBT,EAAUS,QAI3DP,EAAyB5C,WAAWyC,GACpCG,EAAyB5C,WAAW0C,IAJpCE,EAAyB5C,WAAWsC,EAAWrH,cAAc,4DAMjEwB,EAAc8D,aAAaqC,GACvBjH,EACAiH,EACKQ,kBAAkB,CAACvB,IACnBwB,OAAO,YAAa,cAAchB,KAAa,cAAcN,MAC7DsB,OAAOxB,EAASG,EAAa,GAIlCY,EACKQ,kBAAkB,CAACvB,IACnBwB,OAAO,YAAa,cAAcjB,KAAc,cAAcL,MAEnEU,EAAW,CACX,MAAMa,EAA6BpI,EAAOuH,GAAWxH,cAAc,sBACnE,GAAIqI,EAA4B,CAC5B,MAAMC,EAA4BD,EAA2BrI,cAAc,qBACrEuI,EAA6BF,EAA2BrI,cAAc,sBACtEwI,GAA2BtE,EAAAA,EAAAA,KAC3BuE,GAA0BvE,EAAAA,EAAAA,KAC1BwE,GAA2BxE,EAAAA,EAAAA,KACjCsE,EACKzD,WAAWsD,GACXrD,aAAa,CAAEjB,QAAS,IAAKwC,QAAS,UACtCoC,YAAY,CAAE5E,QAAS,GAAIwC,QAAS,KACzCkC,EACK1D,WAAWuD,GACXH,kBAAkB,CAACvB,IACnBwB,OAAOxB,EAAS,EAAG,IACxB8B,EACK3D,WAAWwD,GACXJ,kBAAkB,CAACvB,IACnBwB,OAAOxB,EAAS,IAAM,IAC3B4B,EAAyBlD,aAAa,CAACmD,EAAyBC,IAChEf,EAAyBrC,aAAa,CAACkD,GAC3C,CACJ,CACA,MAAMI,EAA+BvB,EAAWrH,cAAc,wCACxD,QAAE6I,EAAO,SAAEC,GA/WUC,EAACvH,EAAeC,EAAKf,EAAe2G,EAAYC,KAC/E,MAAM0B,EAAqBvI,EAAc4G,EAAY3G,GAC/CuI,EAAoB7I,EAAckH,GAClC4B,EAAqB9I,EAAciH,GACnC8B,EAAoB1I,EAAc6G,EAAW5G,GAC7C0I,EAAgD,OAAvBJ,GAAqD,OAAtBC,IAA+BvI,EACvF2I,EAAiD,OAAvBH,GAAqD,OAAtBC,GAA8BzI,EAC7F,GAAI0I,EAAwB,CACxB,MAAME,EAAuBL,EAAkB9F,wBACzCoG,EAAwBP,EAAmB7F,wBAC3CqG,EAA2BvJ,EAAO+I,GAAoBhJ,cAAc,gBACpEyJ,EAA4BD,EAAyBrG,wBAErDuG,EAD0BzJ,EAAOgJ,GAAmBjJ,cAAc,kBACfmD,wBACzDoC,EAAkB/D,EAAeC,EAAKf,EAAeuI,EAAmBK,EAAsBI,EAA0BF,EAA0BC,GAClJlI,EAAkBC,EAAeC,EAAKf,EAAesI,EAAoBO,EAAuBC,EAA0BC,EAA2BR,EAAmBS,EAC5K,MACK,GAAIL,EAAyB,CAC9B,MAAMM,EAAwBT,EAAmB/F,wBAC3CyG,EAAuBT,EAAkBhG,wBACzC0G,EAA0B5J,EAAOkJ,GAAmBnJ,cAAc,gBAClE8J,EAA2BD,EAAwB1G,wBAEnD4G,EAD2B9J,EAAOiJ,GAAoBlJ,cAAc,kBACfmD,wBAC3DoC,EAAkB/D,EAAeC,EAAKf,EAAewI,EAAoBS,EAAuBI,EAA2BF,EAAyBC,GACpJvI,EAAkBC,EAAeC,EAAKf,EAAeyI,EAAmBS,EAAsBC,EAAyBC,EAA0BZ,EAAoBa,EACzK,CACA,MAAO,CACHlB,QAASO,EACTN,SAAUO,IAkVoBN,CAA2BvH,EAAewF,EAAOtG,EAAe2G,EAAYC,GAgF1G,GA/EAI,EAAmBsC,QAASC,IACxB,MAAMC,GAAkBhG,EAAAA,EAAAA,KACxBgG,EAAgBnF,WAAWkF,GAC3BzI,EAAc8D,aAAa4E,GAC3B,MAAMC,GAAgBjG,EAAAA,EAAAA,KACtBiG,EAAcpF,WAAWkF,EAAkBjK,cAAc,cACzD,MAAMoK,GAAyBlG,EAAAA,EAAAA,KACzBrD,EAAUwJ,MAAMC,KAAKL,EAAkBrJ,iBAAiB,6BACxDE,EAAemJ,EAAkBlJ,QAAQ,cACzCwJ,EAAkC,OAAjBzJ,QAA0C,IAAjBA,OAA0B,EAASA,EAAaG,UAAUC,SAAS,qCACnH,IAAIsJ,EAEAA,EADA9J,EACmBG,EAAQ4J,OAAQC,IAC/B,MAAMC,EAAmBD,EAAOzJ,UAAUC,SAAS,oBACnD,OAAQyJ,IAAqBJ,IAAoBI,IAIlC9J,EAAQ4J,OAAQC,IAAYA,EAAOzJ,UAAUC,SAAS,qBAE7EkJ,EAAuBrF,WAAWyF,GAClC,MAAMI,GAAuB1G,EAAAA,EAAAA,KAC7B0G,EAAqB7F,WAAWkF,EAAkBrJ,iBAAiB,iEACnE,MAAMiK,GAAoB3G,EAAAA,EAAAA,KAC1B2G,EAAkB9F,WAAW9E,EAAOgK,GAAmBjK,cAAc,wBACrE,MAAMgJ,GAAqB9E,EAAAA,EAAAA,KACrBxC,EAAeuI,EAAkBjK,cAAc,mBAarD,GAZI0B,GACAsH,EAAmBjE,WAAWrD,GAElCwI,EAAgB5E,aAAa,CACzB6E,EACAC,EACAQ,EACAC,EACA7B,IAEJoB,EAAuBhC,OAAOxB,EAAS,IAAM,GAC7CgE,EAAqBxC,OAAOxB,EAAS,IAAM,GACvClG,EACK6J,GACDJ,EACK/B,OAAO,YAAa,cAAchB,KAAa,cAAcN,MAC7DsB,OAAOxB,EAAS,IAAM,GAE/BgE,EAAqBxC,OAAO,YAAa,cAAchB,KAAa,cAAcN,MAElFkC,EAAmBZ,OAAOxB,EAAS,IAAM,OAExC,CAEIgC,GACDuB,EACK/B,OAAO,YAAa,cAAcjB,KAAc,cAAcL,MAC9DsB,OAAOxB,EAAS,IAAM,GAE/BgE,EAAqBxC,OAAO,YAAa,cAAcjB,KAAc,cAAcL,MACnF+D,EAAkB1C,kBAAkB,CAACvB,EAAS,cAY9C,IAX2C,OAAjB9F,QAA0C,IAAjBA,OAA0B,EAASA,EAAagK,aAK/FD,EAAkBzC,OAAO,YAAapB,EAAQ,oBAAsB,mBAAoB,mBAHxF6D,EAAkBzC,OAAOxB,EAAS,IAAM,kBAMvCiC,GACDG,EAAmBZ,OAAOxB,EAAS,IAAM,GAEzClF,IAAiBmH,EAAS,CAC1B,MAAMkC,GAAsB7G,EAAAA,EAAAA,KAC5B6G,EACKhG,WAAW9E,EAAOyB,GAAc1B,cAAc,iBAC9CoI,OAAO,YAAapB,EAAQ,qBAAuB,oBAAqB,mBAC7EkD,EAAgB5E,aAAayF,EACjC,CACJ,IAGAzD,EAAW,CACX,MAAM0D,GAAiB9G,EAAAA,EAAAA,KACjB+G,EAAmB3D,EAAUtH,cAAc,wBAC3CkL,EAAoB5D,EAAU1G,iBAAiB,qCAC/CuK,EAAmB7D,EAAU1G,iBAAiB,qEASpD,GARKqK,GAAiD,IAA7BC,EAAkBhD,QAA4C,IAA5BiD,EAAiBjD,QAIxE8C,EAAejG,WAAWkG,GAC1BD,EAAejG,WAAWoG,IAJ1BH,EAAejG,WAAWuC,EAAUtH,cAAc,4DAMtDwB,EAAc8D,aAAa0F,GACvBtK,EAAe,CAEfsK,EACK7C,kBAAkB,CAACvB,IACnBwB,OAAO,YAAa,cAActB,KAAWE,EAAQ,oBAAsB,oBAChF,MAAMoE,GAAcC,EAAAA,EAAAA,GAAkB/D,GACtC9F,EAAc4D,cAAc,KACa,WAAjC5D,EAAc8J,gBACdF,EAAYvG,MAAMC,YAAY,UAAW,SAGrD,MAGIkG,EACK5C,OAAO,YAAa,cAActB,KAAW,cAAcM,MAC3DgB,OAAOxB,EAAS,EAAGG,GAE5B,GAAIkE,EAAkB,CAClB,MAAMM,EAA4BtL,EAAOgL,GAAkBjL,cAAc,sBACzE,GAAIuL,EAA2B,CAC3B,MAAMC,EAA2BD,EAA0BvL,cAAc,qBACnEyL,EAA4BF,EAA0BvL,cAAc,sBACpE0L,GAA0BxH,EAAAA,EAAAA,KAC1ByH,GAAyBzH,EAAAA,EAAAA,KACzB0H,GAA0B1H,EAAAA,EAAAA,KAChCwH,EACK3G,WAAWwG,GACXvG,aAAa,CAAEjB,QAAS,IAAKwC,QAAS,UACtCoC,YAAY,CAAE5E,QAAS,GAAIwC,QAAS,KACzCoF,EACK5G,WAAWyG,GACXrD,kBAAkB,CAACvB,IACnBwB,OAAOxB,EAAS,GAAK,GAC1BgF,EACK7G,WAAW0G,GACXtD,kBAAkB,CAACvB,IACnBwB,OAAOxB,EAAS,GAAK,KAC1B8E,EAAwBpG,aAAa,CAACqG,EAAwBC,IAC9DZ,EAAe1F,aAAa,CAACoG,GACjC,CACJ,CACAR,EAAkBlB,QAAS6B,IACvB,MAAMC,GAAiB5H,EAAAA,EAAAA,KACvB4H,EAAe/G,WAAW8G,GAC1B,MAAME,GAAe7H,EAAAA,EAAAA,KACrB6H,EAAahH,WAAW8G,EAAiB7L,cAAc,cACvD,MAAMgM,GAAwB9H,EAAAA,EAAAA,KACxBrD,EAAUgL,EAAiBjL,iBAAiB,4BAC5CE,EAAe+K,EAAiB9K,QAAQ,cACxCwJ,EAAkC,OAAjBzJ,QAA0C,IAAjBA,OAA0B,EAASA,EAAaG,UAAUC,SAAS,qCAC7GsJ,EAAmBH,MAAMC,KAAKzJ,GAAS4J,OAAQC,IACjD,MAAMC,EAAmBD,EAAOzJ,UAAUC,SAAS,oBACnD,OAAQyJ,IAAqBJ,IAAoBI,IAErDqB,EAAsBjH,WAAWyF,GACjC,MAAMyB,GAAsB/H,EAAAA,EAAAA,KACtBgI,EAAwBL,EAAiBjL,iBAAiB,gEAC5DsL,EAAsBhE,OAAS,GAC/B+D,EAAoBlH,WAAWmH,GAEnC,MAAMC,GAAmBjI,EAAAA,EAAAA,KACzBiI,EAAiBpH,WAAW9E,EAAO4L,GAAkB7L,cAAc,wBACnE,MAAMmJ,GAAoBjF,EAAAA,EAAAA,KACpBxC,EAAemK,EAAiB7L,cAAc,mBAgBpD,GAfI0B,GACAyH,EAAkBpE,WAAWrD,GAEjCoK,EAAexG,aAAa,CACxByG,EACAC,EACAC,EACA9C,EACAgD,IAEJ3K,EAAc8D,aAAawG,GAE3B3C,EAAkBf,OAAOxB,EAAS,IAAM,GACxCoF,EAAsB5D,OAAOxB,EAAS,IAAM,GAC5CqF,EAAoB7D,OAAOxB,EAAS,IAAM,GACtClG,EAAe,CACV6J,GAEDwB,EACK3D,OAAO,YAAa,cAActB,KAAWE,EAAQ,oBAAsB,oBAC3EoB,OAAOxB,EAAS,IAAM,GAE/BqF,EAAoB7D,OAAO,YAAa,cAActB,KAAWE,EAAQ,oBAAsB,oBAC/FmF,EAAiBhE,kBAAkB,CAACvB,EAAS,cAU7C,IAP2C,OAAjB9F,QAA0C,IAAjBA,OAA0B,EAASA,EAAagK,aAK/FqB,EAAiB/D,OAAO,YAAa,kBAAmBpB,EAAQ,oBAAsB,oBAHtFmF,EAAiB/D,OAAOxB,EAAS,iBAAkB,GAKnDlF,IAAiBoH,EAAU,CAC3B,MAAMsD,GAAqBlI,EAAAA,EAAAA,KAC3BkI,EACKrH,WAAW9E,EAAOyB,GAAc1B,cAAc,iBAC9CoI,OAAO,YAAa,cAActB,KAAW,eAAeE,GAAS,IAAM,KAAO,SACvF8E,EAAexG,aAAa8G,EAChC,CACJ,MAGS7B,GACDwB,EACK3D,OAAO,YAAa,cAActB,KAAW,cAAcM,MAC3DgB,OAAOxB,EAAS,IAAM,GACtByF,iBAAiB,CAACxF,EAAWD,IAEtCqF,EACK7D,OAAO,YAAa,cAActB,KAAW,cAAcM,MAC3DiF,iBAAiB,CAACxF,EAAWD,IAClCuC,EAAkBkD,iBAAiB,CAACzF,IACpCmF,EAAaM,iBAAiB,CAACzF,IAC/BoF,EAAsBK,iBAAiB,CAACzF,KAGpD,CACA,OAAOpF,CACX,CACA,MAAO8K,GACH,MAAMA,CACV,GAWEvJ,EAA0B,E", "sources": ["../node_modules/@ionic/core/components/ios.transition.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation.js';\nimport { g as getIonPageElement } from './index2.js';\n\nconst DURATION = 540;\n// TODO(FW-2832): types\nconst getClonedElement = (tagName) => {\n    return document.querySelector(`${tagName}.ion-cloned-element`);\n};\nconst shadow = (el) => {\n    return el.shadowRoot || el;\n};\nconst getLargeTitle = (refEl) => {\n    const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n    const query = 'ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large';\n    if (tabs != null) {\n        const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n        return activeTab != null ? activeTab.querySelector(query) : null;\n    }\n    return refEl.querySelector(query);\n};\nconst getBackButton = (refEl, backDirection) => {\n    const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n    let buttonsList = [];\n    if (tabs != null) {\n        const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n        if (activeTab != null) {\n            buttonsList = activeTab.querySelectorAll('ion-buttons');\n        }\n    }\n    else {\n        buttonsList = refEl.querySelectorAll('ion-buttons');\n    }\n    for (const buttons of buttonsList) {\n        const parentHeader = buttons.closest('ion-header');\n        const activeHeader = parentHeader && !parentHeader.classList.contains('header-collapse-condense-inactive');\n        const backButton = buttons.querySelector('ion-back-button');\n        const buttonsCollapse = buttons.classList.contains('buttons-collapse');\n        const startSlot = buttons.slot === 'start' || buttons.slot === '';\n        if (backButton !== null && startSlot && ((buttonsCollapse && activeHeader && backDirection) || !buttonsCollapse)) {\n            return backButton;\n        }\n    }\n    return null;\n};\nconst createLargeTitleTransition = (rootAnimation, rtl, backDirection, enteringEl, leavingEl) => {\n    const enteringBackButton = getBackButton(enteringEl, backDirection);\n    const leavingLargeTitle = getLargeTitle(leavingEl);\n    const enteringLargeTitle = getLargeTitle(enteringEl);\n    const leavingBackButton = getBackButton(leavingEl, backDirection);\n    const shouldAnimationForward = enteringBackButton !== null && leavingLargeTitle !== null && !backDirection;\n    const shouldAnimationBackward = enteringLargeTitle !== null && leavingBackButton !== null && backDirection;\n    if (shouldAnimationForward) {\n        const leavingLargeTitleBox = leavingLargeTitle.getBoundingClientRect();\n        const enteringBackButtonBox = enteringBackButton.getBoundingClientRect();\n        const enteringBackButtonTextEl = shadow(enteringBackButton).querySelector('.button-text');\n        const enteringBackButtonTextBox = enteringBackButtonTextEl.getBoundingClientRect();\n        const leavingLargeTitleTextEl = shadow(leavingLargeTitle).querySelector('.toolbar-title');\n        const leavingLargeTitleTextBox = leavingLargeTitleTextEl.getBoundingClientRect();\n        animateLargeTitle(rootAnimation, rtl, backDirection, leavingLargeTitle, leavingLargeTitleBox, leavingLargeTitleTextBox, enteringBackButtonTextEl, enteringBackButtonTextBox);\n        animateBackButton(rootAnimation, rtl, backDirection, enteringBackButton, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox, leavingLargeTitle, leavingLargeTitleTextBox);\n    }\n    else if (shouldAnimationBackward) {\n        const enteringLargeTitleBox = enteringLargeTitle.getBoundingClientRect();\n        const leavingBackButtonBox = leavingBackButton.getBoundingClientRect();\n        const leavingBackButtonTextEl = shadow(leavingBackButton).querySelector('.button-text');\n        const leavingBackButtonTextBox = leavingBackButtonTextEl.getBoundingClientRect();\n        const enteringLargeTitleTextEl = shadow(enteringLargeTitle).querySelector('.toolbar-title');\n        const enteringLargeTitleTextBox = enteringLargeTitleTextEl.getBoundingClientRect();\n        animateLargeTitle(rootAnimation, rtl, backDirection, enteringLargeTitle, enteringLargeTitleBox, enteringLargeTitleTextBox, leavingBackButtonTextEl, leavingBackButtonTextBox);\n        animateBackButton(rootAnimation, rtl, backDirection, leavingBackButton, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox, enteringLargeTitle, enteringLargeTitleTextBox);\n    }\n    return {\n        forward: shouldAnimationForward,\n        backward: shouldAnimationBackward,\n    };\n};\nconst animateBackButton = (rootAnimation, rtl, backDirection, backButtonEl, backButtonBox, backButtonTextEl, backButtonTextBox, largeTitleEl, largeTitleTextBox) => {\n    var _a, _b;\n    const BACK_BUTTON_START_OFFSET = rtl ? `calc(100% - ${backButtonBox.right + 4}px)` : `${backButtonBox.left - 4}px`;\n    const TEXT_ORIGIN_X = rtl ? 'right' : 'left';\n    const ICON_ORIGIN_X = rtl ? 'left' : 'right';\n    const CONTAINER_ORIGIN_X = rtl ? 'right' : 'left';\n    /**\n     * When the title and back button texts match\n     * then they should overlap during the page transition.\n     * If the texts do not match up then the back button text scale adjusts\n     * to not perfectly match the large title text otherwise the\n     * proportions will be incorrect.\n     * When the texts match we scale both the width and height to account for\n     * font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    const WIDTH_SCALE = largeTitleTextBox.width / backButtonTextBox.width;\n    /**\n     * We subtract an offset to account for slight sizing/padding\n     * differences between the title and the back button.\n     */\n    const HEIGHT_SCALE = (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET) / backButtonTextBox.height;\n    const TEXT_START_SCALE = doTitleAndButtonTextsMatch\n        ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})`\n        : `scale(${HEIGHT_SCALE})`;\n    const TEXT_END_SCALE = 'scale(1)';\n    const backButtonIconEl = shadow(backButtonEl).querySelector('ion-icon');\n    const backButtonIconBox = backButtonIconEl.getBoundingClientRect();\n    /**\n     * We need to offset the container by the icon dimensions\n     * so that the back button text aligns with the large title\n     * text. Otherwise, the back button icon will align with the\n     * large title text but the back button text will not.\n     */\n    const CONTAINER_START_TRANSLATE_X = rtl\n        ? `${backButtonIconBox.width / 2 - (backButtonIconBox.right - backButtonBox.right)}px`\n        : `${backButtonBox.left - backButtonIconBox.width / 2}px`;\n    const CONTAINER_END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right}px` : `${backButtonBox.left}px`;\n    /**\n     * Back button container should be\n     * aligned to the top of the title container\n     * so the texts overlap as the back button\n     * text begins to fade in.\n     */\n    const CONTAINER_START_TRANSLATE_Y = `${largeTitleTextBox.top}px`;\n    /**\n     * The cloned back button should align exactly with the\n     * real back button on the entering page otherwise there will\n     * be a layout shift.\n     */\n    const CONTAINER_END_TRANSLATE_Y = `${backButtonBox.top}px`;\n    /**\n     * In the forward direction, the cloned back button\n     * container should translate from over the large title\n     * to over the back button. In the backward direction,\n     * it should translate from over the back button to over\n     * the large title.\n     */\n    const FORWARD_CONTAINER_KEYFRAMES = [\n        { offset: 0, transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)` },\n        { offset: 1, transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)` },\n    ];\n    const BACKWARD_CONTAINER_KEYFRAMES = [\n        { offset: 0, transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)` },\n        { offset: 1, transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)` },\n    ];\n    const CONTAINER_KEYFRAMES = backDirection ? BACKWARD_CONTAINER_KEYFRAMES : FORWARD_CONTAINER_KEYFRAMES;\n    /**\n     * In the forward direction, the text in the cloned back button\n     * should start to be (roughly) the size of the large title\n     * and then scale down to be the size of the actual back button.\n     * The text should also translate, but that translate is handled\n     * by the container keyframes.\n     */\n    const FORWARD_TEXT_KEYFRAMES = [\n        { offset: 0, opacity: 0, transform: TEXT_START_SCALE },\n        { offset: 1, opacity: 1, transform: TEXT_END_SCALE },\n    ];\n    const BACKWARD_TEXT_KEYFRAMES = [\n        { offset: 0, opacity: 1, transform: TEXT_END_SCALE },\n        { offset: 1, opacity: 0, transform: TEXT_START_SCALE },\n    ];\n    const TEXT_KEYFRAMES = backDirection ? BACKWARD_TEXT_KEYFRAMES : FORWARD_TEXT_KEYFRAMES;\n    /**\n     * The icon should scale in/out in the second\n     * half of the animation. The icon should also\n     * translate, but that translate is handled by the\n     * container keyframes.\n     */\n    const FORWARD_ICON_KEYFRAMES = [\n        { offset: 0, opacity: 0, transform: 'scale(0.6)' },\n        { offset: 0.6, opacity: 0, transform: 'scale(0.6)' },\n        { offset: 1, opacity: 1, transform: 'scale(1)' },\n    ];\n    const BACKWARD_ICON_KEYFRAMES = [\n        { offset: 0, opacity: 1, transform: 'scale(1)' },\n        { offset: 0.2, opacity: 0, transform: 'scale(0.6)' },\n        { offset: 1, opacity: 0, transform: 'scale(0.6)' },\n    ];\n    const ICON_KEYFRAMES = backDirection ? BACKWARD_ICON_KEYFRAMES : FORWARD_ICON_KEYFRAMES;\n    const enteringBackButtonTextAnimation = createAnimation();\n    const enteringBackButtonIconAnimation = createAnimation();\n    const enteringBackButtonAnimation = createAnimation();\n    const clonedBackButtonEl = getClonedElement('ion-back-button');\n    const clonedBackButtonTextEl = shadow(clonedBackButtonEl).querySelector('.button-text');\n    const clonedBackButtonIconEl = shadow(clonedBackButtonEl).querySelector('ion-icon');\n    clonedBackButtonEl.text = backButtonEl.text;\n    clonedBackButtonEl.mode = backButtonEl.mode;\n    clonedBackButtonEl.icon = backButtonEl.icon;\n    clonedBackButtonEl.color = backButtonEl.color;\n    clonedBackButtonEl.disabled = backButtonEl.disabled;\n    clonedBackButtonEl.style.setProperty('display', 'block');\n    clonedBackButtonEl.style.setProperty('position', 'fixed');\n    enteringBackButtonIconAnimation.addElement(clonedBackButtonIconEl);\n    enteringBackButtonTextAnimation.addElement(clonedBackButtonTextEl);\n    enteringBackButtonAnimation.addElement(clonedBackButtonEl);\n    enteringBackButtonAnimation\n        .beforeStyles({\n        position: 'absolute',\n        top: '0px',\n        [CONTAINER_ORIGIN_X]: '0px',\n    })\n        .keyframes(CONTAINER_KEYFRAMES);\n    enteringBackButtonTextAnimation\n        .beforeStyles({\n        'transform-origin': `${TEXT_ORIGIN_X} top`,\n    })\n        .beforeAddWrite(() => {\n        backButtonEl.style.setProperty('display', 'none');\n        clonedBackButtonEl.style.setProperty(TEXT_ORIGIN_X, BACK_BUTTON_START_OFFSET);\n    })\n        .afterAddWrite(() => {\n        backButtonEl.style.setProperty('display', '');\n        clonedBackButtonEl.style.setProperty('display', 'none');\n        clonedBackButtonEl.style.removeProperty(TEXT_ORIGIN_X);\n    })\n        .keyframes(TEXT_KEYFRAMES);\n    enteringBackButtonIconAnimation\n        .beforeStyles({\n        'transform-origin': `${ICON_ORIGIN_X} center`,\n    })\n        .keyframes(ICON_KEYFRAMES);\n    rootAnimation.addAnimation([\n        enteringBackButtonTextAnimation,\n        enteringBackButtonIconAnimation,\n        enteringBackButtonAnimation,\n    ]);\n};\nconst animateLargeTitle = (rootAnimation, rtl, backDirection, largeTitleEl, largeTitleBox, largeTitleTextBox, backButtonTextEl, backButtonTextBox) => {\n    var _a, _b;\n    /**\n     * The horizontal transform origin for the large title\n     */\n    const ORIGIN_X = rtl ? 'right' : 'left';\n    const TITLE_START_OFFSET = rtl ? `calc(100% - ${largeTitleBox.right}px)` : `${largeTitleBox.left}px`;\n    /**\n     * The cloned large should align exactly with the\n     * real large title on the leaving page otherwise there will\n     * be a layout shift.\n     */\n    const START_TRANSLATE_X = '0px';\n    const START_TRANSLATE_Y = `${largeTitleBox.top}px`;\n    /**\n     * How much to offset the large title translation by.\n     * This accounts for differences in sizing between the large\n     * title and the back button due to padding and font weight.\n     */\n    const LARGE_TITLE_TRANSLATION_OFFSET = 8;\n    /**\n     * The scaled title should (roughly) overlap the back button.\n     * This ensures that the back button and title overlap during\n     * the animation. Note that since both elements either fade in\n     * or fade out over the course of the animation, neither element\n     * will be fully visible on top of the other. As a result, the overlap\n     * does not need to be perfect, so approximate values are acceptable here.\n     */\n    const END_TRANSLATE_X = rtl\n        ? `-${window.innerWidth - backButtonTextBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px`\n        : `${backButtonTextBox.x - LARGE_TITLE_TRANSLATION_OFFSET}px`;\n    /**\n     * The top of the scaled large title\n     * should match with the top of the\n     * back button text element.\n     * We subtract 2px to account for the top padding\n     * on the large title element.\n     */\n    const LARGE_TITLE_TOP_PADDING = 2;\n    const END_TRANSLATE_Y = `${backButtonTextBox.y - LARGE_TITLE_TOP_PADDING}px`;\n    /**\n     * In the forward direction, the large title should start at its\n     * normal size and then scale down to be (roughly) the size of the\n     * back button on the other view. In the backward direction, the\n     * large title should start at (roughly) the size of the back button\n     * and then scale up to its original size.\n     *\n     * Note that since both elements either fade in\n     * or fade out over the course of the animation, neither element\n     * will be fully visible on top of the other. As a result, the overlap\n     * does not need to be perfect, so approximate values are acceptable here.\n     */\n    /**\n     * When the title and back button texts match\n     * then they should overlap during the page transition.\n     * If the texts do not match up then the large title text scale adjusts\n     * to not perfectly match the back button text otherwise the\n     * proportions will be incorrect.\n     * When the texts match we scale both the width and height to account for\n     * font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    const WIDTH_SCALE = backButtonTextBox.width / largeTitleTextBox.width;\n    const HEIGHT_SCALE = backButtonTextBox.height / (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET);\n    const START_SCALE = 'scale(1)';\n    const END_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n    const BACKWARDS_KEYFRAMES = [\n        { offset: 0, opacity: 0, transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}` },\n        { offset: 0.1, opacity: 0 },\n        { offset: 1, opacity: 1, transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}` },\n    ];\n    const FORWARDS_KEYFRAMES = [\n        {\n            offset: 0,\n            opacity: 0.99,\n            transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`,\n        },\n        { offset: 0.6, opacity: 0 },\n        { offset: 1, opacity: 0, transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}` },\n    ];\n    const KEYFRAMES = backDirection ? BACKWARDS_KEYFRAMES : FORWARDS_KEYFRAMES;\n    const clonedTitleEl = getClonedElement('ion-title');\n    const clonedLargeTitleAnimation = createAnimation();\n    clonedTitleEl.innerText = largeTitleEl.innerText;\n    clonedTitleEl.size = largeTitleEl.size;\n    clonedTitleEl.color = largeTitleEl.color;\n    clonedLargeTitleAnimation.addElement(clonedTitleEl);\n    clonedLargeTitleAnimation\n        .beforeStyles({\n        'transform-origin': `${ORIGIN_X} top`,\n        /**\n         * Since font size changes will cause\n         * the dimension of the large title to change\n         * we need to set the cloned title height\n         * equal to that of the original large title height.\n         */\n        height: `${largeTitleBox.height}px`,\n        display: '',\n        position: 'relative',\n        [ORIGIN_X]: TITLE_START_OFFSET,\n    })\n        .beforeAddWrite(() => {\n        largeTitleEl.style.setProperty('opacity', '0');\n    })\n        .afterAddWrite(() => {\n        largeTitleEl.style.setProperty('opacity', '');\n        clonedTitleEl.style.setProperty('display', 'none');\n    })\n        .keyframes(KEYFRAMES);\n    rootAnimation.addAnimation(clonedLargeTitleAnimation);\n};\nconst iosTransitionAnimation = (navEl, opts) => {\n    var _a;\n    try {\n        const EASING = 'cubic-bezier(0.32,0.72,0,1)';\n        const OPACITY = 'opacity';\n        const TRANSFORM = 'transform';\n        const CENTER = '0%';\n        const OFF_OPACITY = 0.8;\n        const isRTL = navEl.ownerDocument.dir === 'rtl';\n        const OFF_RIGHT = isRTL ? '-99.5%' : '99.5%';\n        const OFF_LEFT = isRTL ? '33%' : '-33%';\n        const enteringEl = opts.enteringEl;\n        const leavingEl = opts.leavingEl;\n        const backDirection = opts.direction === 'back';\n        const contentEl = enteringEl.querySelector(':scope > ion-content');\n        const headerEls = enteringEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n        const enteringToolBarEls = enteringEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n        const rootAnimation = createAnimation();\n        const enteringContentAnimation = createAnimation();\n        rootAnimation\n            .addElement(enteringEl)\n            .duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || DURATION)\n            .easing(opts.easing || EASING)\n            .fill('both')\n            .beforeRemoveClass('ion-page-invisible');\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        if (leavingEl && navEl !== null && navEl !== undefined) {\n            const navDecorAnimation = createAnimation();\n            navDecorAnimation.addElement(navEl);\n            rootAnimation.addAnimation(navDecorAnimation);\n        }\n        if (!contentEl && enteringToolBarEls.length === 0 && headerEls.length === 0) {\n            enteringContentAnimation.addElement(enteringEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n        }\n        else {\n            enteringContentAnimation.addElement(contentEl); // REVIEW\n            enteringContentAnimation.addElement(headerEls);\n        }\n        rootAnimation.addAnimation(enteringContentAnimation);\n        if (backDirection) {\n            enteringContentAnimation\n                .beforeClearStyles([OPACITY])\n                .fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`)\n                .fromTo(OPACITY, OFF_OPACITY, 1);\n        }\n        else {\n            // entering content, forward direction\n            enteringContentAnimation\n                .beforeClearStyles([OPACITY])\n                .fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n        }\n        if (contentEl) {\n            const enteringTransitionEffectEl = shadow(contentEl).querySelector('.transition-effect');\n            if (enteringTransitionEffectEl) {\n                const enteringTransitionCoverEl = enteringTransitionEffectEl.querySelector('.transition-cover');\n                const enteringTransitionShadowEl = enteringTransitionEffectEl.querySelector('.transition-shadow');\n                const enteringTransitionEffect = createAnimation();\n                const enteringTransitionCover = createAnimation();\n                const enteringTransitionShadow = createAnimation();\n                enteringTransitionEffect\n                    .addElement(enteringTransitionEffectEl)\n                    .beforeStyles({ opacity: '1', display: 'block' })\n                    .afterStyles({ opacity: '', display: '' });\n                enteringTransitionCover\n                    .addElement(enteringTransitionCoverEl) // REVIEW\n                    .beforeClearStyles([OPACITY])\n                    .fromTo(OPACITY, 0, 0.1);\n                enteringTransitionShadow\n                    .addElement(enteringTransitionShadowEl) // REVIEW\n                    .beforeClearStyles([OPACITY])\n                    .fromTo(OPACITY, 0.03, 0.7);\n                enteringTransitionEffect.addAnimation([enteringTransitionCover, enteringTransitionShadow]);\n                enteringContentAnimation.addAnimation([enteringTransitionEffect]);\n            }\n        }\n        const enteringContentHasLargeTitle = enteringEl.querySelector('ion-header.header-collapse-condense');\n        const { forward, backward } = createLargeTitleTransition(rootAnimation, isRTL, backDirection, enteringEl, leavingEl);\n        enteringToolBarEls.forEach((enteringToolBarEl) => {\n            const enteringToolBar = createAnimation();\n            enteringToolBar.addElement(enteringToolBarEl);\n            rootAnimation.addAnimation(enteringToolBar);\n            const enteringTitle = createAnimation();\n            enteringTitle.addElement(enteringToolBarEl.querySelector('ion-title')); // REVIEW\n            const enteringToolBarButtons = createAnimation();\n            const buttons = Array.from(enteringToolBarEl.querySelectorAll('ion-buttons,[menuToggle]'));\n            const parentHeader = enteringToolBarEl.closest('ion-header');\n            const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n            let buttonsToAnimate;\n            if (backDirection) {\n                buttonsToAnimate = buttons.filter((button) => {\n                    const isCollapseButton = button.classList.contains('buttons-collapse');\n                    return (isCollapseButton && !inactiveHeader) || !isCollapseButton;\n                });\n            }\n            else {\n                buttonsToAnimate = buttons.filter((button) => !button.classList.contains('buttons-collapse'));\n            }\n            enteringToolBarButtons.addElement(buttonsToAnimate);\n            const enteringToolBarItems = createAnimation();\n            enteringToolBarItems.addElement(enteringToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])'));\n            const enteringToolBarBg = createAnimation();\n            enteringToolBarBg.addElement(shadow(enteringToolBarEl).querySelector('.toolbar-background')); // REVIEW\n            const enteringBackButton = createAnimation();\n            const backButtonEl = enteringToolBarEl.querySelector('ion-back-button');\n            if (backButtonEl) {\n                enteringBackButton.addElement(backButtonEl);\n            }\n            enteringToolBar.addAnimation([\n                enteringTitle,\n                enteringToolBarButtons,\n                enteringToolBarItems,\n                enteringToolBarBg,\n                enteringBackButton,\n            ]);\n            enteringToolBarButtons.fromTo(OPACITY, 0.01, 1);\n            enteringToolBarItems.fromTo(OPACITY, 0.01, 1);\n            if (backDirection) {\n                if (!inactiveHeader) {\n                    enteringTitle\n                        .fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`)\n                        .fromTo(OPACITY, 0.01, 1);\n                }\n                enteringToolBarItems.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`);\n                // back direction, entering page has a back button\n                enteringBackButton.fromTo(OPACITY, 0.01, 1);\n            }\n            else {\n                // entering toolbar, forward direction\n                if (!enteringContentHasLargeTitle) {\n                    enteringTitle\n                        .fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`)\n                        .fromTo(OPACITY, 0.01, 1);\n                }\n                enteringToolBarItems.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n                enteringToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n                const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n                if (!translucentHeader) {\n                    enteringToolBarBg.fromTo(OPACITY, 0.01, 'var(--opacity)');\n                }\n                else {\n                    enteringToolBarBg.fromTo('transform', isRTL ? 'translateX(-100%)' : 'translateX(100%)', 'translateX(0px)');\n                }\n                // forward direction, entering page has a back button\n                if (!forward) {\n                    enteringBackButton.fromTo(OPACITY, 0.01, 1);\n                }\n                if (backButtonEl && !forward) {\n                    const enteringBackBtnText = createAnimation();\n                    enteringBackBtnText\n                        .addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n                        .fromTo(`transform`, isRTL ? 'translateX(-100px)' : 'translateX(100px)', 'translateX(0px)');\n                    enteringToolBar.addAnimation(enteringBackBtnText);\n                }\n            }\n        });\n        // setup leaving view\n        if (leavingEl) {\n            const leavingContent = createAnimation();\n            const leavingContentEl = leavingEl.querySelector(':scope > ion-content');\n            const leavingToolBarEls = leavingEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n            const leavingHeaderEls = leavingEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n            if (!leavingContentEl && leavingToolBarEls.length === 0 && leavingHeaderEls.length === 0) {\n                leavingContent.addElement(leavingEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n            }\n            else {\n                leavingContent.addElement(leavingContentEl); // REVIEW\n                leavingContent.addElement(leavingHeaderEls);\n            }\n            rootAnimation.addAnimation(leavingContent);\n            if (backDirection) {\n                // leaving content, back direction\n                leavingContent\n                    .beforeClearStyles([OPACITY])\n                    .fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n                const leavingPage = getIonPageElement(leavingEl);\n                rootAnimation.afterAddWrite(() => {\n                    if (rootAnimation.getDirection() === 'normal') {\n                        leavingPage.style.setProperty('display', 'none');\n                    }\n                });\n            }\n            else {\n                // leaving content, forward direction\n                leavingContent\n                    .fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`)\n                    .fromTo(OPACITY, 1, OFF_OPACITY);\n            }\n            if (leavingContentEl) {\n                const leavingTransitionEffectEl = shadow(leavingContentEl).querySelector('.transition-effect');\n                if (leavingTransitionEffectEl) {\n                    const leavingTransitionCoverEl = leavingTransitionEffectEl.querySelector('.transition-cover');\n                    const leavingTransitionShadowEl = leavingTransitionEffectEl.querySelector('.transition-shadow');\n                    const leavingTransitionEffect = createAnimation();\n                    const leavingTransitionCover = createAnimation();\n                    const leavingTransitionShadow = createAnimation();\n                    leavingTransitionEffect\n                        .addElement(leavingTransitionEffectEl)\n                        .beforeStyles({ opacity: '1', display: 'block' })\n                        .afterStyles({ opacity: '', display: '' });\n                    leavingTransitionCover\n                        .addElement(leavingTransitionCoverEl) // REVIEW\n                        .beforeClearStyles([OPACITY])\n                        .fromTo(OPACITY, 0.1, 0);\n                    leavingTransitionShadow\n                        .addElement(leavingTransitionShadowEl) // REVIEW\n                        .beforeClearStyles([OPACITY])\n                        .fromTo(OPACITY, 0.7, 0.03);\n                    leavingTransitionEffect.addAnimation([leavingTransitionCover, leavingTransitionShadow]);\n                    leavingContent.addAnimation([leavingTransitionEffect]);\n                }\n            }\n            leavingToolBarEls.forEach((leavingToolBarEl) => {\n                const leavingToolBar = createAnimation();\n                leavingToolBar.addElement(leavingToolBarEl);\n                const leavingTitle = createAnimation();\n                leavingTitle.addElement(leavingToolBarEl.querySelector('ion-title')); // REVIEW\n                const leavingToolBarButtons = createAnimation();\n                const buttons = leavingToolBarEl.querySelectorAll('ion-buttons,[menuToggle]');\n                const parentHeader = leavingToolBarEl.closest('ion-header');\n                const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n                const buttonsToAnimate = Array.from(buttons).filter((button) => {\n                    const isCollapseButton = button.classList.contains('buttons-collapse');\n                    return (isCollapseButton && !inactiveHeader) || !isCollapseButton;\n                });\n                leavingToolBarButtons.addElement(buttonsToAnimate);\n                const leavingToolBarItems = createAnimation();\n                const leavingToolBarItemEls = leavingToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])');\n                if (leavingToolBarItemEls.length > 0) {\n                    leavingToolBarItems.addElement(leavingToolBarItemEls);\n                }\n                const leavingToolBarBg = createAnimation();\n                leavingToolBarBg.addElement(shadow(leavingToolBarEl).querySelector('.toolbar-background')); // REVIEW\n                const leavingBackButton = createAnimation();\n                const backButtonEl = leavingToolBarEl.querySelector('ion-back-button');\n                if (backButtonEl) {\n                    leavingBackButton.addElement(backButtonEl);\n                }\n                leavingToolBar.addAnimation([\n                    leavingTitle,\n                    leavingToolBarButtons,\n                    leavingToolBarItems,\n                    leavingBackButton,\n                    leavingToolBarBg,\n                ]);\n                rootAnimation.addAnimation(leavingToolBar);\n                // fade out leaving toolbar items\n                leavingBackButton.fromTo(OPACITY, 0.99, 0);\n                leavingToolBarButtons.fromTo(OPACITY, 0.99, 0);\n                leavingToolBarItems.fromTo(OPACITY, 0.99, 0);\n                if (backDirection) {\n                    if (!inactiveHeader) {\n                        // leaving toolbar, back direction\n                        leavingTitle\n                            .fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)')\n                            .fromTo(OPACITY, 0.99, 0);\n                    }\n                    leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n                    leavingToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n                    // leaving toolbar, back direction, and there's no entering toolbar\n                    // should just slide out, no fading out\n                    const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n                    if (!translucentHeader) {\n                        leavingToolBarBg.fromTo(OPACITY, 'var(--opacity)', 0);\n                    }\n                    else {\n                        leavingToolBarBg.fromTo('transform', 'translateX(0px)', isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n                    }\n                    if (backButtonEl && !backward) {\n                        const leavingBackBtnText = createAnimation();\n                        leavingBackBtnText\n                            .addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n                            .fromTo('transform', `translateX(${CENTER})`, `translateX(${(isRTL ? -124 : 124) + 'px'})`);\n                        leavingToolBar.addAnimation(leavingBackBtnText);\n                    }\n                }\n                else {\n                    // leaving toolbar, forward direction\n                    if (!inactiveHeader) {\n                        leavingTitle\n                            .fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`)\n                            .fromTo(OPACITY, 0.99, 0)\n                            .afterClearStyles([TRANSFORM, OPACITY]);\n                    }\n                    leavingToolBarItems\n                        .fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`)\n                        .afterClearStyles([TRANSFORM, OPACITY]);\n                    leavingBackButton.afterClearStyles([OPACITY]);\n                    leavingTitle.afterClearStyles([OPACITY]);\n                    leavingToolBarButtons.afterClearStyles([OPACITY]);\n                }\n            });\n        }\n        return rootAnimation;\n    }\n    catch (err) {\n        throw err;\n    }\n};\n/**\n * The scale of the back button during the animation\n * is computed based on the scale of the large title\n * and vice versa. However, we need to account for slight\n * variations in the size of the large title due to\n * padding and font weight. This value should be used to subtract\n * a small amount from the large title height when computing scales\n * to get more accurate scale results.\n */\nconst LARGE_TITLE_SIZE_OFFSET = 10;\n\nexport { iosTransitionAnimation, shadow };\n"], "names": ["getClonedElement", "tagName", "document", "querySelector", "shadow", "el", "shadowRoot", "getLargeTitle", "refEl", "tabs", "query", "activeTab", "getBackButton", "backDirection", "buttonsList", "querySelectorAll", "buttons", "parentHeader", "closest", "activeHeader", "classList", "contains", "backButton", "buttonsCollapse", "startSlot", "slot", "animateBackButton", "rootAnimation", "rtl", "backButtonEl", "backButtonBox", "backButtonTextEl", "backButtonTextBox", "largeTitleEl", "largeTitleTextBox", "_a", "_b", "BACK_BUTTON_START_OFFSET", "right", "left", "TEXT_ORIGIN_X", "ICON_ORIGIN_X", "CONTAINER_ORIGIN_X", "doTitleAndButtonTextsMatch", "textContent", "trim", "WIDTH_SCALE", "width", "HEIGHT_SCALE", "height", "LARGE_TITLE_SIZE_OFFSET", "TEXT_START_SCALE", "TEXT_END_SCALE", "backButtonIconBox", "getBoundingClientRect", "CONTAINER_START_TRANSLATE_X", "CONTAINER_END_TRANSLATE_X", "window", "innerWidth", "CONTAINER_START_TRANSLATE_Y", "top", "CONTAINER_END_TRANSLATE_Y", "CONTAINER_KEYFRAMES", "offset", "transform", "TEXT_KEYFRAMES", "opacity", "ICON_KEYFRAMES", "enteringBackButtonTextAnimation", "createAnimation", "enteringBackButtonIconAnimation", "enteringBackButtonAnimation", "clonedBackButtonEl", "clonedBackButtonTextEl", "clonedBackButtonIconEl", "text", "mode", "icon", "color", "disabled", "style", "setProperty", "addElement", "beforeStyles", "position", "keyframes", "beforeAddWrite", "afterAddWrite", "removeProperty", "addAnimation", "animate<PERSON>arge<PERSON>it<PERSON>", "largeTitleBox", "ORIGIN_X", "TITLE_START_OFFSET", "START_TRANSLATE_Y", "END_TRANSLATE_X", "x", "END_TRANSLATE_Y", "y", "START_SCALE", "END_SCALE", "KEYFRAMES", "clonedTitleEl", "clonedLargeTitleAnimation", "innerText", "size", "display", "iosTransitionAnimation", "navEl", "opts", "EASING", "OPACITY", "TRANSFORM", "CENTER", "OFF_OPACITY", "isRTL", "ownerDocument", "dir", "OFF_RIGHT", "OFF_LEFT", "enteringEl", "leavingEl", "direction", "contentEl", "headerEls", "enteringToolBarEls", "enteringContentAnimation", "duration", "easing", "fill", "beforeRemoveClass", "undefined", "navDecorAnimation", "length", "beforeClearStyles", "fromTo", "enteringTransitionEffectEl", "enteringTransitionCoverEl", "enteringTransitionShadowEl", "enteringTransitionEffect", "enteringTransitionCover", "enteringTransitionShadow", "afterStyles", "enteringContentHasLargeTitle", "forward", "backward", "createLargeTitleTransition", "enteringBackButton", "leavingLargeTitle", "enteringLargeTitle", "leavingBackButton", "shouldAnimationForward", "shouldAnimationBackward", "leavingLargeTitleBox", "enteringBackButtonBox", "enteringBackButtonTextEl", "enteringBackButtonTextBox", "leavingLargeTitleTextBox", "enteringLargeTitleBox", "leavingBackButtonBox", "leavingBackButtonTextEl", "leavingBackButtonTextBox", "enteringLargeTitleTextBox", "for<PERSON>ach", "enteringToolBarEl", "enteringToolBar", "enteringTitle", "enteringToolBarButtons", "Array", "from", "inactiveH<PERSON>er", "buttonsToAnimate", "filter", "button", "isCollapseButton", "enteringToolBarItems", "enteringToolBarBg", "translucent", "enteringBackBtnText", "leavingContent", "leavingContentEl", "leavingToolBarEls", "leavingHeaderEls", "leavingPage", "getIonPageElement", "getDirection", "leavingTransitionEffectEl", "leavingTransitionCoverEl", "leavingTransitionShadowEl", "leavingTransitionEffect", "leavingTransitionCover", "leavingTransitionShadow", "leavingToolBarEl", "leavingToolBar", "leavingTitle", "leavingToolBarButtons", "leavingToolBarItems", "leavingToolBarItemEls", "leavingToolBarBg", "leavingBackBtnText", "afterClearStyles", "err"], "sourceRoot": ""}